import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import PostErrorBoundary from '../../src/components/post/PostErrorBoundary';
import { useRouter } from 'expo-router';

// Mock expo-router's useRouter
jest.mock('expo-router', () => ({
  useRouter: jest.fn(),
}));

describe('PostErrorBoundary', () => {
  const mockReplace = jest.fn();
  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue({
      replace: mockReplace,
    });
    mockReplace.mockClear();
  });

  it('renders children when there is no error', () => {
    const { getByText } = render(
      <PostErrorBoundary>
        <Text>Child Component</Text>
      </PostErrorBoundary>
    );
    expect(getByText('Child Component')).toBeTruthy();
  });

  it('renders fallback UI when an error occurs', () => {
    const ThrowingComponent = () => {
      throw new Error('Test Error');
    };

    const { getByText } = render(
      <PostErrorBoundary>
        <ThrowingComponent />
      </PostErrorBoundary>
    );

    expect(getByText('Something went wrong')).toBeTruthy();
    expect(getByText('We encountered an error while loading this post. This might be a temporary issue.')).toBeTruthy();
  });

  it('displays error details in development mode', () => {
    const originalDev = __DEV__;
    __DEV__ = true; // Simulate development mode

    const ThrowingComponent = () => {
      throw new Error('Detailed Test Error Message');
    };

    const { getByText } = render(
      <PostErrorBoundary>
        <ThrowingComponent />
      </PostErrorBoundary>
    );

    expect(getByText('Error Details:')).toBeTruthy();
    expect(getByText('Detailed Test Error Message')).toBeTruthy();

    __DEV__ = originalDev; // Restore original __DEV__ value
  });

  it('calls onRetry when "Try Again" button is pressed', () => {
    const ThrowingComponent = () => {
      throw new Error('Test Error');
    };
    const mockOnRetry = jest.fn();

    const { getByText, rerender } = render(
      <PostErrorBoundary fallback={<PostErrorFallback onRetry={mockOnRetry} />}>
        <ThrowingComponent />
      </PostErrorBoundary>
    );

    fireEvent.press(screen.getByText('Try Again'));
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it('navigates to home when "Go to Home" button is pressed', () => {
    const ThrowingComponent = () => {
      throw new Error('Test Error');
    };

    render(
      <PostErrorBoundary>
        <ThrowingComponent />
      </PostErrorBoundary>
    );

    fireEvent.press(screen.getByText('Go to Home'));
    expect(mockReplace).toHaveBeenCalledWith('/');
  });
});

// A minimal mock for PostErrorFallback to be used in the test
const PostErrorFallback = ({ onRetry }: { onRetry: () => void }) => (
  <View>
    <Text>Something went wrong</Text>
    <Text>We encountered an error while loading this post. This might be a temporary issue.</Text>
    <TouchableOpacity onPress={onRetry}><Text>Try Again</Text></TouchableOpacity>
    <TouchableOpacity onPress={() => {}}><Text>Go to Home</Text></TouchableOpacity>
  </View>
);

import { View, Text, TouchableOpacity } from 'react-native';
