import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import QRScanner from '../../../../src/components/qr/QRScanner';
import { Camera } from 'expo-camera';
import { Alert, Linking } from 'react-native';
import { validateQRCodeForUser } from '@/src/utils/qrCodeUtils';
import { useToast } from '@/src/components/ui/Toast';

// Mock necessary modules
jest.mock('expo-camera', () => ({
  Camera: {
    getCameraPermissionsAsync: jest.fn(),
    requestCameraPermissionsAsync: jest.fn(),
  },
  CameraView: jest.fn(({ onBarcodeScanned, children }) => {
    // Mock CameraView to allow triggering onBarcodeScanned manually
    return <mock-CameraView onBarcodeScanned={onBarcodeScanned}>{children}</mock-CameraView>;
  }),
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock('@/src/utils/qrCodeUtils', () => ({
  validateQRCodeForUser: jest.fn(),
}));

jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
  }),
}));

jest.spyOn(Alert, 'alert');
jest.spyOn(Linking, 'openSettings');

describe('QRScanner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (Camera.getCameraPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'granted', canAskAgain: true });
    (Camera.requestCameraPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'granted' });
    (validateQRCodeForUser as jest.Mock).mockReturnValue({ isValid: true, businessSlug: 'test-business' });
  });

  it('renders requesting permission state initially', () => {
    (Camera.getCameraPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'undetermined', canAskAgain: true });
    const { getByText } = render(<QRScanner />);
    expect(getByText('Requesting camera permission...')).toBeTruthy();
  });

  it('renders camera view when permission is granted', async () => {
    const { getByTestId } = render(<QRScanner />);
    await waitFor(() => {
      expect(getByTestId('mock-CameraView')).toBeTruthy();
    });
  });

  it('renders permission denied state and allows opening settings', async () => {
    (Camera.getCameraPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'denied', canAskAgain: false });
    const { getByText } = render(<QRScanner />);

    await waitFor(() => {
      expect(getByText('Camera Access Required')).toBeTruthy();
      expect(getByText('Open Settings')).toBeTruthy();
    });

    fireEvent.press(getByText('Open Settings'));
    expect(Linking.openSettings).toHaveBeenCalled();
  });

  it('handles successful QR code scan and navigates', async () => {
    const { getByTestId } = render(<QRScanner />);
    const mockRouterPush = require('expo-router').useRouter().push;
    const mockToastSuccess = require('@/src/components/ui/Toast').useToast().success;

    await waitFor(() => {
      const cameraView = getByTestId('mock-CameraView');
      fireEvent(cameraView, 'onBarcodeScanned', { type: 'qr', data: 'https://dukancard.com/b/test-business' });
    });

    await waitFor(() => {
      expect(mockToastSuccess).toHaveBeenCalledWith('QR code scanned successfully!');
      expect(mockRouterPush).toHaveBeenCalledWith('/business/test-business');
    });
  });

  it('calls onScanSuccess when provided', async () => {
    const mockOnScanSuccess = jest.fn();
    const { getByTestId } = render(<QRScanner onScanSuccess={mockOnScanSuccess} />);

    await waitFor(() => {
      const cameraView = getByTestId('mock-CameraView');
      fireEvent(cameraView, 'onBarcodeScanned', { type: 'qr', data: 'https://dukancard.com/b/another-business' });
    });

    await waitFor(() => {
      expect(mockOnScanSuccess).toHaveBeenCalledWith('another-business');
    });
  });

  it('handles invalid QR code and calls onScanError', async () => {
    (validateQRCodeForUser as jest.Mock).mockReturnValue({ isValid: false, error: 'Invalid URL format' });
    const mockOnScanError = jest.fn();
    const mockToastError = require('@/src/components/ui/Toast').useToast().error;

    const { getByTestId } = render(<QRScanner onScanError={mockOnScanError} />);

    await waitFor(() => {
      const cameraView = getByTestId('mock-CameraView');
      fireEvent(cameraView, 'onBarcodeScanned', { type: 'qr', data: 'invalid-data' });
    });

    await waitFor(() => {
      expect(mockToastError).toHaveBeenCalledWith('Invalid URL format');
      expect(mockOnScanError).toHaveBeenCalledWith('Invalid URL format');
    });
  });

  it('shows processing state during scan', async () => {
    (validateQRCodeForUser as jest.Mock).mockImplementationOnce(() => {
      return new Promise(resolve => setTimeout(() => resolve({ isValid: true, businessSlug: 'test-business' }), 100));
    });

    const { getByTestId, getByText } = render(<QRScanner />);

    await waitFor(() => {
      const cameraView = getByTestId('mock-CameraView');
      fireEvent(cameraView, 'onBarcodeScanned', { type: 'qr', data: 'https://dukancard.com/b/test-business' });
    });

    expect(getByText('Processing...')).toBeTruthy();

    await waitFor(() => {
      expect(getByText('Processing...')).toBeFalsy(); // Should disappear after processing
    }, { timeout: 200 });
  });
});
