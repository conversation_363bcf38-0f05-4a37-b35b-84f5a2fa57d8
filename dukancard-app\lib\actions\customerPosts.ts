/**
 * Customer Posts Actions for React Native
 * Handles CRUD operations for customer posts
 */

import { supabase } from '@/lib/supabase';
import { checkCustomerAddress } from '@/backend/supabase/services/storage/customerPostImageUploadService';
import { deleteCustomerPostMedia } from '@/src/utils/deletePostMedia';
import { CustomerPostService } from '@/backend/supabase/services/customer/customerPostService';

import { customerPostSchema, CustomerPostFormData } from '@/lib/schemas/postSchemas';

export interface ActionResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: any;
}

/**
 * Create a new customer post
 */
export async function createCustomerPost(formData: CustomerPostFormData): Promise<ActionResponse> {

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to create a post'
    };
  }

  try {
    // Validate form data
    try {
      await customerPostSchema.validate(formData, { abortEarly: false });
    } catch (validationError: any) {
// Validate form data
    try {
      await customerPostSchema.validate(formData, { abortEarly: false });
    } catch (validationError: any) {
      return {
        success: false,
        message: 'Validation failed',
        error: validationError.errors ? validationError.errors.join(', ') : 'Invalid data provided'
      };
    }
      return {
        success: false,
        message: 'Validation failed',
        error: validationError.errors ? validationError.errors.join(', ') : 'Invalid data provided'
      };
    }

    // Check customer address before creating new posts (matching Next.js implementation)
    const hasValidAddress = await checkCustomerAddress();
    if (!hasValidAddress) {
      return {
        success: false,
        message: 'Complete address required',
        error: 'Please complete your address in profile settings before creating posts'
      };
    }
    // Get the user's customer profile (use private table for own profile access)
    const { data: customerProfile, error: profileError } = await supabase
      .from('customer_profiles')
      .select('id, city_slug, state_slug, locality_slug, pincode, avatar_url')
      .eq('id', user.id)
      .single();

    if (profileError || !customerProfile) {
      return {
        success: false,
        message: 'Customer profile not found',
        error: 'You must have a customer profile to create a post'
      };
    }

    // Prepare post data
    const postData = {
      customer_id: user.id,
      content: formData.content,
      image_url: formData.image_url || null,
      city_slug: customerProfile.city_slug,
      state_slug: customerProfile.state_slug,
      locality_slug: customerProfile.locality_slug,
      pincode: customerProfile.pincode,
      mentioned_business_ids: formData.mentioned_business_ids || [],
    };

    // Insert the post
    const { data, error } = await CustomerPostService.insertCustomerPost(postData);

    if (error) {
      console.error('Error creating customer post:', error);
      return {
        success: false,
        message: 'Failed to create post',
        error: error.message
      };
    }

    return {
      success: true,
      message: 'Post created successfully',
      data
    };
  } catch (error) {
    console.error('Error in createCustomerPost:', error);
    return {
      success: false,
      message: 'Failed to create post',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Update an existing customer post
 */
export async function updateCustomerPost(postId: string, formData: CustomerPostFormData): Promise<ActionResponse> {

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to update a post'
    };
  }

  try {
    // Check if the post exists and belongs to the user
    const { data: existingPost, error: postError } = await CustomerPostService.getCustomerPostByIdAndUserId(postId, user.id);

    if (postError || !existingPost) {
      return {
        success: false,
        message: 'Post not found',
        error: 'The post does not exist or you do not have permission to update it'
      };
    }

    // Prepare update data
    const updateData = {
      content: formData.content,
      image_url: formData.image_url || null,
      mentioned_business_ids: formData.mentioned_business_ids || [],
      updated_at: new Date().toISOString()
    };

    // Update the post
    const { data, error } = await CustomerPostService.updateCustomerPost(postId, updateData);

    if (error) {
      console.error('Error updating customer post:', error);
      return {
        success: false,
        message: 'Failed to update post',
        error: error.message
      };
    }

    return {
      success: true,
      message: 'Post updated successfully',
      data
    };
  } catch (error) {
    console.error('Error in updateCustomerPost:', error);
    return {
      success: false,
      message: 'Failed to update post',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Delete a customer post
 */
export async function deleteCustomerPost(postId: string): Promise<ActionResponse> {

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to delete a post'
    };
  }

  try {
    // Check if the post exists and belongs to the user, get creation date for media deletion
    const { data: existingPost, error: postError } = await CustomerPostService.getCustomerPostByIdAndUserId(postId, user.id);

    if (postError || !existingPost) {
      return {
        success: false,
        message: 'Post not found',
        error: 'The post does not exist or you do not have permission to delete it'
      };
    }

    // First, attempt to delete the post folder from storage
    // This ensures we clean up any files that might exist, regardless of image_url status
    try {
      const mediaDeleteResult = await deleteCustomerPostMedia(user.id, postId, existingPost.created_at);
      if (!mediaDeleteResult.success && mediaDeleteResult.error) {
        console.error('Error deleting customer post media:', mediaDeleteResult.error);
        return {
          success: false,
          message: 'Failed to delete post images',
          error: `Cannot delete post: ${mediaDeleteResult.error}`
        };
      }
    } catch (mediaError) {
      console.error('Error deleting customer post media:', mediaError);
      return {
        success: false,
        message: 'Failed to delete post images',
        error: 'Cannot delete post: Failed to clean up associated images'
      };
    }

    // Only delete the post after successful media deletion
    const { error } = await CustomerPostService.deleteCustomerPost(postId);

    if (error) {
      console.error('Error deleting customer post:', error);
      return {
        success: false,
        message: 'Failed to delete post',
        error: error.message
      };
    }

    return {
      success: true,
      message: 'Post deleted successfully'
    };
  } catch (error) {
    console.error('Error in deleteCustomerPost:', error);
    return {
      success: false,
      message: 'Failed to delete post',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get customer posts for the current user
 */
export async function getCustomerPosts(limit: number = 20, offset: number = 0): Promise<ActionResponse> {

  // Get the current user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: 'Authentication required',
      error: 'You must be logged in to view posts'
    };
  }

  try {
    const { data, error } = await CustomerPostService.getCustomerPosts(user.id, limit, offset);

    if (error) {
      console.error('Error fetching customer posts:', error);
      return {
        success: false,
        message: 'Failed to fetch posts',
        error: error.message
      };
    }

    return {
      success: true,
      message: 'Posts fetched successfully',
      data: data || []
    };
  } catch (error) {
    console.error('Error in getCustomerPosts:', error);
    return {
      success: false,
      message: 'Failed to fetch posts',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
