import { getProductSortDisplayName, getBusinessSortDisplayName, getSortDisplayName } from '@/src/utils/sortMappings';

describe('sortMappings', () => {
  describe('getProductSortDisplayName', () => {
    it('should return correct display name for product sort options', () => {
      expect(getProductSortDisplayName('newest')).toBe('Newest First');
      expect(getProductSortDisplayName('name_asc')).toBe('Name (A-Z)');
      expect(getProductSortDisplayName('name_desc')).toBe('Name (Z-A)');
      expect(getProductSortDisplayName('price_low')).toBe('Price (Low to High)');
      expect(getProductSortDisplayName('price_high')).toBe('Price (High to Low)');
      expect(getProductSortDisplayName('unknown' as any)).toBe('Newest First');
    });
  });

  describe('getBusinessSortDisplayName', () => {
    it('should return correct display name for business sort options', () => {
      expect(getBusinessSortDisplayName('created_desc')).toBe('Newest First');
      expect(getBusinessSortDisplayName('created_asc')).toBe('Oldest First');
      expect(getBusinessSortDisplayName('name_asc')).toBe('Name (A-Z)');
      expect(getBusinessSortDisplayName('name_desc')).toBe('Name (Z-A)');
      expect(getBusinessSortDisplayName('likes_desc')).toBe('Most Liked');
      expect(getBusinessSortDisplayName('likes_asc')).toBe('Least Liked');
      expect(getBusinessSortDisplayName('subscriptions_desc')).toBe('Most Subscribed');
      expect(getBusinessSortDisplayName('subscriptions_asc')).toBe('Least Subscribed');
      expect(getBusinessSortDisplayName('rating_desc')).toBe('Highest Rated');
      expect(getBusinessSortDisplayName('rating_asc')).toBe('Lowest Rated');
      expect(getBusinessSortDisplayName('unknown' as any)).toBe('Newest First');
    });
  });

  describe('getSortDisplayName', () => {
    it('should return product sort display name when viewType is products', () => {
      expect(getSortDisplayName('products', 'price_low', 'created_desc')).toBe('Price (Low to High)');
    });

    it('should return business sort display name when viewType is cards', () => {
      expect(getSortDisplayName('cards', 'newest', 'likes_desc')).toBe('Most Liked');
    });
  });
});