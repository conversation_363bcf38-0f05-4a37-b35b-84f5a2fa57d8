(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/utils/client-image-compression.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_utils_client-image-compression_ts_45be4be3._.js",
  "static/chunks/lib_utils_client-image-compression_ts_5bbac14e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/utils/client-image-compression.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/lib/actions/shared/upload-customer-post-media.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_actions_shared_8ba072f5._.js",
  "static/chunks/lib_actions_shared_upload-customer-post-media_ts_f9842fd3._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/actions/shared/upload-customer-post-media.ts [app-client] (ecmascript)");
    });
});
}}),
}]);