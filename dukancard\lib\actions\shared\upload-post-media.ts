"use server";

import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { getPostImagePath, getPostFolderPath } from "@/lib/utils/storage-paths";

export interface PostMediaUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Upload and process image for business post
 * Future-proof structure: {userId}/posts/{year}/{month}/{postId}/image_0_{timestamp}.webp
 */
export async function uploadPostImage(
  formData: FormData,
  postId: string,
  postCreatedAt?: string
): Promise<PostMediaUploadResult> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const userId = user.id;
  const imageFile = formData.get("imageFile") as File | null;

  if (!imageFile) {
    return { success: false, error: "No image file provided." };
  }

  // Use the provided post creation date for consistent folder structure

  // Validate file type (strict backend validation)
  const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
  const allowedExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];

  if (!allowedTypes.includes(imageFile.type)) {
    return {
      success: false,
      error: "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed."
    };
  }

  // Validate file extension as additional security measure
  const fileName = imageFile.name.toLowerCase();
  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
  if (!hasValidExtension) {
    return {
      success: false,
      error: "Invalid file extension. Please upload files with .jpg, .jpeg, .png, .gif, or .webp extensions."
    };
  }

  // Backend size validation (critical security check)
  const maxSize = 15 * 1024 * 1024; // 15MB - matches industry standards
  if (imageFile.size > maxSize) {
    const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(2);
    return {
      success: false,
      error: `File size (${fileSizeMB}MB) exceeds the 15MB limit. Please choose a smaller image.`
    };
  }

  // Additional security: Check for minimum file size (avoid empty files)
  if (imageFile.size < 100) { // 100 bytes minimum
    return {
      success: false,
      error: "File appears to be empty or corrupted. Please try a different image."
    };
  }

  try {
    // Create scalable path structure for billions of users
    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const bucketName = "business";
    const imagePath = getPostImagePath(userId, postId, 0, timestamp, postCreatedAt);

    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    // Use admin client for storage operations to bypass RLS
    const adminSupabase = createAdminClient();

    // Upload to Supabase Storage using admin client
    const { error: uploadError } = await adminSupabase.storage
      .from(bucketName)
      .upload(imagePath, fileBuffer, {
        contentType: imageFile.type, // Use original file type (already compressed)
        upsert: true
      });

    if (uploadError) {
      console.error("Post Image Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`,
      };
    }

    // Get the public URL using admin client
    const { data: urlData } = adminSupabase.storage
      .from(bucketName)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    return {
      success: true,
      url: urlData.publicUrl,
    };

  } catch (error) {
    console.error("Error processing post image:", error);
    return {
      success: false,
      error: "Failed to process image. Please try a different image."
    };
  }
}

/**
 * Delete entire post folder and all its contents
 * This removes all files in the post folder, effectively deleting the folder
 */
export async function deletePostMedia(
  userId: string,
  postId: string,
  createdAt: string
): Promise<{ success: boolean; error?: string }> {
  // Use admin client for storage operations to bypass RLS
  const adminSupabase = createAdminClient();

  try {
    const bucketName = "businesses";
    const postFolderPath = getPostFolderPath(userId, postId, createdAt);

    // List all files in the post folder
    const { data: files, error: listError } = await adminSupabase.storage
      .from(bucketName)
      .list(postFolderPath, {
        limit: 1000, // Set a reasonable limit for safety
        sortBy: { column: 'name', order: 'asc' }
      });

    if (listError) {
      // If folder doesn't exist, consider it successful (already deleted)
      if (listError.message?.includes('not found') ||
          listError.message?.includes('does not exist') ||
          listError.message?.includes('The resource was not found')) {
        return { success: true };
      }
      console.error("Error listing post media files:", listError);
      return {
        success: false,
        error: `Failed to list media files: ${listError.message}`,
      };
    }

    if (!files || files.length === 0) {
      // No files to delete, folder is already empty or doesn't exist
      return { success: true };
    }

    // Create full paths for all files in the folder
    const filePaths = files.map(file => `${postFolderPath}/${file.name}`);

    // Delete all files in the post folder using admin client
    // In object storage, deleting all files effectively removes the folder
    const { error: deleteError } = await adminSupabase.storage
      .from(bucketName)
      .remove(filePaths);

    if (deleteError) {
      console.error("Error deleting post folder contents:", deleteError);
      return {
        success: false,
        error: `Failed to delete post folder: ${deleteError.message}`,
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Error in deletePostMedia:", error);
    return {
      success: false,
      error: "An unexpected error occurred while deleting post folder."
    };
  }
}
