import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import ComingSoonModal from '@/src/components/ui/ComingSoonModal';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Linking } from 'react-native';
import { useToast } from '@/src/components/ui/Toast';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Linking: {
    canOpenURL: jest.fn(() => Promise.resolve(true)),
    openURL: jest.fn(() => Promise.resolve()),
  },
}));
jest.mock('@/src/components/ui/Toast');

describe('ComingSoonModal', () => {
  const mockOnClose = jest.fn();
  const mockToastError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (useToast as jest.Mock).mockReturnValue({ error: mockToastError });
  });

  it('renders correctly when visible', () => {
    render(
      <ComingSoonModal
        visible={true}
        onClose={mockOnClose}
        featureName="New Feature"
      />
    );

    expect(screen.getByText('New Feature')).toBeOnTheScreen();
    expect(screen.getByText('Coming Soon to Mobile')).toBeOnTheScreen();
    expect(screen.getByText('Open Website')).toBeOnTheScreen();
    expect(screen.getByText('Maybe Later')).toBeOnTheScreen();
  });

  it('calls onClose when close button is pressed', () => {
    render(
      <ComingSoonModal
        visible={true}
        onClose={mockOnClose}
        featureName="New Feature"
      />
    );
    fireEvent.press(screen.getByLabelText('close-button')); // Assuming an accessibilityLabel
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when Maybe Later button is pressed', () => {
    render(
      <ComingSoonModal
        visible={true}
        onClose={mockOnClose}
        featureName="New Feature"
      />
    );
    fireEvent.press(screen.getByText('Maybe Later'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('opens website when Open Website button is pressed', async () => {
    render(
      <ComingSoonModal
        visible={true}
        onClose={mockOnClose}
        featureName="New Feature"
      />
    );
    fireEvent.press(screen.getByText('Open Website'));

    await waitFor(() => {
      expect(Linking.canOpenURL).toHaveBeenCalledWith('https://dukancard.in/login');
      expect(Linking.openURL).toHaveBeenCalledWith('https://dukancard.in/login');
    });
  });

  it('handles Linking.canOpenURL returning false', async () => {
    (Linking.canOpenURL as jest.Mock).mockResolvedValueOnce(false);

    render(
      <ComingSoonModal
        visible={true}
        onClose={mockOnClose}
        featureName="New Feature"
      />
    );
    fireEvent.press(screen.getByText('Open Website'));

    await waitFor(() => {
      expect(Linking.canOpenURL).toHaveBeenCalledWith('https://dukancard.in/login');
      expect(Linking.openURL).not.toHaveBeenCalled();
    });
  });

  it('displays custom description when provided', () => {
    render(
      <ComingSoonModal
        visible={true}
        onClose={mockOnClose}
        featureName="New Feature"
        description="This is a custom description."
      />
    );
    expect(screen.getByText('This is a custom description.')).toBeOnTheScreen();
  });

  it('applies dark mode styles', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    render(
      <ComingSoonModal
        visible={true}
        onClose={mockOnClose}
        featureName="New Feature"
      />
    );
    // Check for dark mode specific styles (e.g., background color, text color)
    // This is a simplified check, a snapshot test would be more comprehensive for styles.
    expect(screen.getByText('New Feature').props.style).toContainEqual({
      color: '#FFFFFF',
    });
  });
});