import { deletePostMedia, deleteCustomerPostMedia } from '@/src/utils/deletePostMedia';
import { supabase } from '@/lib/supabase';
import { getPostFolderPath } from '@/backend/supabase/utils/storage-paths';

// Mock external dependencies
jest.mock('@/lib/supabase');
jest.mock('@/backend/supabase/utils/storage-paths');

describe('deletePostMedia', () => {
  const mockUserId = 'user123';
  const mockPostId = 'post123';
  const mockCreatedAt = '2023-01-01T00:00:00Z';
  const mockFolderPath = 'user123/posts/post123_2023-01-01T00:00:00Z';

  beforeEach(() => {
    jest.clearAllMocks();
    (getPostFolderPath as jest.Mock).mockReturnValue(mockFolderPath);
    (supabase.storage.from as jest.Mock).mockReturnValue({
      list: jest.fn().mockResolvedValue({
        data: [{ name: 'image1.jpg' }, { name: 'image2.png' }],
        error: null,
      }),
      remove: jest.fn().mockResolvedValue({ error: null }),
    });
  });

  it('should delete business post media successfully', async () => {
    const result = await deletePostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(true);
    expect(getPostFolderPath).toHaveBeenCalledWith(mockUserId, mockPostId, mockCreatedAt);
    expect(supabase.storage.from).toHaveBeenCalledWith('business');
    expect(supabase.storage.from('business').list).toHaveBeenCalledWith(mockFolderPath, { limit: 1000, sortBy: { column: 'name', order: 'asc' } });
    expect(supabase.storage.from('business').remove).toHaveBeenCalledWith([
      `${mockFolderPath}/image1.jpg`,
      `${mockFolderPath}/image2.png`,
    ]);
  });

  it('should return success if no files are found in the folder', async () => {
    (supabase.storage.from('business').list as jest.Mock).mockResolvedValue({
      data: [],
      error: null,
    });
    const result = await deletePostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(true);
    expect(supabase.storage.from('business').remove).not.toHaveBeenCalled();
  });

  it('should handle list error', async () => {
    (supabase.storage.from('business').list as jest.Mock).mockResolvedValue({
      data: null,
      error: { message: 'List error' },
    });
    const result = await deletePostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(false);
    expect(result.error).toBe('Failed to list post folder: List error');
  });

  it('should handle remove error', async () => {
    (supabase.storage.from('business').remove as jest.Mock).mockResolvedValue({
      error: { message: 'Remove error' },
    });
    const result = await deletePostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(false);
    expect(result.error).toBe('Failed to delete post folder: Remove error');
  });

  it('should handle unexpected errors', async () => {
    (getPostFolderPath as jest.Mock).mockImplementation(() => {
      throw new Error('Unexpected error');
    });
    const result = await deletePostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(false);
    expect(result.error).toBe('An unexpected error occurred while deleting post folder.');
  });
});

describe('deleteCustomerPostMedia', () => {
  const mockUserId = 'user123';
  const mockPostId = 'post123';
  const mockCreatedAt = '2023-01-01T00:00:00Z';
  const mockFolderPath = 'user123/customer_posts/post123_2023-01-01T00:00:00Z';

  beforeEach(() => {
    jest.clearAllMocks();
    (getPostFolderPath as jest.Mock).mockReturnValue(mockFolderPath);
    (supabase.storage.from as jest.Mock).mockReturnValue({
      list: jest.fn().mockResolvedValue({
        data: [{ name: 'customer_image1.jpg' }],
        error: null,
      }),
      remove: jest.fn().mockResolvedValue({ error: null }),
    });
  });

  it('should delete customer post media successfully', async () => {
    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(true);
    expect(getPostFolderPath).toHaveBeenCalledWith(mockUserId, mockPostId, mockCreatedAt);
    expect(supabase.storage.from).toHaveBeenCalledWith('customers');
    expect(supabase.storage.from('customers').list).toHaveBeenCalledWith(mockFolderPath, { limit: 1000, sortBy: { column: 'name', order: 'asc' } });
    expect(supabase.storage.from('customers').remove).toHaveBeenCalledWith([
      `${mockFolderPath}/customer_image1.jpg`,
    ]);
  });

  it('should return success if no files are found in the customer folder', async () => {
    (supabase.storage.from('customers').list as jest.Mock).mockResolvedValue({
      data: [],
      error: null,
    });
    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(true);
    expect(supabase.storage.from('customers').remove).not.toHaveBeenCalled();
  });

  it('should handle list error for customer post', async () => {
    (supabase.storage.from('customers').list as jest.Mock).mockResolvedValue({
      data: null,
      error: { message: 'Customer list error' },
    });
    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(false);
    expect(result.error).toBe('Failed to list customer post folder: Customer list error');
  });

  it('should handle remove error for customer post', async () => {
    (supabase.storage.from('customers').remove as jest.Mock).mockResolvedValue({
      error: { message: 'Customer remove error' },
    });
    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(false);
    expect(result.error).toBe('Failed to delete customer post folder: Customer remove error');
  });

  it('should handle unexpected errors for customer post', async () => {
    (getPostFolderPath as jest.Mock).mockImplementation(() => {
      throw new Error('Unexpected error');
    });
    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);
    expect(result.success).toBe(false);
    expect(result.error).toBe('An unexpected error occurred while deleting customer post folder.');
  });
});