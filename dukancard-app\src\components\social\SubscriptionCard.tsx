/**
 * SubscriptionCard Component for React Native
 * Displays individual subscription with unsubscribe functionality
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useToast } from '@/src/components/ui/Toast';
import { useTheme } from '@/src/hooks/useTheme';
import { createSubscriptionCardStyles } from '@/styles/components/social/subscription-card';

interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  locality: string | null;
  city: string | null;
  state: string | null;
}

interface SubscriptionWithProfile {
  id: string;
  business_profiles: BusinessProfileData | null;
}

interface SubscriptionCardProps {
  subscription: SubscriptionWithProfile;
  onUnsubscribe: (subscriptionId: string) => Promise<void>;
}

export const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  subscription,
  onUnsubscribe,
}) => {
  const [isUnsubscribing, setIsUnsubscribing] = useState(false);
  const business = subscription.business_profiles;
  const router = useRouter();
  const toast = useToast();
  const theme = useTheme();
  const styles = createSubscriptionCardStyles(theme);

  if (!business) {
    return null;
  }

  const handleUnsubscribe = () => {
    Alert.alert(
      'Unsubscribe',
      `Are you sure you want to unsubscribe from ${business.business_name}?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Unsubscribe',
          style: 'destructive',
          onPress: async () => {
            setIsUnsubscribing(true);
            try {
              await onUnsubscribe(subscription.id);
            } catch (error) {
              console.error('Error unsubscribing:', error);
            } finally {
              setIsUnsubscribing(false);
            }
          },
        },
      ]
    );
  };

  const handleVisitBusiness = () => {
    try {
      if (!business?.business_slug) {
        toast.error("Navigation Error", "Business profile not available");
        return;
      }

      // Navigate to business profile using the slug
      router.push(`/business/${business.business_slug}`);
    } catch (error) {
      console.error("Error navigating to business profile:", error);
      toast.error("Navigation Error", "Failed to open business profile");
    }
  };

  const getBusinessInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatAddress = () => {
    const parts = [];
    if (business.locality) parts.push(business.locality);
    if (business.city) parts.push(business.city);
    if (business.state) parts.push(business.state);
    return parts.join(', ');
  };

  return (
    <View style={styles.listItem}>
      <TouchableOpacity
        style={styles.listContent}
        onPress={handleVisitBusiness}
        activeOpacity={0.7}
      >
        {/* Business Logo/Avatar */}
        <View style={styles.logoContainer}>
          {business.logo_url ? (
            <Image source={{ uri: business.logo_url }} style={styles.logo} />
          ) : (
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>
                {getBusinessInitials(business.business_name || 'B')}
              </Text>
            </View>
          )}
        </View>

        {/* Business Info */}
        <View style={styles.businessInfo}>
          <Text style={styles.businessName} numberOfLines={1}>
            {business.business_name}
          </Text>
          {formatAddress() && (
            <Text style={styles.businessAddress} numberOfLines={1}>
              {formatAddress()}
            </Text>
          )}
          <View style={styles.statusContainer}>
            <View style={styles.statusDot} />
            <Text style={styles.statusText}>Following</Text>
          </View>
        </View>

        {/* Unsubscribe Button - Right Corner */}
        <TouchableOpacity
          style={[styles.unsubscribeButton, isUnsubscribing && styles.unsubscribeButtonDisabled]}
          onPress={handleUnsubscribe}
          disabled={isUnsubscribing}
        >
          {isUnsubscribing ? (
            <ActivityIndicator size="small" color="#007AFF" />
          ) : (
            <Ionicons name="notifications-off" size={20} color="#007AFF" />
          )}
        </TouchableOpacity>
      </TouchableOpacity>

      {/* Separator */}
      <View style={styles.separator} />
    </View>
  );
};
