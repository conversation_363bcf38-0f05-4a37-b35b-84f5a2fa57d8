import { renderHook } from '@testing-library/react-hooks';
import { useThemeColor } from '@/src/hooks/useThemeColor';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Colors } from '@/src/constants/Colors';

// Mock external dependencies
jest.mock('@/src/hooks/useColorScheme');
jest.mock('@/src/constants/Colors', () => ({
  Colors: {
    light: {
      text: '#000000',
      background: '#FFFFFF',
      primary: '#D4AF37',
    },
    dark: {
      text: '#FFFFFF',
      background: '#000000',
      primary: '#FFD700',
    },
  },
}));

describe('useThemeColor', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
  });

  it('should return light color from props when in light mode', () => {
    const { result } = renderHook(() => useThemeColor({ light: '#FF0000', dark: '#0000FF' }, 'text'));
    expect(result.current).toBe('#FF0000');
  });

  it('should return dark color from props when in dark mode', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    const { result } = renderHook(() => useThemeColor({ light: '#FF0000', dark: '#0000FF' }, 'text'));
    expect(result.current).toBe('#0000FF');
  });

  it('should return color from Colors constant when no prop color is provided', () => {
    const { result } = renderHook(() => useThemeColor({}, 'text'));
    expect(result.current).toBe(Colors.light.text);
  });

  it('should return dark color from Colors constant when in dark mode and no prop color', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    const { result } = renderHook(() => useThemeColor({}, 'text'));
    expect(result.current).toBe(Colors.dark.text);
  });

  it('should return primary color from Colors constant', () => {
    const { result } = renderHook(() => useThemeColor({}, 'primary'));
    expect(result.current).toBe(Colors.light.primary);
  });

  it('should handle undefined color scheme gracefully', () => {
    (useColorScheme as jest.Mock).mockReturnValue(undefined);
    const { result } = renderHook(() => useThemeColor({}, 'text'));
    expect(result.current).toBe(Colors.light.text); // Should default to light theme
  });
});