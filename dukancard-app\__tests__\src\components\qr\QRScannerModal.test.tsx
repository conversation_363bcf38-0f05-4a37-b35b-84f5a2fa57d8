import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import QRScannerModal from '../../../../src/components/qr/QRScannerModal';
import { Alert, Linking } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Camera } from 'expo-camera';
import { QRScanService } from '@/backend/supabase/services/common/qrScanService';

// Mock necessary modules
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock('@/src/components/qr/QRScanner', () => jest.fn(({ onScanSuccess, onScanError, enableTorch }) => (
  <mock-QRScanner
    testID="qr-scanner-component"
    onScanSuccess={onScanSuccess}
    onScanError={onScanError}
    enableTorch={enableTorch}
  />
)));

jest.mock('@/backend/supabase/services/common/qrScanService', () => ({
  QRScanService: {
    processQRScan: jest.fn(),
    processQRScanAllowOffline: jest.fn(),
    getErrorMessage: jest.fn((error) => `Error: ${error}`),
    getErrorRecoveryAction: jest.fn(() => ({ action: 'none', message: '' })),
  },
}));

jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
  }),
}));

jest.mock('@/src/utils/qrCodeUtils', () => ({
  validateQRCodeForUser: jest.fn(),
}));

jest.mock('@/styles/components/QRScannerModal-styles', () => ({
  createQRScannerModalStyles: () => ({
    container: { flex: 1 },
    header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: 16 },
    closeButton: { padding: 8 },
    headerCenter: { flex: 1, alignItems: 'center' },
    headerTitle: { color: '#fff', fontSize: 18, fontWeight: 'bold' },
    offlineIndicator: { flexDirection: 'row', alignItems: 'center', marginTop: 4 },
    offlineText: { color: '#EF4444', marginLeft: 4, fontSize: 12 },
    flashlightButton: { padding: 8 },
    scannerContainer: { flex: 1 },
    footer: { padding: 16, alignItems: 'center' },
    galleryButton: { padding: 12, backgroundColor: '#D4AF37', borderRadius: 8, marginBottom: 12 },
    galleryButtonText: { color: '#000', fontWeight: 'bold' },
    footerText: { color: '#ccc', fontSize: 12, textAlign: 'center' },
    processingOverlay: { ...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(0,0,0,0.8)', justifyContent: 'center', alignItems: 'center' },
    processingContainer: { alignItems: 'center' },
    processingText: { color: '#D4AF37', marginTop: 10, fontSize: 16 },
    progressContainer: { width: '80%', marginTop: 10 },
    progressBar: { height: 5, backgroundColor: '#333', borderRadius: 5 },
    progressFill: { height: '100%', backgroundColor: '#D4AF37', borderRadius: 5 },
    progressText: { color: '#fff', fontSize: 12, marginTop: 5 },
  }),
}));

jest.mock('@/src/utils/networkStatus', () => ({
  useNetworkStatus: () => ({ isConnected: true }),
}));

jest.spyOn(Alert, 'alert');
jest.spyOn(Linking, 'openSettings');

describe('QRScannerModal', () => {
  const mockOnClose = jest.fn();
  const mockOnScanSuccess = jest.fn();
  const mockRouterPush = jest.fn();
  const mockToast = require('@/src/components/ui/Toast').useToast();

  beforeEach(() => {
    jest.clearAllMocks();
    require('expo-router').useRouter().push = mockRouterPush;
    (QRScanService.processQRScan as jest.Mock).mockResolvedValue({ success: true, businessSlug: 'test-business' });
    (QRScanService.processQRScanAllowOffline as jest.Mock).mockResolvedValue({ success: true, businessSlug: 'test-business-offline' });
    (ImagePicker.getMediaLibraryPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'granted' });
    (ImagePicker.requestMediaLibraryPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'granted' });
    (ImagePicker.launchImageLibraryAsync as jest.Mock).mockResolvedValue({ canceled: true });
    (Camera.scanFromURLAsync as jest.Mock).mockResolvedValue([]);
    (require('@/src/utils/qrCodeUtils').validateQRCodeForUser as jest.Mock).mockReturnValue({ isValid: true, businessSlug: 'mock-slug' });
  });

  it('renders null when not visible', () => {
    const { queryByTestId } = render(<QRScannerModal visible={false} onClose={mockOnClose} />);
    expect(queryByTestId('qr-scanner-modal')).toBeNull();
  });

  it('renders correctly when visible', () => {
    const { getByText, getByTestId } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);
    expect(getByText('Scan QR Code')).toBeTruthy();
    expect(getByTestId('qr-scanner-component')).toBeTruthy();
  });

  it('toggles flashlight on button press', () => {
    const { getByTestId } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);
    const flashlightButton = getByTestId('flashlight-button');

    fireEvent.press(flashlightButton);
    expect(getByTestId('qr-scanner-component').props.enableTorch).toBe(true);

    fireEvent.press(flashlightButton);
    expect(getByTestId('qr-scanner-component').props.enableTorch).toBe(false);
  });

  it('handles successful QR scan and navigates', async () => {
    const { getByTestId } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);
    const qrScanner = getByTestId('qr-scanner-component');

    act(() => {
      qrScanner.props.onScanSuccess('test-business');
    });

    await waitFor(() => {
      expect(mockToast.success).toHaveBeenCalledWith('QR code scanned successfully!');
      expect(mockOnClose).toHaveBeenCalled();
      expect(mockRouterPush).toHaveBeenCalledWith('/business/test-business');
    });
  });

  it('calls onScanSuccess prop if provided', async () => {
    const { getByTestId } = render(<QRScannerModal visible={true} onClose={mockOnClose} onScanSuccess={mockOnScanSuccess} />);
    const qrScanner = getByTestId('qr-scanner-component');

    act(() => {
      qrScanner.props.onScanSuccess('custom-business');
    });

    await waitFor(() => {
      expect(mockToast.success).toHaveBeenCalledWith('QR code scanned successfully!');
      expect(mockOnClose).toHaveBeenCalled();
      expect(mockOnScanSuccess).toHaveBeenCalledWith('custom-business');
    });
  });

  it('handles invalid QR code scan', async () => {
    (QRScanService.processQRScan as jest.Mock).mockResolvedValue({ success: false, error: 'invalid_qr_code' });
    const { getByTestId } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);
    const qrScanner = getByTestId('qr-scanner-component');

    act(() => {
      qrScanner.props.onScanSuccess('invalid-data');
    });

    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Error: invalid_qr_code\n');
      expect(mockOnClose).not.toHaveBeenCalled();
    });
  });

  it('handles offline scenario and queues scan', async () => {
    require('@/src/utils/networkStatus').useNetworkStatus.mockReturnValue({ isConnected: false });
    const { getByTestId } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);
    const qrScanner = getByTestId('qr-scanner-component');

    act(() => {
      qrScanner.props.onScanSuccess('offline-business');
    });

    await waitFor(() => {
      expect(mockToast.info).toHaveBeenCalledWith(
        'You are offline. QR scan has been queued and will be processed when connection is restored.'
      );
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it('processes queued scans when coming online', async () => {
    let isConnected = false;
    require('@/src/utils/networkStatus').useNetworkStatus.mockImplementation(() => ({
      isConnected: isConnected,
    }));

    const { getByTestId, rerender } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);
    const qrScanner = getByTestId('qr-scanner-component');

    // Simulate offline scan
    act(() => {
      qrScanner.props.onScanSuccess('offline-business-queued');
    });

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });

    // Simulate coming online
    isConnected = true;
    rerender(<QRScannerModal visible={true} onClose={mockOnClose} />);

    await waitFor(() => {
      expect(mockToast.info).toHaveBeenCalledWith('Processing queued QR scans...');
      expect(QRScanService.processQRScanAllowOffline).toHaveBeenCalledWith('https://dukancard.in/offline-business-queued');
      expect(mockRouterPush).toHaveBeenCalledWith('/business/test-business-offline');
    });
  });

  it('handles closing modal during processing', async () => {
    (QRScanService.processQRScan as jest.Mock).mockImplementationOnce(() => new Promise(resolve => setTimeout(resolve, 500)));

    const { getByTestId, getByText } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);
    const qrScanner = getByTestId('qr-scanner-component');

    act(() => {
      qrScanner.props.onScanSuccess('long-processing-business');
    });

    await waitFor(() => {
      expect(getByText('Processing QR code...')).toBeTruthy();
    });

    fireEvent.press(getByTestId('close-button'));

    expect(Alert.alert).toHaveBeenCalledWith(
      'Processing QR Code',
      'A QR code is currently being processed. Do you want to cancel?',
      expect.any(Array)
    );

    const cancelButton = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    act(() => {
      cancelButton.onPress();
    });

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
      expect(getByText('Processing QR code...')).toBeFalsy();
    });
  });

  it('handles gallery image selection and QR code detection', async () => {
    (ImagePicker.launchImageLibraryAsync as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/qr-image.jpg' }],
    });
    (Camera.scanFromURLAsync as jest.Mock).mockResolvedValue([{ data: 'https://dukancard.in/gallery-business' }]);

    const { getByText } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);

    fireEvent.press(getByText('Select QR from Gallery'));

    await waitFor(() => {
      expect(ImagePicker.launchImageLibraryAsync).toHaveBeenCalled();
      expect(Camera.scanFromURLAsync).toHaveBeenCalledWith('file://mock/path/to/qr-image.jpg', ['qr']);
      expect(mockToast.success).toHaveBeenCalledWith('QR code detected successfully!');
      expect(mockRouterPush).toHaveBeenCalledWith('/business/gallery-business');
    });
  });

  it('shows error if no QR code found in gallery image', async () => {
    (ImagePicker.launchImageLibraryAsync as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/non-qr-image.jpg' }],
    });
    (Camera.scanFromURLAsync as jest.Mock).mockResolvedValue([]); // No QR code found

    const { getByText } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);

    fireEvent.press(getByText('Select QR from Gallery'));

    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('No QR code found in the selected image');
    });
  });

  it('handles gallery permission denied', async () => {
    (ImagePicker.getMediaLibraryPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'denied', canAskAgain: false });

    const { getByText } = render(<QRScannerModal visible={true} onClose={mockOnClose} />);

    fireEvent.press(getByText('Select QR from Gallery'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Gallery Permission Required',
        'Gallery access is needed to select QR code images. Please enable gallery permissions in your device settings.',
        expect.any(Array)
      );
    });
  });
});
