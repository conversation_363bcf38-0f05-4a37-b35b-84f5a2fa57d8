import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { EmptyState } from '../../../../src/components/shared/ui/EmptyState';
import { FolderOpen, XCircle } from 'lucide-react-native';

// Mock Ionicons from @expo/vector-icons
jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons',
}));

// Mock Lucide icons
jest.mock('lucide-react-native', () => ({
  FolderOpen: 'FolderOpenIcon',
  XCircle: 'XCircleIcon',
}));

describe('EmptyState', () => {
  it('renders title and description correctly', () => {
    const { getByText } = render(
      <EmptyState title="No Data Found" description="Please try again later." />
    );
    expect(getByText('No Data Found')).toBeTruthy();
    expect(getByText('Please try again later.')).toBeTruthy();
  });

  it('renders default Ionicons icon when no icon prop is provided', () => {
    const { getByTestId } = render(
      <EmptyState title="No Data Found" />
    );
    expect(getByTestId('Ionicons')).toBeTruthy();
    expect(getByTestId('Ionicons').props.name).toBe('folder-open-outline');
  });

  it('renders custom Ionicons icon when icon prop is a string', () => {
    const { getByTestId } = render(
      <EmptyState title="No Data Found" icon="alert-circle-outline" />
    );
    expect(getByTestId('Ionicons')).toBeTruthy();
    expect(getByTestId('Ionicons').props.name).toBe('alert-circle-outline');
  });

  it('renders custom LucideIcon when icon prop is a component', () => {
    const { getByTestId } = render(
      <EmptyState title="No Data Found" icon={XCircle} />
    );
    expect(getByTestId('XCircleIcon')).toBeTruthy();
  });

  it('renders action button and calls onAction when pressed', () => {
    const mockOnAction = jest.fn();
    const { getByText } = render(
      <EmptyState
        title="No Data Found"
        actionText="Retry"
        onAction={mockOnAction}
      />
    );
    const actionButton = getByText('Retry');
    fireEvent.press(actionButton);
    expect(mockOnAction).toHaveBeenCalledTimes(1);
  });

  it('does not render description if not provided', () => {
    const { queryByText } = render(
      <EmptyState title="No Data Found" />
    );
    expect(queryByText('Please try again later.')).toBeNull();
  });

  it('does not render action button if actionText or onAction are not provided', () => {
    const { queryByText } = render(
      <EmptyState title="No Data Found" description="Some description" />
    );
    expect(queryByText('Retry')).toBeNull();
  });
});
