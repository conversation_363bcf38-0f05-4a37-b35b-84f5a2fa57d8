import { 
  generatePostUrl,
  generatePostDeepLink,
  generatePostPath,
  extractPostIdFromUrl,
  isValidPostId,
  generateSharingText,
  isPostUrl,
} from '@/src/utils/postUrl';
import { BACKEND_CONFIG } from '@/src/config/publicKeys';

describe('postUrl utilities', () => {
  const mockPostId = 'test-post-id-123';

  describe('generatePostUrl', () => {
    it('should generate a complete post URL', () => {
      const expectedUrl = `${BACKEND_CONFIG.baseUrl}/post/${mockPostId}`;
      expect(generatePostUrl(mockPostId)).toBe(expectedUrl);
    });
  });

  describe('generatePostDeepLink', () => {
    it('should generate a deep link URL', () => {
      const expectedDeepLink = `dukancard://post/${mockPostId}`;
      expect(generatePostDeepLink(mockPostId)).toBe(expectedDeepLink);
    });
  });

  describe('generatePostPath', () => {
    it('should generate a relative post path', () => {
      const expectedPath = `/post/${mockPostId}`;
      expect(generatePostPath(mockPostId)).toBe(expectedPath);
    });
  });

  describe('extractPostIdFromUrl', () => {
    it('should extract post ID from a complete URL', () => {
      const url = `${BACKEND_CONFIG.baseUrl}/post/${mockPostId}`;
      expect(extractPostIdFromUrl(url)).toBe(mockPostId);
    });

    it('should extract post ID from a deep link', () => {
      const deepLink = `dukancard://post/${mockPostId}`;
      expect(extractPostIdFromUrl(deepLink)).toBe(mockPostId);
    });

    it('should return null for invalid URL format', () => {
      const invalidUrl = `https://example.com/invalid/${mockPostId}`;
      expect(extractPostIdFromUrl(invalidUrl)).toBeNull();
    });

    it('should return null for URL without post ID', () => {
      const url = `${BACKEND_CONFIG.baseUrl}/post/`;
      expect(extractPostIdFromUrl(url)).toBeNull();
    });

    it('should return null for malformed URL', () => {
      expect(extractPostIdFromUrl('invalid-url')).toBeNull();
    });
  });

  describe('isValidPostId', () => {
    it('should return true for a valid post ID', () => {
      expect(isValidPostId(mockPostId)).toBe(true);
    });

    it('should return false for an empty string', () => {
      expect(isValidPostId('')).toBe(false);
    });

    it('should return false for a string with only whitespace', () => {
      expect(isValidPostId('   ')).toBe(false);
    });

    it('should return false for non-string input', () => {
      expect(isValidPostId(null as any)).toBe(false);
      expect(isValidPostId(undefined as any)).toBe(false);
      expect(isValidPostId(123 as any)).toBe(false);
    });
  });

  describe('generateSharingText', () => {
    it('should generate sharing text with author name', () => {
      const authorName = 'John Doe';
      const expectedText = `Check out this post by ${authorName} on Dukancard: ${BACKEND_CONFIG.baseUrl}/post/${mockPostId}`;
      expect(generateSharingText(mockPostId, authorName)).toBe(expectedText);
    });

    it('should generate sharing text without author name', () => {
      const expectedText = `Check out this post on Dukancard: ${BACKEND_CONFIG.baseUrl}/post/${mockPostId}`;
      expect(generateSharingText(mockPostId)).toBe(expectedText);
    });
  });

  describe('isPostUrl', () => {
    it('should return true for a valid post URL', () => {
      const url = `${BACKEND_CONFIG.baseUrl}/post/${mockPostId}`;
      expect(isPostUrl(url)).toBe(true);
    });

    it('should return true for a valid deep link', () => {
      const deepLink = `dukancard://post/${mockPostId}`;
      expect(isPostUrl(deepLink)).toBe(true);
    });

    it('should return false for an invalid post URL', () => {
      const invalidUrl = `https://example.com/not-a-post/${mockPostId}`;
      expect(isPostUrl(invalidUrl)).toBe(false);
    });

    it('should return false for a URL with no post ID', () => {
      const url = `${BACKEND_CONFIG.baseUrl}/post/`;
      expect(isPostUrl(url)).toBe(false);
    });

    it('should return false for a non-string input', () => {
      expect(isPostUrl(123 as any)).toBe(false);
    });
  });
});