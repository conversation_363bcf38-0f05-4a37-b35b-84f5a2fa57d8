import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { HapticTab } from '../../../src/components/HapticTab';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

// Mock expo-haptics
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Light: 'light',
  },
}));

describe('HapticTab', () => {
  const mockOnPressIn = jest.fn();
  const defaultProps = {
    onPressIn: mockOnPressIn,
    accessibilityRole: 'button',
    accessibilityState: { selected: false },
    testID: 'haptic-tab-button',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('calls onPressIn prop', () => {
    const { getByTestId } = render(<HapticTab {...defaultProps} />);
    fireEvent.press(getByTestId('haptic-tab-button'));
    expect(mockOnPressIn).toHaveBeenCalled();
  });

  it('triggers haptic feedback on iOS when pressed', () => {
    Platform.OS = 'ios';
    process.env.EXPO_OS = 'ios';

    const { getByTestId } = render(<HapticTab {...defaultProps} />);
    fireEvent.press(getByTestId('haptic-tab-button'));

    expect(Haptics.impactAsync).toHaveBeenCalledWith(Haptics.ImpactFeedbackStyle.Light);
  });

  it('does not trigger haptic feedback on non-iOS platforms', () => {
    Platform.OS = 'android';
    process.env.EXPO_OS = 'android';

    const { getByTestId } = render(<HapticTab {...defaultProps} />);
    fireEvent.press(getByTestId('haptic-tab-button'));

    expect(Haptics.impactAsync).not.toHaveBeenCalled();
  });
});
