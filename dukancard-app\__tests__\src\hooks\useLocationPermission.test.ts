import { renderHook, act, waitFor } from '@testing-library/react-hooks';
import { useLocationPermission } from '@/src/hooks/useLocationPermission';
import * as Location from 'expo-location';
import {
  checkLocationPermission,
  requestLocationPermission,
} from '@/backend/supabase/services/location/locationService';

// Mock external dependencies
jest.mock('expo-location');
jest.mock('@/backend/supabase/services/location/locationService');

describe('useLocationPermission', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default mock for successful permission check
    (checkLocationPermission as jest.Mock).mockResolvedValue({
      granted: true,
      canAskAgain: true,
      status: 'granted',
      expires: 'never',
    });
    // Default mock for successful permission request
    (requestLocationPermission as jest.Mock).mockResolvedValue({
      granted: true,
      canAskAgain: true,
      status: 'granted',
      expires: 'never',
    });
  });

  it('should return initial loading state and then permission status', async () => {
    const { result } = renderHook(() => useLocationPermission());

    expect(result.current.isLoading).toBe(true);
    expect(result.current.permission).toBeNull();

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.permission?.granted).toBe(true);
    });
    expect(checkLocationPermission).toHaveBeenCalledTimes(1);
  });

  it('checkPermission should update permission state', async () => {
    const { result } = renderHook(() => useLocationPermission());

    // Wait for initial check to complete
    await waitFor(() => expect(result.current.isLoading).toBe(false));

    (checkLocationPermission as jest.Mock).mockResolvedValueOnce({
      granted: false,
      canAskAgain: true,
      status: 'denied',
      expires: 'never',
    });

    await act(async () => {
      await result.current.checkPermission();
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.permission?.granted).toBe(false);
    expect(checkLocationPermission).toHaveBeenCalledTimes(2); // Initial + explicit check
  });

  it('requestPermission should update permission state and return result', async () => {
    const { result } = renderHook(() => useLocationPermission());

    // Wait for initial check to complete
    await waitFor(() => expect(result.current.isLoading).toBe(false));

    (requestLocationPermission as jest.Mock).mockResolvedValueOnce({
      granted: true,
      canAskAgain: false,
      status: 'granted',
      expires: 'never',
    });

    let permissionResult: Location.PermissionResponse | null = null;
    await act(async () => {
      permissionResult = await result.current.requestPermission();
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.permission?.granted).toBe(true);
    expect(permissionResult?.granted).toBe(true);
    expect(requestLocationPermission).toHaveBeenCalledTimes(1);
  });

  it('should handle errors during checkPermission', async () => {
    (checkLocationPermission as jest.Mock).mockRejectedValueOnce(new Error('Check error'));

    const { result } = renderHook(() => useLocationPermission());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.permission).toBeNull();
    });
    expect(console.error).toHaveBeenCalledWith('Error checking location permission:', expect.any(Error));
  });

  it('should handle errors during requestPermission', async () => {
    const mockError = new Error('Request error');
    (requestLocationPermission as jest.Mock).mockRejectedValueOnce(mockError);

    const { result } = renderHook(() => useLocationPermission());

    // Wait for initial check to complete
    await waitFor(() => expect(result.current.isLoading).toBe(false));

    let caughtError: any;
    await act(async () => {
      try {
        await result.current.requestPermission();
      } catch (e) {
        caughtError = e;
      }
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.permission).toBeNull();
    expect(caughtError).toBe(mockError);
    expect(console.error).toHaveBeenCalledWith('Error requesting location permission:', expect.any(Error));
  });
});