import { DiscoveryService } from '@/src/services/discovery/DiscoveryService';
import { supabase } from '@/src/config/supabase';
import { getSecureBusinessProfiles } from '@/src/services/discovery/utils/secureBusinessProfiles';

// Mock external dependencies
jest.mock('@/src/config/supabase');
jest.mock('@/src/services/discovery/utils/secureBusinessProfiles');

describe('DiscoveryService', () => {
  let discoveryService: DiscoveryService;

  beforeEach(() => {
    jest.clearAllMocks();
    discoveryService = new DiscoveryService();

    // Mock supabase.from chainable methods
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      ilike: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      // Default mock for execute
      then: jest.fn((resolve) => resolve({ data: [], count: 0, error: null })),
    });

    // Mock getSecureBusinessProfiles
    (getSecureBusinessProfiles as jest.Mock).mockResolvedValue({
      data: [],
      count: 0,
      error: null,
    });
  });

  describe('search', () => {
    it('should call searchProducts when viewType is products', async () => {
      const searchProductsSpy = jest.spyOn(discoveryService as any, 'searchProducts');
      const params = { viewType: 'products' as const };
      await discoveryService.search(params);
      expect(searchProductsSpy).toHaveBeenCalledTimes(1);
      expect(searchProductsSpy).toHaveBeenCalledWith(expect.objectContaining({ viewType: 'products' }));
    });

    it('should call searchBusinesses when viewType is cards', async () => {
      const searchBusinessesSpy = jest.spyOn(discoveryService as any, 'searchBusinesses');
      const params = { viewType: 'cards' as const };
      await discoveryService.search(params);
      expect(searchBusinessesSpy).toHaveBeenCalledTimes(1);
      expect(searchBusinessesSpy).toHaveBeenCalledWith(expect.objectContaining({ viewType: 'cards' }));
    });

    it('should return an error if an unexpected error occurs', async () => {
      jest.spyOn(discoveryService as any, 'validateAndNormalizeParams').mockImplementation(() => {
        throw new Error('Validation error');
      });
      const params = { viewType: 'cards' as const };
      const result = await discoveryService.search(params);
      expect(result.error).toBe('An unexpected error occurred during search');
    });
  });

  describe('searchProducts', () => {
    it('should return empty data if no valid business IDs are found', async () => {
      jest.spyOn(discoveryService as any, 'getValidBusinessIds').mockResolvedValue([]);
      const params = { viewType: 'products' as const, page: 1, limit: 10 };
      const result = await discoveryService.search(params);
      expect(result.data?.products).toEqual([]);
      expect(result.data?.totalCount).toBe(0);
      expect(result.data?.hasMore).toBe(false);
    });

    it('should fetch and process products correctly', async () => {
      jest.spyOn(discoveryService as any, 'getValidBusinessIds').mockResolvedValue(['biz1']);
      jest.spyOn(discoveryService as any, 'countProducts').mockResolvedValue(1);
      jest.spyOn(discoveryService as any, 'fetchProducts').mockResolvedValue([
        { id: 'prod1', name: 'Product 1', business_id: 'biz1', business_profiles: { business_slug: 'biz-1' } },
      ]);

      const params = { viewType: 'products' as const, page: 1, limit: 10 };
      const result = await discoveryService.search(params);

      expect(result.data?.products).toEqual([
        expect.objectContaining({ id: 'prod1', name: 'Product 1', business_slug: 'biz-1' }),
      ]);
      expect(result.data?.totalCount).toBe(1);
      expect(result.data?.hasMore).toBe(false);
    });
  });

  describe('searchBusinesses', () => {
    it('should fetch and process businesses correctly', async () => {
      const mockBusinessData = [
        { id: 'biz1', business_name: 'Business 1', status: 'online' },
      ];
      (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
        if (tableName === 'business_profiles') {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            ilike: jest.fn().mockReturnThis(),
            order: jest.fn().mockReturnThis(),
            range: jest.fn().mockReturnThis(),
            then: jest.fn((resolve) => resolve({ data: mockBusinessData, count: 1, error: null })),
          };
        }
        return jest.fn();
      });

      const params = { viewType: 'cards' as const, page: 1, limit: 10 };
      const result = await discoveryService.search(params);

      expect(result.data?.businesses).toEqual([
        expect.objectContaining({ id: 'biz1', business_name: 'Business 1' }),
      ]);
      expect(result.data?.totalCount).toBe(1);
      expect(result.data?.hasMore).toBe(false);
    });
  });

  describe('validateAndNormalizeParams', () => {
    it('should normalize parameters correctly', () => {
      const params = {
        viewType: 'cards',
        searchTerm: '  Test  ',
        category: '  Food  ',
        pincode: ' 12345 ',
        city: '  City  ',
        locality: ' Locality ',
        page: 0,
        limit: 100,
        businessSort: 'name_asc',
        productSort: 'price_low',
        productType: 'physical',
      };
      const normalized = (discoveryService as any).validateAndNormalizeParams(params);
      expect(normalized).toEqual({
        viewType: 'cards',
        searchTerm: 'Test',
        category: 'Food',
        pincode: '12345',
        city: 'City',
        locality: 'Locality',
        page: 1,
        limit: 50,
        businessSort: 'name_asc',
        productSort: 'price_low',
        productType: 'physical',
      });
    });

    it('should handle null/undefined string parameters', () => {
      const params = {
        viewType: 'products',
        searchTerm: null,
        category: undefined,
        pincode: '',
        city: ' ',
        locality: '\t\n',
        page: 1,
        limit: 20,
        businessSort: 'created_desc',
        productSort: 'newest',
        productType: null,
      };
      const normalized = (discoveryService as any).validateAndNormalizeParams(params);
      expect(normalized.searchTerm).toBeNull();
      expect(normalized.category).toBeNull();
      expect(normalized.pincode).toBeNull();
      expect(normalized.city).toBeNull();
      expect(normalized.locality).toBeNull();
    });
  });

  describe('processBusinessData', () => {
    it('should correctly map raw business data to BusinessCardData', () => {
      const rawData = [
        {
          id: '1',
          business_name: 'Biz 1',
          subscription_status: 'active',
          logo_url: 'logo1.png',
          status: 'online',
          latitude: 10,
          longitude: 20,
        },
      ];
      const processed = (discoveryService as any).processBusinessData(rawData);
      expect(processed[0]).toEqual(expect.objectContaining({
        id: '1',
        business_name: 'Biz 1',
        has_active_subscription: true,
        logo_url: 'logo1.png',
        status: 'online',
        latitude: 10,
        longitude: 20,
      }));
    });
  });

  describe('processProductData', () => {
    it('should correctly map raw product data to NearbyProduct', () => {
      const rawData = [
        {
          id: 'p1',
          business_id: 'b1',
          name: 'Product 1',
          business_profiles: { business_slug: 'biz-slug', latitude: 30, longitude: 40 },
        },
      ];
      const processed = (discoveryService as any).processProductData(rawData);
      expect(processed[0]).toEqual(expect.objectContaining({
        id: 'p1',
        business_id: 'b1',
        name: 'Product 1',
        business_slug: 'biz-slug',
        businessLatitude: 30,
        businessLongitude: 40,
      }));
    });
  });
});