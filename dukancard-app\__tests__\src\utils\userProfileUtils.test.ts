import { checkUserProfile } from '@/src/utils/userProfileUtils';
import { supabase } from '@/lib/supabase';

// Mock external dependencies
jest.mock('@/lib/supabase');

describe('checkUserProfile', () => {
  const mockUserId = 'test-user-id';

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(console, 'error').mockImplementation(() => {}); // Suppress console.error during tests
  });

  it('should return business profile if it exists', async () => {
    const mockBusinessProfile = { id: mockUserId, business_name: 'Test Business' };
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'business_profiles') {
        return {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          maybeSingle: jest.fn().mockResolvedValue({ data: mockBusinessProfile, error: null }),
        };
      }
      return {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
      };
    });

    const result = await checkUserProfile(mockUserId, supabase);
    expect(result).toEqual({ type: 'business', profile: mockBusinessProfile });
    expect(supabase.from).toHaveBeenCalledWith('business_profiles');
    expect(supabase.from('business_profiles').eq).toHaveBeenCalledWith('id', mockUserId);
  });

  it('should return customer profile if business profile does not exist', async () => {
    const mockCustomerProfile = { id: mockUserId, full_name: 'Test Customer' };

    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'business_profiles') {
        return {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
        };
      } else if (tableName === 'customer_profiles') {
        return {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          maybeSingle: jest.fn().mockResolvedValue({ data: mockCustomerProfile, error: null }),
        };
      }
      return {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
      };
    });

    const result = await checkUserProfile(mockUserId, supabase);
    expect(result).toEqual({ type: 'customer', profile: mockCustomerProfile });
    expect(supabase.from).toHaveBeenCalledWith('business_profiles');
    expect(supabase.from).toHaveBeenCalledWith('customer_profiles');
    expect(supabase.from('customer_profiles').eq).toHaveBeenCalledWith('id', mockUserId);
  });

  it('should return none if no profile exists', async () => {
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      return {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
      };
    });

    const result = await checkUserProfile(mockUserId, supabase);
    expect(result).toEqual({ type: 'none', profile: null });
  });

  it('should return error if fetching business profile fails', async () => {
    const mockError = { message: 'Business DB Error' };
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'business_profiles') {
        return {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          maybeSingle: jest.fn().mockResolvedValue({ data: null, error: mockError }),
        };
      }
      return {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
      };
    });

    const result = await checkUserProfile(mockUserId, supabase);
    expect(result).toEqual({ type: 'error', error: mockError.message });
    expect(console.error).toHaveBeenCalledWith('Error checking business profile:', mockError);
  });

  it('should return error if fetching customer profile fails', async () => {
    const mockError = { message: 'Customer DB Error' };
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'business_profiles') {
        return {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
        };
      } else if (tableName === 'customer_profiles') {
        return {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          maybeSingle: jest.fn().mockResolvedValue({ data: null, error: mockError }),
        };
      }
      return {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
      };
    });

    const result = await checkUserProfile(mockUserId, supabase);
    expect(result).toEqual({ type: 'error', error: mockError.message });
    expect(console.error).toHaveBeenCalledWith('Error checking customer profile:', mockError);
  });

  it('should handle unexpected errors', async () => {
    (supabase.from as jest.Mock).mockImplementation(() => {
      throw new Error('Unexpected error');
    });

    const result = await checkUserProfile(mockUserId, supabase);
    expect(result).toEqual({ type: 'error', error: 'Unexpected error' });
    expect(console.error).toHaveBeenCalledWith('Unexpected error in checkUserProfile:', expect.any(Error));
  });
});