import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ExternalLink } from '../../../src/components/ExternalLink';
import { openBrowserAsync } from 'expo-web-browser';
import { Platform } from 'react-native';

// Mock expo-router's Link component
jest.mock('expo-router', () => ({
  Link: ({ href, onPress, children }: any) => (
    <mock-link onPress={onPress} href={href}>
      {children}
    </mock-link>
  ),
}));

// Mock expo-web-browser
jest.mock('expo-web-browser', () => ({
  openBrowserAsync: jest.fn(),
}));

describe('ExternalLink', () => {
  const mockHref = 'https://example.com';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with children', () => {
    const { getByText } = render(
      <ExternalLink href={mockHref}>Click Me</ExternalLink>
    );
    expect(getByText('Click Me')).toBeTruthy();
  });

  it('calls openBrowserAsync on press for native platforms', async () => {
    Platform.OS = 'ios'; // Simulate native platform

    const { getByTestId } = render(
      <ExternalLink href={mockHref} testID="external-link">
        Click Me
      </ExternalLink>
    );

    fireEvent.press(getByTestId('external-link'));

    expect(openBrowserAsync).toHaveBeenCalledWith(mockHref);
  });

  it('does not call openBrowserAsync on press for web platform', async () => {
    Platform.OS = 'web'; // Simulate web platform

    const { getByTestId } = render(
      <ExternalLink href={mockHref} testID="external-link">
        Click Me
      </ExternalLink>
    );

    fireEvent.press(getByTestId('external-link'));

    expect(openBrowserAsync).not.toHaveBeenCalled();
  });
});
