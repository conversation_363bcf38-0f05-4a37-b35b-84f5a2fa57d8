import React from 'react';
import { render } from '@testing-library/react-native';
import ParallaxScrollView from '@/src/components/ParallaxScrollView';
import Animated, {
  useAnimatedRef,
  useAnimatedStyle,
  useScrollViewOffset,
} from 'react-native-reanimated';
import { ThemedView } from '@/src/components/ThemedView';
import { useBottomTabOverflow } from '@/src/components/ui/TabBarBackground';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { HEADER_HEIGHT } from '@/styles/components/ParallaxScrollView-styles';
import { Text, View } from 'react-native';

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => ({
  useAnimatedRef: jest.fn(() => ({ current: null })),
  useAnimatedStyle: jest.fn(() => ({})), // Mock to return an empty style object
  useScrollViewOffset: jest.fn(() => ({ value: 0 })), // Mock initial scroll offset
  interpolate: jest.fn((value, inputRange, outputRange) => value), // Simple passthrough
}));

// Mock ThemedView
jest.mock('@/src/components/ThemedView', () => ({
  ThemedView: ({ children, style }: any) => <View style={style}>{children}</View>,
}));

// Mock useBottomTabOverflow
jest.mock('@/src/components/ui/TabBarBackground', () => ({
  useBottomTabOverflow: jest.fn(() => 0),
}));

// Mock useColorScheme
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: jest.fn(() => 'light'),
}));

// Mock styles
jest.mock('@/styles/components/ParallaxScrollView-styles', () => ({
  createParallaxScrollViewStyles: () => ({
    container: { flex: 1 },
    header: { height: 200 },
    content: { padding: 20 },
  }),
  HEADER_HEIGHT: 200,
}));

describe('ParallaxScrollView', () => {
  const defaultProps = {
    headerImage: <Text testID="header-image">Header Image</Text>,
    headerBackgroundColor: { dark: '#000', light: '#fff' },
  };

  it('renders children and header image', () => {
    const { getByText, getByTestId } = render(
      <ParallaxScrollView {...defaultProps}>
        <Text>Scrollable Content</Text>
      </ParallaxScrollView>
    );

    expect(getByText('Scrollable Content')).toBeTruthy();
    expect(getByTestId('header-image')).toBeTruthy();
  });

  it('applies correct header background color based on color scheme', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    const { getByTestId } = render(
      <ParallaxScrollView {...defaultProps} testID="parallax-scroll-view" />
    );

    // The header background color is applied to the Animated.View inside the ScrollView
    // We need to find that specific Animated.View
    const headerView = getByTestId('parallax-scroll-view').findByProps({
      style: expect.arrayContaining([
        expect.objectContaining({ backgroundColor: '#000' }),
      ]),
    });
    expect(headerView.props.style[1].backgroundColor).toBe('#000');
  });

  it('calls reanimated hooks', () => {
    render(<ParallaxScrollView {...defaultProps} />);
    expect(useAnimatedRef).toHaveBeenCalled();
    expect(useAnimatedStyle).toHaveBeenCalled();
    expect(useScrollViewOffset).toHaveBeenCalled();
  });
});
