import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { ComingSoon } from '@/src/components/ui/ComingSoon';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Rocket, Clock, Star } from 'lucide-react-native';

// Mock the hooks and icons
jest.mock('@/src/hooks/useColorScheme');
jest.mock('lucide-react-native', () => ({
  Rocket: jest.fn(() => null),
  Clock: jest.fn(() => null),
  Star: jest.fn(() => null),
}));

describe('ComingSoon', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (Rocket as jest.Mock).mockImplementation(() => <Text testID="rocket-icon">RocketIcon</Text>);
    (Clock as jest.Mock).mockImplementation(() => <Text testID="clock-icon">ClockIcon</Text>);
    (Star as jest.Mock).mockImplementation(() => <Text testID="star-icon">StarIcon</Text>);
  });

  it('renders with default title and description', () => {
    render(<ComingSoon />);
    expect(screen.getByText('Coming Soon')).toBeOnTheScreen();
    expect(screen.getByText("We're working hard to bring you something amazing. Stay tuned!")).toBeOnTheScreen();
    expect(screen.getByTestId('rocket-icon')).toBeOnTheScreen();
  });

  it('renders with custom title and description', () => {
    render(<ComingSoon title="New Feature" description="Get ready!" />);
    expect(screen.getByText('New Feature')).toBeOnTheScreen();
    expect(screen.getByText('Get ready!')).toBeOnTheScreen();
  });

  it('renders Clock icon when icon prop is 'clock'', () => {
    render(<ComingSoon icon="clock" />);
    expect(screen.getByTestId('clock-icon')).toBeOnTheScreen();
    expect(screen.queryByTestId('rocket-icon')).toBeNull();
  });

  it('renders Star icon when icon prop is 'star'', () => {
    render(<ComingSoon icon="star" />);
    expect(screen.getByTestId('star-icon')).toBeOnTheScreen();
    expect(screen.queryByTestId('rocket-icon')).toBeNull();
  });

  it('applies dark mode styles', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    render(<ComingSoon />);
    // Check for dark mode specific styles (e.g., background color, text color)
    // This is a simplified check, a snapshot test would be more comprehensive for styles.
    expect(screen.getByText('Coming Soon').props.style).toContainEqual({
      color: '#FFFFFF',
    });
    expect(screen.getByText("We're working hard to bring you something amazing. Stay tuned!").props.style).toContainEqual({
      color: '#999999',
    });
  });
});