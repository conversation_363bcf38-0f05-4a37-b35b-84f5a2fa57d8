import { 
  handlePostCreationFeed,
  createPostCreationState,
  isPostCreationStateValid,
  clearPostCreationState,
  processFeedWithCreationHandling,
  PostCreationStateManager,
  markPostAsJustCreated,
  shouldShowJustPostedIndicator,
} from '@/src/utils/feed/postCreationHandler';

// Mock UnifiedPost type for testing
interface MockUnifiedPost {
  id: string;
  author_id: string;
  created_at: string;
  post_source: 'customer' | 'business';
  business_plan?: string;
}

describe('postCreationHandler', () => {
  const mockAlgorithmicPosts: MockUnifiedPost[] = [
    { id: 'p1', author_id: 'a1', created_at: '2023-01-01T10:00:00Z', post_source: 'customer' },
    { id: 'p2', author_id: 'a2', created_at: '2023-01-01T11:00:00Z', post_source: 'business' },
    { id: 'p3', author_id: 'a3', created_at: '2023-01-01T12:00:00Z', post_source: 'customer' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock sessionStorage
    const sessionStorageMock = (() => {
      let store: { [key: string]: string } = {};
      return {
        getItem: (key: string) => store[key] || null,
        setItem: (key: string, value: string) => {
          store[key] = value.toString();
        },
        removeItem: (key: string) => {
          delete store[key];
        },
        clear: () => {
          store = {};
        },
      };
    })();

    Object.defineProperty(window, 'sessionStorage', {
      value: sessionStorageMock,
    });

    // Mock Date.now() for consistent session IDs
    jest.spyOn(Date, 'now').mockReturnValue(1672531200000); // Jan 1, 2023 00:00:00 GMT
  });

  describe('handlePostCreationFeed', () => {
    it('should return algorithmic posts if no justCreatedPostId', () => {
      const creationState = {};
      const result = handlePostCreationFeed(mockAlgorithmicPosts, creationState);
      expect(result.posts).toEqual(mockAlgorithmicPosts);
      expect(result.hasJustCreatedPost).toBe(false);
    });

    it('should return algorithmic posts if justCreatedPostId not found in algorithmicPosts', () => {
      const creationState = { justCreatedPostId: 'p99' };
      const result = handlePostCreationFeed(mockAlgorithmicPosts, creationState);
      expect(result.posts).toEqual(mockAlgorithmicPosts);
      expect(result.hasJustCreatedPost).toBe(false);
    });

    it('should place justCreatedPost at the top of the feed', () => {
      const creationState = { justCreatedPostId: 'p2' };
      const result = handlePostCreationFeed(mockAlgorithmicPosts, creationState);
      expect(result.posts[0].id).toBe('p2');
      expect(result.posts.length).toBe(mockAlgorithmicPosts.length);
      expect(result.hasJustCreatedPost).toBe(true);
      expect(result.justCreatedPost).toEqual(mockAlgorithmicPosts[1]);
    });

    it('should maintain order of other posts', () => {
      const creationState = { justCreatedPostId: 'p2' };
      const result = handlePostCreationFeed(mockAlgorithmicPosts, creationState);
      expect(result.posts[1].id).toBe('p1');
      expect(result.posts[2].id).toBe('p3');
    });
  });

  describe('createPostCreationState', () => {
    it('should create a post creation state object', () => {
      const state = createPostCreationState('newPost1', 'session123');
      expect(state.justCreatedPostId).toBe('newPost1');
      expect(state.sessionId).toBe('session123');
      expect(state.createdAt).toBeDefined();
    });

    it('should generate sessionId if not provided', () => {
      const state = createPostCreationState('newPost2');
      expect(state.sessionId).toMatch(/^session_\d+_\w+$/);
    });
  });

  describe('isPostCreationStateValid', () => {
    it('should return false if no justCreatedPostId', () => {
      expect(isPostCreationStateValid({})).toBe(false);
    });

    it('should return true if sessionIds match', () => {
      const state = { justCreatedPostId: 'p1', sessionId: 's1' };
      expect(isPostCreationStateValid(state, 's1')).toBe(true);
    });

    it('should return false if sessionIds do not match', () => {
      const state = { justCreatedPostId: 'p1', sessionId: 's1' };
      expect(isPostCreationStateValid(state, 's2')).toBe(false);
    });

    it('should return true if created recently (within 5 minutes) as fallback', () => {
      const fiveMinutesAgo = new Date(Date.now() - 4 * 60 * 1000).toISOString();
      const state = { justCreatedPostId: 'p1', createdAt: fiveMinutesAgo };
      expect(isPostCreationStateValid(state)).toBe(true);
    });

    it('should return false if not created recently and no session ID match', () => {
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString();
      const state = { justCreatedPostId: 'p1', createdAt: tenMinutesAgo };
      expect(isPostCreationStateValid(state)).toBe(false);
    });
  });

  describe('clearPostCreationState', () => {
    it('should return an empty object', () => {
      expect(clearPostCreationState()).toEqual({});
    });
  });

  describe('processFeedWithCreationHandling', () => {
    it('should return normal feed if no creationState', () => {
      const result = processFeedWithCreationHandling(mockAlgorithmicPosts, 3, false);
      expect(result.data?.items).toEqual(mockAlgorithmicPosts);
      expect(result.data?.hasJustCreatedPost).toBe(false);
    });

    it('should return feed with new post at top if creationState is valid', () => {
      const newPost: MockUnifiedPost = { id: 'new1', author_id: 'new_a', created_at: '2023-01-01T13:00:00Z', post_source: 'customer' };
      const algorithmicPostsWithNew = [mockAlgorithmicPosts[0], newPost, mockAlgorithmicPosts[1], mockAlgorithmicPosts[2]];
      const creationState = { justCreatedPostId: 'new1' };

      const result = processFeedWithCreationHandling(algorithmicPostsWithNew, 4, false, creationState);
      expect(result.data?.items[0].id).toBe('new1');
      expect(result.data?.hasJustCreatedPost).toBe(true);
      expect(result.data?.justCreatedPost).toEqual(newPost);
    });
  });

  describe('PostCreationStateManager', () => {
    it('should save and load state', () => {
      const state = { justCreatedPostId: 'test1', sessionId: 's1', createdAt: 'now' };
      PostCreationStateManager.save(state);
      const loadedState = PostCreationStateManager.load();
      expect(loadedState).toEqual(state);
    });

    it('should clear state', () => {
      PostCreationStateManager.save({ justCreatedPostId: 'test1' });
      PostCreationStateManager.clear();
      expect(PostCreationStateManager.load()).toEqual({});
    });

    it('should validate and clean state', () => {
      // Invalid state (old timestamp)
      const oldState = { justCreatedPostId: 'old', createdAt: new Date(Date.now() - 10 * 60 * 1000).toISOString() };
      PostCreationStateManager.save(oldState);
      let cleanedState = PostCreationStateManager.validateAndClean();
      expect(cleanedState).toEqual({});

      // Valid state
      const newState = { justCreatedPostId: 'new', createdAt: new Date().toISOString() };
      PostCreationStateManager.save(newState);
      cleanedState = PostCreationStateManager.validateAndClean();
      expect(cleanedState.justCreatedPostId).toBe('new');
    });

    it('should get or create current session ID', () => {
      const sessionId1 = PostCreationStateManager.getCurrentSessionId();
      const sessionId2 = PostCreationStateManager.getCurrentSessionId();
      expect(sessionId1).toBe(sessionId2);

      sessionStorage.clear();
      const sessionId3 = PostCreationStateManager.getCurrentSessionId();
      expect(sessionId3).not.toBe(sessionId1);
    });
  });

  describe('markPostAsJustCreated', () => {
    it('should save the post creation state', () => {
      markPostAsJustCreated('newPostId');
      const loadedState = PostCreationStateManager.load();
      expect(loadedState.justCreatedPostId).toBe('newPostId');
      expect(loadedState.sessionId).toBeDefined();
      expect(loadedState.createdAt).toBeDefined();
    });
  });

  describe('shouldShowJustPostedIndicator', () => {
    it('should return true if post matches just created post', () => {
      const post: MockUnifiedPost = { id: 'p1', author_id: 'a1', created_at: '' , post_source: 'customer'};
      const creationState = { justCreatedPostId: 'p1' };
      expect(shouldShowJustPostedIndicator(post, creationState)).toBe(true);
    });

    it('should return false if post does not match just created post', () => {
      const post: MockUnifiedPost = { id: 'p1', author_id: 'a1', created_at: '' , post_source: 'customer'};
      const creationState = { justCreatedPostId: 'p2' };
      expect(shouldShowJustPostedIndicator(post, creationState)).toBe(false);
    });

    it('should return false if no creation state', () => {
      const post: MockUnifiedPost = { id: 'p1', author_id: 'a1', created_at: '' , post_source: 'customer'};
      expect(shouldShowJustPostedIndicator(post, undefined)).toBe(false);
    });
  });
});