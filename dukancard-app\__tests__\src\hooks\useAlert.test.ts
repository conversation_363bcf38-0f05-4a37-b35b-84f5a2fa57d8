import { renderHook, act } from '@testing-library/react-hooks';
import { useAlert } from '@/src/hooks/useAlert';
import { AlertDialog, AlertButton } from '@/src/components/ui/AlertDialog';

// Mock the AlertDialog component
jest.mock('@/src/components/ui/AlertDialog', () => ({
  AlertDialog: jest.fn(() => null), // Mock AlertDialog to return null
}));

describe('useAlert', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('initializes with alertState visible set to false', () => {
    const { result } = renderHook(() => useAlert());
    expect(result.current.alertState.visible).toBe(false);
  });

  it('showAlert sets alertState visible to true and passes options', () => {
    const { result } = renderHook(() => useAlert());
    const mockButtons: AlertButton[] = [{ text: 'OK', onPress: jest.fn() }];

    act(() => {
      result.current.showAlert({
        type: 'info',
        title: 'Test Title',
        message: 'Test Message',
        buttons: mockButtons,
      });
    });

    expect(result.current.alertState.visible).toBe(true);
    expect(result.current.alertState.title).toBe('Test Title');
    expect(result.current.alertState.message).toBe('Test Message');
    expect(result.current.alertState.buttons).toEqual(mockButtons);
    expect(AlertDialog).toHaveBeenCalledWith(
      expect.objectContaining({
        visible: true,
        title: 'Test Title',
        message: 'Test Message',
        buttons: mockButtons,
      }),
      {}
    );
  });

  it('hideAlert sets alertState visible to false', () => {
    const { result } = renderHook(() => useAlert());
    act(() => {
      result.current.showAlert({ title: 'Test', buttons: [] });
    });
    expect(result.current.alertState.visible).toBe(true);

    act(() => {
      result.current.hideAlert();
    });
    expect(result.current.alertState.visible).toBe(false);
  });

  it('showSuccess sets correct type and button', () => {
    const { result } = renderHook(() => useAlert());
    const onOkMock = jest.fn();

    act(() => {
      result.current.showSuccess('Success Title', 'Success Message', onOkMock);
    });

    expect(result.current.alertState.type).toBe('success');
    expect(result.current.alertState.title).toBe('Success Title');
    expect(result.current.alertState.message).toBe('Success Message');
    expect(result.current.alertState.buttons?.length).toBe(1);
    expect(result.current.alertState.buttons?.[0].text).toBe('OK');

    // Simulate pressing OK button
    act(() => {
      result.current.alertState.buttons?.[0].onPress();
    });
    expect(onOkMock).toHaveBeenCalledTimes(1);
    expect(result.current.alertState.visible).toBe(false);
  });

  it('showError sets correct type and button', () => {
    const { result } = renderHook(() => useAlert());
    act(() => {
      result.current.showError('Error Title', 'Error Message');
    });
    expect(result.current.alertState.type).toBe('error');
    expect(result.current.alertState.title).toBe('Error Title');
    expect(result.current.alertState.message).toBe('Error Message');
  });

  it('showWarning sets correct type and button', () => {
    const { result } = renderHook(() => useAlert());
    act(() => {
      result.current.showWarning('Warning Title', 'Warning Message');
    });
    expect(result.current.alertState.type).toBe('warning');
    expect(result.current.alertState.title).toBe('Warning Title');
    expect(result.current.alertState.message).toBe('Warning Message');
  });

  it('showInfo sets correct type and button', () => {
    const { result } = renderHook(() => useAlert());
    act(() => {
      result.current.showInfo('Info Title', 'Info Message');
    });
    expect(result.current.alertState.type).toBe('info');
    expect(result.current.alertState.title).toBe('Info Title');
    expect(result.current.alertState.message).toBe('Info Message');
  });

  it('showConfirm sets correct type and buttons', () => {
    const { result } = renderHook(() => useAlert());
    const onConfirmMock = jest.fn();
    const onCancelMock = jest.fn();

    act(() => {
      result.current.showConfirm(
        'Confirm Title',
        'Confirm Message',
        onConfirmMock,
        onCancelMock,
        { confirmText: 'Yes', cancelText: 'No', type: 'question', destructive: true }
      );
    });

    expect(result.current.alertState.type).toBe('question');
    expect(result.current.alertState.title).toBe('Confirm Title');
    expect(result.current.alertState.message).toBe('Confirm Message');
    expect(result.current.alertState.buttons?.length).toBe(2);
    expect(result.current.alertState.buttons?.[0].text).toBe('No');
    expect(result.current.alertState.buttons?.[0].style).toBe('cancel');
    expect(result.current.alertState.buttons?.[1].text).toBe('Yes');
    expect(result.current.alertState.buttons?.[1].style).toBe('destructive');

    // Simulate pressing confirm button
    act(() => {
      result.current.alertState.buttons?.[1].onPress();
    });
    expect(onConfirmMock).toHaveBeenCalledTimes(1);
    expect(result.current.alertState.visible).toBe(false);

    // Re-show and simulate pressing cancel button
    act(() => {
      result.current.showConfirm('Confirm Title', 'Confirm Message', onConfirmMock, onCancelMock);
    });
    act(() => {
      result.current.alertState.buttons?.[0].onPress();
    });
    expect(onCancelMock).toHaveBeenCalledTimes(1);
  });

  it('showLogout sets correct title, message and buttons', () => {
    const { result } = renderHook(() => useAlert());
    act(() => {
      result.current.showLogout();
    });
    expect(result.current.alertState.type).toBe('warning');
    expect(result.current.alertState.title).toBe('Logout');
    expect(result.current.alertState.message).toContain('Are you sure you want to logout?');
    expect(result.current.alertState.buttons?.[0].text).toBe('Cancel');
    expect(result.current.alertState.buttons?.[1].text).toBe('Logout');
  });

  it('showDelete sets correct title, message and buttons', () => {
    const { result } = renderHook(() => useAlert());
    act(() => {
      result.current.showDelete('Item');
    });
    expect(result.current.alertState.type).toBe('error');
    expect(result.current.alertState.title).toBe('Delete Item');
    expect(result.current.alertState.message).toContain('Are you sure you want to delete this item?');
    expect(result.current.alertState.buttons?.[0].text).toBe('Cancel');
    expect(result.current.alertState.buttons?.[1].text).toBe('Delete');

    act(() => {
      result.current.showDelete('Product', jest.fn(), jest.fn(), 'Custom delete message.');
    });
    expect(result.current.alertState.title).toBe('Delete Product');
    expect(result.current.alertState.message).toBe('Custom delete message.');
  });
});