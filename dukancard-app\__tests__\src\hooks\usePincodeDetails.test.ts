import { renderHook, act, waitFor } from '@testing-library/react-hooks';
import { usePincodeDetails, PincodeDetails } from '@/src/hooks/usePincodeDetails';
import { supabase } from '@/lib/supabase';

// Mock external dependencies
jest.mock('@/lib/supabase');

describe('usePincodeDetails', () => {
  const mockOnPincodeChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock supabase chainable methods
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      // Default successful response
      single: jest.fn().mockResolvedValue({
        data: {
          OfficeName: 'Test Locality',
          DivisionName: 'Test City',
          StateName: 'Test State',
        },
        error: null,
      }),
    });
  });

  it('should return initial state', () => {
    const { result } = renderHook(() => usePincodeDetails());
    expect(result.current.isPincodeLoading).toBe(false);
    expect(result.current.availableLocalities).toEqual([]);
  });

  it('should fetch and set pincode details on valid pincode change', async () => {
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockResolvedValue({
        data: [
          { OfficeName: 'Locality A', DivisionName: 'City A', StateName: 'State A' },
          { OfficeName: 'Locality B', DivisionName: 'City A', StateName: 'State A' },
        ],
        error: null,
      }),
    });

    const { result } = renderHook(() => usePincodeDetails({ onPincodeChange: mockOnPincodeChange }));

    await act(async () => {
      await result.current.handlePincodeChange('123456');
    });

    expect(result.current.isPincodeLoading).toBe(false);
    expect(result.current.availableLocalities).toEqual(['Locality A', 'Locality B']);
    expect(mockOnPincodeChange).toHaveBeenCalledWith({
      city: 'City A',
      state: 'State A',
      localities: ['Locality A', 'Locality B'],
    });
  });

  it('should handle invalid pincode format', async () => {
    const { result } = renderHook(() => usePincodeDetails({ onPincodeChange: mockOnPincodeChange }));

    await act(async () => {
      await result.current.handlePincodeChange('123');
    });

    expect(result.current.isPincodeLoading).toBe(false);
    expect(result.current.availableLocalities).toEqual([]);
    expect(mockOnPincodeChange).toHaveBeenCalledWith(null);
  });

  it('should handle pincode not found', async () => {
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockResolvedValue({
        data: [],
        error: null,
      }),
    });

    const { result } = renderHook(() => usePincodeDetails({ onPincodeChange: mockOnPincodeChange }));

    await act(async () => {
      await result.current.handlePincodeChange('999999');
    });

    expect(result.current.isPincodeLoading).toBe(false);
    expect(result.current.availableLocalities).toEqual([]);
    expect(mockOnPincodeChange).toHaveBeenCalledWith(null);
  });

  it('should handle database error', async () => {
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockResolvedValue({
        data: null,
        error: { message: 'DB Error' },
      }),
    });

    const { result } = renderHook(() => usePincodeDetails({ onPincodeChange: mockOnPincodeChange }));

    await act(async () => {
      await result.current.handlePincodeChange('123456');
    });

    expect(result.current.isPincodeLoading).toBe(false);
    expect(result.current.availableLocalities).toEqual([]);
    expect(mockOnPincodeChange).toHaveBeenCalledWith(null);
  });
});