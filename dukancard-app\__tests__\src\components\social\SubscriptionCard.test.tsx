import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { Alert, ActivityIndicator } from 'react-native';
import { SubscriptionCard } from '@/src/components/social/SubscriptionCard';
import { useRouter } from 'expo-router';
import { useToast } from '@/src/components/ui/Toast';

// Mock necessary modules
jest.mock('expo-router');
jest.mock('@/src/components/ui/Toast');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
  ActivityIndicator: jest.fn(() => null), // Mock ActivityIndicator to return null
}));

const mockSubscription = {
  id: 'sub1',
  business_profiles: {
    id: 'business1',
    business_name: 'Test Business',
    business_slug: 'test-business',
    logo_url: 'https://example.com/logo.jpg',
    locality: 'Test Locality',
    city: 'Test City',
    state: 'Test State',
  },
};

describe('SubscriptionCard', () => {
  const onUnsubscribeMock = jest.fn();
  const mockRouterPush = jest.fn();
  const mockToastError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockRouterPush });
    (useToast as jest.Mock).mockReturnValue({ error: mockToastError });
    (ActivityIndicator as jest.Mock).mockImplementation(({ color }) => <Text testID="activity-indicator" style={{ color }}>Loading...</Text>);
  });

  it('renders correctly with business data', () => {
    render(<SubscriptionCard subscription={mockSubscription} onUnsubscribe={onUnsubscribeMock} />);

    expect(screen.getByText('Test Business')).toBeOnTheScreen();
    expect(screen.getByText('Test Locality, Test City, Test State')).toBeOnTheScreen();
    expect(screen.getByText('Following')).toBeOnTheScreen();
  });

  it('displays business initials when no logo_url is provided', () => {
    const subWithoutLogo = {
      ...mockSubscription,
      business_profiles: { ...mockSubscription.business_profiles, logo_url: null },
    };
    render(<SubscriptionCard subscription={subWithoutLogo} onUnsubscribe={onUnsubscribeMock} />);

    expect(screen.getByText('TB')).toBeOnTheScreen();
  });

  it('navigates to business profile on touch', () => {
    render(<SubscriptionCard subscription={mockSubscription} onUnsubscribe={onUnsubscribeMock} />);
    fireEvent.press(screen.getByText('Test Business'));
    expect(mockRouterPush).toHaveBeenCalledWith('/business/test-business');
  });

  it('shows alert on unsubscribe button press', () => {
    render(<SubscriptionCard subscription={mockSubscription} onUnsubscribe={onUnsubscribeMock} />);
    fireEvent.press(screen.getByLabelText('Unsubscribe')); // Assuming an accessibilityLabel for the unsubscribe button
    expect(Alert.alert).toHaveBeenCalledWith(
      'Unsubscribe',
      'Are you sure you want to unsubscribe from Test Business?',
      expect.any(Array)
    );
  });

  it('calls onUnsubscribe when unsubscribe is confirmed', async () => {
    render(<SubscriptionCard subscription={mockSubscription} onUnsubscribe={onUnsubscribeMock} />);
    fireEvent.press(screen.getByLabelText('Unsubscribe'));

    // Simulate pressing the 'Unsubscribe' button in the alert
    const unsubscribeAction = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    await unsubscribeAction.onPress();

    expect(onUnsubscribeMock).toHaveBeenCalledWith('sub1');
  });

  it('shows activity indicator when unsubscribing', async () => {
    onUnsubscribeMock.mockImplementationOnce(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(<SubscriptionCard subscription={mockSubscription} onUnsubscribe={onUnsubscribeMock} />);
    fireEvent.press(screen.getByLabelText('Unsubscribe'));

    const unsubscribeAction = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    unsubscribeAction.onPress();

    expect(screen.getByTestId('activity-indicator')).toBeOnTheScreen();
    await screen.findByText('Test Business'); // Wait for unsubscribing to complete
    expect(screen.queryByTestId('activity-indicator')).toBeNull();
  });

  it('handles navigation error gracefully', () => {
    const subWithoutSlug = {
      ...mockSubscription,
      business_profiles: { ...mockSubscription.business_profiles, business_slug: null },
    };
    render(<SubscriptionCard subscription={subWithoutSlug} onUnsubscribe={onUnsubscribeMock} />);
    fireEvent.press(screen.getByText('Test Business'));
    expect(mockToastError).toHaveBeenCalledWith("Navigation Error", "Business profile not available");
    expect(mockRouterPush).not.toHaveBeenCalled();
  });
});