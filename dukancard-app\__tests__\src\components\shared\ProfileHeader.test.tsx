import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ProfileHeader } from '../../../../src/components/shared/ProfileHeader';

// Mock necessary modules
jest.mock('lucide-react-native', () => ({
  User: 'UserIcon',
  Heart: 'HeartIcon',
  Users: 'UsersIcon',
  Star: 'StarIcon',
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/lib/utils', () => ({
  formatIndianNumberShort: jest.fn((num) => {
    if (num >= 10000000) return `${(num / 10000000).toFixed(1)} Cr`;
    if (num >= 100000) return `${(num / 100000).toFixed(1)} L`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)} K`;
    return num.toString();
  }),
}));

jest.mock('./ProfileHeader.styles', () => ({
  createProfileHeaderStyles: jest.fn(() => ({
    profileHeaderContainer: {},
    avatarSection: {},
    largeAvatar: {},
    avatarImage: {},
    nameAndStatsContainer: {},
    profileName: {},
    statsSection: {},
    statItem: {},
    statValue: {},
    statLabel: {},
  })),
}));

describe('ProfileHeader', () => {
  const commonProps = {
    profileName: 'Test User',
    likesCount: 12345,
    followersCount: 67890,
    reviewsOrRatingsCount: 4.5,
    isBusinessProfile: false,
    onLikesPress: jest.fn(),
    onFollowingPress: jest.fn(),
    onReviewsPress: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with an avatar URL', () => {
    const { getByText, getByTestId } = render(
      <ProfileHeader {...commonProps} avatarUrl="https://example.com/avatar.jpg" />
    );
    expect(getByText('Test User')).toBeTruthy();
    expect(getByTestId('profile-avatar-image')).toBeTruthy();
  });

  it('renders correctly without an avatar URL (shows UserIcon)', () => {
    const { getByText, getByTestId } = render(
      <ProfileHeader {...commonProps} avatarUrl={null} />
    );
    expect(getByText('Test User')).toBeTruthy();
    expect(getByTestId('profile-avatar-placeholder')).toBeTruthy();
    expect(getByTestId('UserIcon')).toBeTruthy();
  });

  it('displays profile name and formatted stats', () => {
    const { getByText } = render(<ProfileHeader {...commonProps} />);
    expect(getByText('Test User')).toBeTruthy();
    expect(getByText('12.3 K')).toBeTruthy(); // Formatted likes
    expect(getByText('67.9 K')).toBeTruthy(); // Formatted followers
    expect(getByText('4.5')).toBeTruthy(); // Formatted reviews/ratings
  });

  it('displays correct labels for customer profile', () => {
    const { getByText } = render(<ProfileHeader {...commonProps} isBusinessProfile={false} />);
    expect(getByText('Likes')).toBeTruthy();
    expect(getByText('Following')).toBeTruthy();
    expect(getByText('Reviews')).toBeTruthy();
  });

  it('displays correct labels for business profile', () => {
    const { getByText } = render(<ProfileHeader {...commonProps} isBusinessProfile={true} />);
    expect(getByText('Likes')).toBeTruthy();
    expect(getByText('Followers')).toBeTruthy();
    expect(getByText('Rating')).toBeTruthy();
  });

  it('calls onLikesPress when Likes stat is pressed', () => {
    const { getByText } = render(<ProfileHeader {...commonProps} />);
    fireEvent.press(getByText('Likes'));
    expect(commonProps.onLikesPress).toHaveBeenCalledTimes(1);
  });

  it('calls onFollowingPress when Following/Followers stat is pressed', () => {
    const { getByText } = render(<ProfileHeader {...commonProps} />);
    fireEvent.press(getByText('Following'));
    expect(commonProps.onFollowingPress).toHaveBeenCalledTimes(1);
  });

  it('calls onReviewsPress when Reviews/Rating stat is pressed', () => {
    const { getByText } = render(<ProfileHeader {...commonProps} />);
    fireEvent.press(getByText('Reviews'));
    expect(commonProps.onReviewsPress).toHaveBeenCalledTimes(1);
  });

  it('renders with dark mode styles', () => {
    require('@/src/hooks/useColorScheme').useColorScheme.mockReturnValue('dark');
    const { getByText } = render(<ProfileHeader {...commonProps} />);
    // Check for dark mode specific styles if they were explicitly set in the mock
    // For now, just ensure it renders without errors in dark mode
    expect(getByText('Test User')).toBeTruthy();
  });
});
