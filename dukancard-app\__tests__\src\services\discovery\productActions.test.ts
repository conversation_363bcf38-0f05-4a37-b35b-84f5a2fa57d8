import { fetchMoreProductsCombined, fetchAllProducts, fetchProductsByBusinessIds } from '@/src/services/discovery/productActions';
import { supabase } from '@/src/config/supabase';
import { getSortingColumn, getSortingDirection } from '@/src/services/discovery/types';

// Mock external dependencies
jest.mock('@/src/config/supabase');
jest.mock('@/src/services/discovery/types', () => ({
  getSortingColumn: jest.fn((sortBy) => {
    if (sortBy === 'newest') return 'created_at';
    if (sortBy === 'name_asc') return 'name';
    if (sortBy === 'name_desc') return 'name';
    if (sortBy === 'price_low') return 'base_price';
    if (sortBy === 'price_high') return 'base_price';
    return 'created_at';
  }),
  getSortingDirection: jest.fn((sortBy) => {
    if (sortBy === 'name_desc' || sortBy === 'price_high') return false;
    return true;
  }),
}));

describe('productActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock supabase.from chainable methods
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      ilike: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      then: jest.fn((resolve) => resolve({ data: [], count: 0, error: null })),
    });
  });

  describe('fetchAllProducts', () => {
    it('should fetch all products with default parameters', async () => {
      const mockProducts = [
        { id: 'p1', name: 'Product 1', business_id: 'b1', is_available: true, business_profiles: { business_slug: 'biz-1' } },
      ];
      (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
        if (tableName === 'business_profiles') {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            then: jest.fn((resolve) => resolve({ data: [{ id: 'b1' }], error: null })),
          };
        } else if (tableName === 'products_services') {
          return {
            select: jest.fn().mockReturnThis(),
            in: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            ilike: jest.fn().mockReturnThis(),
            order: jest.fn().mockReturnThis(),
            range: jest.fn().mockReturnThis(),
            then: jest.fn((resolve) => resolve({ data: mockProducts, count: 1, error: null })),
          };
        }
        return jest.fn();
      });

      const result = await fetchAllProducts({});

      expect(result.data?.products.length).toBe(1);
      expect(result.data?.totalCount).toBe(1);
      expect(result.data?.hasMore).toBe(false);
      expect(result.data?.nextPage).toBeNull();
      expect(result.data?.isAuthenticated).toBe(true);
    });

    it('should filter by productType', async () => {
      await fetchAllProducts({ productType: 'physical' });
      expect(supabase.from).toHaveBeenCalledWith('products_services');
      expect(supabase.from('products_services').eq).toHaveBeenCalledWith('product_type', 'physical');
    });

    it('should filter by productName', async () => {
      await fetchAllProducts({ productName: 'test' });
      expect(supabase.from).toHaveBeenCalledWith('products_services');
      expect(supabase.from('products_services').ilike).toHaveBeenCalledWith('name', '%test%');
    });

    it('should apply sorting for products', async () => {
      await fetchAllProducts({ sortBy: 'name_asc' });
      expect(getSortingColumn).toHaveBeenCalledWith('name_asc', true);
      expect(getSortingDirection).toHaveBeenCalledWith('name_asc');
    });

    it('should handle price sorting correctly', async () => {
      await fetchAllProducts({ sortBy: 'price_low' });
      expect(supabase.from('products_services').order).toHaveBeenCalledWith('discounted_price', { ascending: true, nullsFirst: false });
      expect(supabase.from('products_services').order).toHaveBeenCalledWith('base_price', { ascending: true, nullsFirst: false });
    });
  });

  describe('fetchMoreProductsCombined', () => {
    it('should fetch more products', async () => {
      const mockProducts = [
        { id: 'p1', name: 'Product 1', business_id: 'b1', is_available: true, business_profiles: { business_slug: 'biz-1' } },
      ];
      (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
        if (tableName === 'business_profiles') {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            then: jest.fn((resolve) => resolve({ data: [{ id: 'b1' }], error: null })),
          };
        } else if (tableName === 'products_services') {
          return {
            select: jest.fn().mockReturnThis(),
            in: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            ilike: jest.fn().mockReturnThis(),
            order: jest.fn().mockReturnThis(),
            range: jest.fn().mockReturnThis(),
            then: jest.fn((resolve) => resolve({ data: mockProducts, count: 1, error: null })),
          };
        }
        return jest.fn();
      });

      const result = await fetchMoreProductsCombined({ page: 1, limit: 1 });

      expect(result.data?.products.length).toBe(1);
      expect(result.data?.totalCount).toBe(1);
      expect(result.data?.hasMore).toBe(false);
      expect(result.data?.nextPage).toBeNull();
    });
  });

  describe('fetchProductsByBusinessIds', () => {
    it('should fetch products for given business IDs', async () => {
      const mockProducts = [
        { id: 'p1', name: 'Product 1', business_id: 'b1', is_available: true, business_profiles: { business_slug: 'biz-1' } },
      ];
      (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
        if (tableName === 'business_profiles') {
          return {
            select: jest.fn().mockReturnThis(),
            in: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            then: jest.fn((resolve) => resolve({ data: [{ id: 'b1' }], error: null })),
          };
        } else if (tableName === 'products_services') {
          return {
            select: jest.fn().mockReturnThis(),
            in: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            ilike: jest.fn().mockReturnThis(),
            order: jest.fn().mockReturnThis(),
            range: jest.fn().mockReturnThis(),
            then: jest.fn((resolve) => resolve({ data: mockProducts, count: 1, error: null })),
          };
        }
        return jest.fn();
      });

      const result = await fetchProductsByBusinessIds({ businessIds: ['b1'] });

      expect(result.data?.products.length).toBe(1);
      expect(result.data?.totalCount).toBe(1);
    });

    it('should return error if no business IDs are provided', async () => {
      const result = await fetchProductsByBusinessIds({ businessIds: [] });
      expect(result.error).toBe('No business IDs provided');
    });
  });
});