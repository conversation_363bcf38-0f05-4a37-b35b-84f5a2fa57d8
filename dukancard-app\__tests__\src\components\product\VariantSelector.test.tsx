import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import VariantSelector from '../../../../src/components/product/VariantSelector';

// Mock useColorScheme hook
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

describe('VariantSelector', () => {
  const mockVariants = [
    {
      id: 'v1',
      variant_name: 'Red Small Cotton',
      variant_values: { color: 'red', size: 's', material: 'cotton' },
      base_price: 100,
      is_available: true,
      images: [],
      featured_image_index: 0,
    },
    {
      id: 'v2',
      variant_name: 'Blue Medium Polyester',
      variant_values: { color: 'blue', size: 'm', material: 'polyester' },
      base_price: 120,
      is_available: true,
      images: [],
      featured_image_index: 0,
    },
    {
      id: 'v3',
      variant_name: 'Red Large Silk',
      variant_values: { color: 'red', size: 'l', material: 'silk' },
      base_price: 150,
      is_available: false,
      images: [],
      featured_image_index: 0,
    },
    {
      id: 'v4',
      variant_name: 'Green Small Cotton',
      variant_values: { color: 'green', size: 's', material: 'cotton' },
      base_price: 110,
      is_available: true,
      images: [],
      featured_image_index: 0,
    },
  ];

  it('renders nothing when no variants are provided', () => {
    const { queryByText } = render(
      <VariantSelector variants={[]} onVariantSelect={jest.fn()} />
    );
    expect(queryByText('Select Options')).toBeNull();
  });

  it('renders variant types and options correctly', () => {
    const { getByText } = render(
      <VariantSelector variants={mockVariants} onVariantSelect={jest.fn()} />
    );

    expect(getByText('Select Options')).toBeTruthy();
    expect(getByText('Color')).toBeTruthy();
    expect(getByText('Size')).toBeTruthy();
    expect(getByText('Material')).toBeTruthy();

    expect(getByText('Red')).toBeTruthy();
    expect(getByText('Blue')).toBeTruthy();
    expect(getByText('S')).toBeTruthy();
    expect(getByText('M')).toBeTruthy();
    expect(getByText('L')).toBeTruthy();
    expect(getByText('Cotton')).toBeTruthy();
    expect(getByText('Polyester')).toBeTruthy();
    expect(getByText('Silk')).toBeTruthy();
  });

  it('selects an option and calls onVariantSelect', () => {
    const onVariantSelectMock = jest.fn();
    const { getByText } = render(
      <VariantSelector variants={mockVariants} onVariantSelect={onVariantSelectMock} />
    );

    fireEvent.press(getByText('Red'));
    expect(onVariantSelectMock).toHaveBeenCalledWith(expect.objectContaining({
      id: 'v1',
      variant_values: expect.objectContaining({ color: 'red' }),
    }));
    expect(getByText('Selected: Red Small Cotton')).toBeTruthy();
  });

  it('deselects an option when pressed again', () => {
    const onVariantSelectMock = jest.fn();
    const { getByText, queryByText } = render(
      <VariantSelector variants={mockVariants} onVariantSelect={onVariantSelectMock} />
    );

    fireEvent.press(getByText('Red'));
    expect(getByText('Selected: Red Small Cotton')).toBeTruthy();

    fireEvent.press(getByText('Red'));
    expect(queryByText('Selected:')).toBeNull();
    expect(onVariantSelectMock).toHaveBeenCalledWith(null);
  });

  it('updates selected variant info when multiple options are selected', () => {
    const onVariantSelectMock = jest.fn();
    const { getByText } = render(
      <VariantSelector variants={mockVariants} onVariantSelect={onVariantSelectMock} />
    );

    fireEvent.press(getByText('Red'));
    fireEvent.press(getByText('S'));
    fireEvent.press(getByText('Cotton'));

    expect(getByText('Selected: Red Small Cotton')).toBeTruthy();
    expect(onVariantSelectMock).toHaveBeenCalledWith(expect.objectContaining({
      id: 'v1',
      variant_values: { color: 'red', size: 's', material: 'cotton' },
    }));
  });

  it('disables unavailable options', () => {
    const { getByText } = render(
      <VariantSelector variants={mockVariants} onVariantSelect={jest.fn()} />
    );

    // Red Large Silk (v3) is unavailable
    const redOption = getByText('Red');
    const largeOption = getByText('L');
    const silkOption = getByText('Silk');

    // Select Red
    fireEvent.press(redOption);

    // Large and Silk should be disabled if only Red is selected and no available variant matches
    // This test might need refinement based on exact availability logic, but for now, check if they are present
    expect(largeOption).toBeTruthy();
    expect(silkOption).toBeTruthy();

    // Attempt to select an unavailable combination (Red + Large + Silk)
    fireEvent.press(largeOption);
    fireEvent.press(silkOption);

    // The selected variant info should not show v3 if it's unavailable
    const selectedInfo = getByText('Selected: Red Small Cotton'); // Should still be v1 or null if no match
    expect(selectedInfo).toBeTruthy();
  });

  it('does not allow selection when disabled prop is true', () => {
    const onVariantSelectMock = jest.fn();
    const { getByText } = render(
      <VariantSelector variants={mockVariants} onVariantSelect={onVariantSelectMock} disabled={true} />
    );

    fireEvent.press(getByText('Red'));
    expect(onVariantSelectMock).not.toHaveBeenCalled();
    expect(queryByText('Selected:')).toBeNull();
  });
});
