import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import { NotificationIcon } from '../../../../src/components/shared/NotificationIcon';

// Mock necessary modules
jest.mock('lucide-react-native', () => ({
  Bell: 'BellIcon',
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: jest.fn(() => ({
    profileStatus: {
      roleStatus: { role: 'customer' }, // Default to customer
    },
  })),
}));

jest.mock('@/src/contexts/NotificationContext', () => ({
  useNotifications: jest.fn(() => ({
    unreadCount: 0,
    showModal: jest.fn(),
  })),
}));

describe('NotificationIcon', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mocks to default behavior for each test
    require('@/src/contexts/AuthContext').useAuth.mockReturnValue({
      profileStatus: { roleStatus: { role: 'customer' } },
    });
    require('@/src/contexts/NotificationContext').useNotifications.mockReturnValue({
      unreadCount: 0,
      showModal: jest.fn(),
    });
  });

  it('renders null if user role is not business', () => {
    const { queryByTestId } = render(<NotificationIcon />);
    expect(queryByTestId('notification-icon')).toBeNull();
  });

  it('renders Bell icon when user role is business', () => {
    require('@/src/contexts/AuthContext').useAuth.mockReturnValue({
      profileStatus: { roleStatus: { role: 'business' } },
    });
    const { getByTestId } = render(<NotificationIcon />);
    expect(getByTestId('notification-icon')).toBeTruthy();
    expect(getByTestId('BellIcon')).toBeTruthy();
  });

  it('displays badge when showBadge is true and unreadCount > 0', () => {
    require('@/src/contexts/AuthContext').useAuth.mockReturnValue({
      profileStatus: { roleStatus: { role: 'business' } },
    });
    require('@/src/contexts/NotificationContext').useNotifications.mockReturnValue({
      unreadCount: 5,
      showModal: jest.fn(),
    });
    const { getByText } = render(<NotificationIcon showBadge={true} />);
    expect(getByText('5')).toBeTruthy();
  });

  it('does not display badge when showBadge is false', () => {
    require('@/src/contexts/AuthContext').useAuth.mockReturnValue({
      profileStatus: { roleStatus: { role: 'business' } },
    });
    require('@/src/contexts/NotificationContext').useNotifications.mockReturnValue({
      unreadCount: 5,
      showModal: jest.fn(),
    });
    const { queryByText } = render(<NotificationIcon showBadge={false} />);
    expect(queryByText('5')).toBeNull();
  });

  it('does not display badge when unreadCount is 0', () => {
    require('@/src/contexts/AuthContext').useAuth.mockReturnValue({
      profileStatus: { roleStatus: { role: 'business' } },
    });
    require('@/src/contexts/NotificationContext').useNotifications.mockReturnValue({
      unreadCount: 0,
      showModal: jest.fn(),
    });
    const { queryByText } = render(<NotificationIcon showBadge={true} />);
    expect(queryByText('0')).toBeNull();
  });

  it('calls onPress prop when provided', () => {
    require('@/src/contexts/AuthContext').useAuth.mockReturnValue({
      profileStatus: { roleStatus: { role: 'business' } },
    });
    const mockOnPress = jest.fn();
    const { getByTestId } = render(<NotificationIcon onPress={mockOnPress} />);
    fireEvent.press(getByTestId('notification-icon'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('calls showModal from useNotifications when onPress is not provided', () => {
    require('@/src/contexts/AuthContext').useAuth.mockReturnValue({
      profileStatus: { roleStatus: { role: 'business' } },
    });
    const mockShowModal = jest.fn();
    require('@/src/contexts/NotificationContext').useNotifications.mockReturnValue({
      unreadCount: 0,
      showModal: mockShowModal,
    });
    const { getByTestId } = render(<NotificationIcon />);
    fireEvent.press(getByTestId('notification-icon'));
    expect(mockShowModal).toHaveBeenCalledTimes(1);
  });

  it('displays "99+" when unreadCount is greater than 99', () => {
    require('@/src/contexts/AuthContext').useAuth.mockReturnValue({
      profileStatus: { roleStatus: { role: 'business' } },
    });
    require('@/src/contexts/NotificationContext').useNotifications.mockReturnValue({
      unreadCount: 100,
      showModal: jest.fn(),
    });
    const { getByText } = render(<NotificationIcon showBadge={true} />);
    expect(getByText('99+')).toBeTruthy();
  });
});
