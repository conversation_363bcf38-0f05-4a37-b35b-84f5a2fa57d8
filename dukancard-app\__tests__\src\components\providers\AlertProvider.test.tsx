import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { AlertProvider, useAlertContext, useAlertDialog } from '../../../../src/components/providers/AlertProvider';

// Mock the useAlert hook
jest.mock('@/src/hooks/useAlert', () => ({
  useAlert: jest.fn(() => ({
    alertState: { visible: false, type: 'info', title: '', message: '', buttons: [] },
    showAlert: jest.fn(),
    hideAlert: jest.fn(),
    showSuccess: jest.fn(),
    showError: jest.fn(),
    showWarning: jest.fn(),
    showInfo: jest.fn(),
    showConfirm: jest.fn(),
    showLogout: jest.fn(),
    showDelete: jest.fn(),
  })),
}));

// Mock the AlertDialog component
jest.mock('@/src/components/ui/AlertDialog', () => ({
  AlertDialog: jest.fn(({ visible, type, title, message, buttons, onClose, showCloseButton, customIcon }) => {
    if (!visible) return null;
    return (
      <mock-AlertDialog
        testID="alert-dialog"
        visible={visible}
        type={type}
        title={title}
        message={message}
        buttons={buttons}
        onClose={onClose}
        showCloseButton={showCloseButton}
        customIcon={customIcon}
      />
    );
  }),
}));

describe('AlertProvider', () => {
  it('renders children correctly', () => {
    render(
      <AlertProvider>
        <TestComponent />
      </AlertProvider>
    );
    expect(screen.getByText('Test Component')).toBeTruthy();
  });

  it('throws error when useAlertContext is not used within AlertProvider', () => {
    const TestComponent = () => {
      useAlertContext();
      return null;
    };
    expect(() => render(<TestComponent />)).toThrow(
      'useAlertContext must be used within an AlertProvider'
    );
  });

  it('useAlertDialog exposes alert functions', () => {
    const TestComponent = () => {
      const { alert, success, error, warning, info, confirm, logout, delete: deleteFn } = useAlertDialog();
      return (
        <>
          <button onPress={() => alert('title', 'message')}>Show Alert</button>
          <button onPress={() => success('title', 'message')}>Show Success</button>
          <button onPress={() => error('title', 'message')}>Show Error</button>
          <button onPress={() => warning('title', 'message')}>Show Warning</button>
          <button onPress={() => info('title', 'message')}>Show Info</button>
          <button onPress={() => confirm('title', 'message', jest.fn())}>Show Confirm</button>
          <button onPress={() => logout(jest.fn())}>Show Logout</button>
          <button onPress={() => deleteFn(jest.fn())}>Show Delete</button>
        </>
      );
    };

    render(
      <AlertProvider>
        <TestComponent />
      </AlertProvider>
    );

    const { useAlert } = require('@/src/hooks/useAlert');
    const mockUseAlert = useAlert();

    fireEvent.press(screen.getByText('Show Alert'));
    expect(mockUseAlert.showAlert).toHaveBeenCalledWith('title', 'message');

    fireEvent.press(screen.getByText('Show Success'));
    expect(mockUseAlert.showSuccess).toHaveBeenCalledWith('title', 'message');

    fireEvent.press(screen.getByText('Show Error'));
    expect(mockUseAlert.showError).toHaveBeenCalledWith('title', 'message');

    fireEvent.press(screen.getByText('Show Warning'));
    expect(mockUseAlert.showWarning).toHaveBeenCalledWith('title', 'message');

    fireEvent.press(screen.getByText('Show Info'));
    expect(mockUseAlert.showInfo).toHaveBeenCalledWith('title', 'message');

    fireEvent.press(screen.getByText('Show Confirm'));
    expect(mockUseAlert.showConfirm).toHaveBeenCalledWith('title', 'message', expect.any(Function));

    fireEvent.press(screen.getByText('Show Logout'));
    expect(mockUseAlert.showLogout).toHaveBeenCalledWith(expect.any(Function));

    fireEvent.press(screen.getByText('Show Delete'));
    expect(mockUseAlert.showDelete).toHaveBeenCalledWith(expect.any(Function));
  });

  it('renders AlertDialog with correct props based on alertState', () => {
    const { useAlert } = require('@/src/hooks/useAlert');
    useAlert.mockReturnValue({
      alertState: {
        visible: true,
        type: 'success',
        title: 'Test Title',
        message: 'Test Message',
        buttons: [{ text: 'OK' }],
        showCloseButton: true,
        customIcon: 'custom-icon',
      },
      showAlert: jest.fn(),
      hideAlert: jest.fn(),
      showSuccess: jest.fn(),
      showError: jest.fn(),
      showWarning: jest.fn(),
      showInfo: jest.fn(),
      showConfirm: jest.fn(),
      showLogout: jest.fn(),
      showDelete: jest.fn(),
    });

    const { AlertDialog } = require('@/src/components/ui/AlertDialog');

    render(
      <AlertProvider>
        <TestComponent />
      </AlertProvider>
    );

    expect(AlertDialog).toHaveBeenCalledWith(
      expect.objectContaining({
        visible: true,
        type: 'success',
        title: 'Test Title',
        message: 'Test Message',
        buttons: [{ text: 'OK' }],
        showCloseButton: true,
        customIcon: 'custom-icon',
      }),
      {}
    );
  });
});

const TestComponent = () => <Text>Test Component</Text>;
