import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import VariantTypeBottomSheet, {
  VariantTypeBottomSheetRef,
} from '@/src/components/pickers/VariantTypeBottomSheet';
import { useTheme } from '@/src/hooks/useTheme';
import { getAllVariantTypes } from '@/src/constants/predefinedVariants';

// Mock dependencies
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
      textPrimary: '#000',
      textSecondary: '#888',
      border: '#ccc',
      background: '#fff',
      mutedForeground: '#aaa',
    },
  }),
}));

jest.mock('@/src/constants/predefinedVariants', () => ({
  getAllVariantTypes: jest.fn(() => [
    { id: '1', name: '<PERSON><PERSON>', display_name: '<PERSON><PERSON>', description: 'Product size' },
    { id: '2', name: 'Color', display_name: 'Color', description: 'Product color' },
    { id: '3', name: 'Material', display_name: 'Material', description: 'Product material' },
  ]),
}));

describe('VariantTypeBottomSheet', () => {
  const mockOnSelect = jest.fn();
  const mockOnClose = jest.fn();

  const defaultProps = {
    onSelect: mockOnSelect,
    onClose: mockOnClose,
  };

  let bottomSheetRef: React.RefObject<VariantTypeBottomSheetRef>;

  beforeEach(() => {
    jest.clearAllMocks();
    bottomSheetRef = React.createRef<VariantTypeBottomSheetRef>();
  });

  it('renders correctly when opened', async () => {
    const { getByText, getByPlaceholderText } = render(
      <VariantTypeBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    await waitFor(() => {
      expect(getByText('Select Variant Type')).toBeTruthy();
      expect(getByPlaceholderText('Search variant types...')).toBeTruthy();
      expect(getByText('Size')).toBeTruthy();
      expect(getByText('Color')).toBeTruthy();
    });
  });

  it('calls onSelect and closes when a variant type is selected', async () => {
    const { getByText } = render(
      <VariantTypeBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    await waitFor(() => {
      fireEvent.press(getByText('Size'));
    });

    expect(mockOnSelect).toHaveBeenCalledWith(expect.objectContaining({ name: 'Size' }));
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('filters variant types based on search query', async () => {
    const { getByPlaceholderText, getByText, queryByText } = render(
      <VariantTypeBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    const searchInput = getByPlaceholderText('Search variant types...');
    fireEvent.changeText(searchInput, 'color');

    await waitFor(() => {
      expect(getByText('Color')).toBeTruthy();
      expect(queryByText('Size')).toBeNull();
    });
  });

  it('shows empty state when no variant types match search', async () => {
    const { getByPlaceholderText, getByText } = render(
      <VariantTypeBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    const searchInput = getByPlaceholderText('Search variant types...');
    fireEvent.changeText(searchInput, 'xyz');

    await waitFor(() => {
      expect(getByText('No variant types found for "xyz"')).toBeTruthy();
    });
  });

  it('calls dismiss when close button is pressed', () => {
    const { getByTestId } = render(
      <VariantTypeBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    fireEvent.press(getByTestId('close-button'));
    expect(bottomSheetRef.current?.close).toHaveBeenCalled();
  });
});
