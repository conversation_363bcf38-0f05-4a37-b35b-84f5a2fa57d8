import { applyDiversityRules, groupPostsByAuthor, roundRobinDistribution } from '@/src/utils/feed/diversityEngine';

// Mock UnifiedPost type for testing
interface MockUnifiedPost {
  id: string;
  author_id: string;
  created_at: string;
  // Add other properties if needed for specific tests
}

describe('diversityEngine', () => {
  describe('applyDiversityRules', () => {
    it('should return an empty array for empty input', () => {
      expect(applyDiversityRules([])).toEqual([]);
    });

    it('should return the same array for a single post', () => {
      const posts: MockUnifiedPost[] = [{ id: '1', author_id: 'A', created_at: '2023-01-01' }];
      expect(applyDiversityRules(posts)).toEqual(posts);
    });

    it('should prevent consecutive posts from the same author by default (maxConsecutiveFromSameAuthor = 1)', () => {
      const posts: MockUnifiedPost[] = [
        { id: '1', author_id: 'A', created_at: '2023-01-01' },
        { id: '2', author_id: 'A', created_at: '2023-01-02' },
        { id: '3', author_id: 'B', created_at: '2023-01-03' },
        { id: '4', author_id: 'A', created_at: '2023-01-04' },
        { id: '5', author_id: 'B', created_at: '2023-01-05' },
      ];
      const expectedOrder = [
        { id: '1', author_id: 'A', created_at: '2023-01-01' },
        { id: '3', author_id: 'B', created_at: '2023-01-03' },
        { id: '2', author_id: 'A', created_at: '2023-01-02' },
        { id: '5', author_id: 'B', created_at: '2023-01-05' },
        { id: '4', author_id: 'A', created_at: '2023-01-04' },
      ];
      expect(applyDiversityRules(posts)).toEqual(expectedOrder);
    });

    it('should allow more consecutive posts when maxConsecutiveFromSameAuthor is higher', () => {
      const posts: MockUnifiedPost[] = [
        { id: '1', author_id: 'A', created_at: '2023-01-01' },
        { id: '2', author_id: 'A', created_at: '2023-01-02' },
        { id: '3', author_id: 'A', created_at: '2023-01-03' },
        { id: '4', author_id: 'B', created_at: '2023-01-04' },
      ];
      const expectedOrder = [
        { id: '1', author_id: 'A', created_at: '2023-01-01' },
        { id: '2', author_id: 'A', created_at: '2023-01-02' },
        { id: '4', author_id: 'B', created_at: '2023-01-04' },
        { id: '3', author_id: 'A', created_at: '2023-01-03' },
      ];
      expect(applyDiversityRules(posts, { maxConsecutiveFromSameAuthor: 2 })).toEqual(expectedOrder);
    });

    it('should handle cases where diversity is impossible', () => {
      const posts: MockUnifiedPost[] = [
        { id: '1', author_id: 'A', created_at: '2023-01-01' },
        { id: '2', author_id: 'A', created_at: '2023-01-02' },
        { id: '3', author_id: 'A', created_at: '2023-01-03' },
      ];
      expect(applyDiversityRules(posts)).toEqual(posts); // Should return as is
    });
  });

  describe('groupPostsByAuthor', () => {
    it('should return an empty map for empty input', () => {
      expect(groupPostsByAuthor([])).toEqual(new Map());
    });

    it('should group posts by author and sort chronologically', () => {
      const posts: MockUnifiedPost[] = [
        { id: '1', author_id: 'A', created_at: '2023-01-01T10:00:00Z' },
        { id: '2', author_id: 'B', created_at: '2023-01-01T11:00:00Z' },
        { id: '3', author_id: 'A', created_at: '2023-01-01T12:00:00Z' },
        { id: '4', author_id: 'C', created_at: '2023-01-01T09:00:00Z' },
      ];
      const grouped = groupPostsByAuthor(posts);
      expect(grouped.get('A')).toEqual([
        { id: '3', author_id: 'A', created_at: '2023-01-01T12:00:00Z' },
        { id: '1', author_id: 'A', created_at: '2023-01-01T10:00:00Z' },
      ]);
      expect(grouped.get('B')).toEqual([
        { id: '2', author_id: 'B', created_at: '2023-01-01T11:00:00Z' },
      ]);
      expect(grouped.get('C')).toEqual([
        { id: '4', author_id: 'C', created_at: '2023-01-01T09:00:00Z' },
      ]);
    });
  });

  describe('roundRobinDistribution', () => {
    it('should return an empty array for empty map', () => {
      expect(roundRobinDistribution(new Map())).toEqual([]);
    });

    it('should distribute posts in a round-robin fashion', () => {
      const groupedPosts = new Map<string, MockUnifiedPost[]>([
        ['A', [{ id: 'A1', author_id: 'A', created_at: '' }, { id: 'A2', author_id: 'A', created_at: '' }]],
        ['B', [{ id: 'B1', author_id: 'B', created_at: '' }, { id: 'B2', author_id: 'B', created_at: '' }, { id: 'B3', author_id: 'B', created_at: '' }]],
        ['C', [{ id: 'C1', author_id: 'C', created_at: '' }]],
      ]);
      const expectedOrder = [
        { id: 'A1', author_id: 'A', created_at: '' },
        { id: 'B1', author_id: 'B', created_at: '' },
        { id: 'C1', author_id: 'C', created_at: '' },
        { id: 'A2', author_id: 'A', created_at: '' },
        { id: 'B2', author_id: 'B', created_at: '' },
        { id: 'B3', author_id: 'B', created_at: '' },
      ];
      expect(roundRobinDistribution(groupedPosts)).toEqual(expectedOrder);
    });

    it('should handle single author correctly', () => {
      const groupedPosts = new Map<string, MockUnifiedPost[]>([
        ['A', [{ id: 'A1', author_id: 'A', created_at: '' }, { id: 'A2', author_id: 'A', created_at: '' }]],
      ]);
      expect(roundRobinDistribution(groupedPosts)).toEqual([
        { id: 'A1', author_id: 'A', created_at: '' },
        { id: 'A2', author_id: 'A', created_at: '' },
      ]);
    });
  });
});