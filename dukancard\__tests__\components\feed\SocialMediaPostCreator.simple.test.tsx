import React from 'react';
import { render } from '@testing-library/react';

// Mock all the dependencies
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

jest.mock('@/lib/actions/customerPosts', () => ({
  createCustomerPost: jest.fn(),
  updateCustomerPost: jest.fn(),
  deleteCustomerPost: jest.fn()
}));

jest.mock('@/lib/actions/shared/upload-customer-post-media', () => ({
  uploadCustomerPostImage: jest.fn()
}));

jest.mock('@/lib/utils/client-image-compression', () => ({
  compressImageUltraAggressiveClient: jest.fn()
}));

jest.mock('@/utils/supabase/client', () => ({
  createClient: () => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })
    },
    from: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { name: 'Test User', avatar_url: null },
            error: null
          })
        })
      })
    })
  })
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    refresh: jest.fn()
  })
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: 'div',
    button: 'button',
    textarea: 'textarea'
  },
  AnimatePresence: ({ children }: any) => children
}));

jest.mock('lucide-react', () => ({
  Image: () => <div>Image Icon</div>,
  MapPin: () => <div>MapPin Icon</div>,
  X: () => <div>X Icon</div>,
  Send: () => <div>Send Icon</div>,
  Loader2: () => <div>Loader Icon</div>
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => <button {...props}>{children}</button>
}));

jest.mock('@/components/ui/avatar', () => ({
  Avatar: ({ children }: any) => <div>{children}</div>,
  AvatarFallback: ({ children }: any) => <div>{children}</div>,
  AvatarImage: ({ children }: any) => <div>{children}</div>
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ alt, ...props }: any) => <img alt={alt} {...props} />
}));

jest.mock('@/components/feed/shared/forms/LocationDisplay', () => {
  return function LocationDisplay() {
    return <div>Location Display</div>;
  };
});

// Import the component after mocking
import SocialMediaPostCreator from '@/components/feed/shared/SocialMediaPostCreator';

describe('SocialMediaPostCreator Simple Test', () => {
  it('should render without crashing', () => {
    const { container } = render(<SocialMediaPostCreator />);
    expect(container).toBeInTheDocument();
  });
});
