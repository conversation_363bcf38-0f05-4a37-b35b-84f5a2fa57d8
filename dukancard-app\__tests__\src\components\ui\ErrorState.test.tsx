import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { ErrorState, ErrorType } from '@/src/components/ui/ErrorState';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import {
  AlertTriangle,
  Wifi,
  RefreshCw,
  Search,
  Lock,
  Server,
  AlertCircle,
} from 'lucide-react-native';

// Mock the hooks and icons
jest.mock('@/src/hooks/useColorScheme');
jest.mock('lucide-react-native', () => ({
  AlertTriangle: jest.fn(() => null),
  Wifi: jest.fn(() => null),
  RefreshCw: jest.fn(() => null),
  Search: jest.fn(() => null),
  Lock: jest.fn(() => null),
  Server: jest.fn(() => null),
  AlertCircle: jest.fn(() => null),
}));

describe('ErrorState', () => {
  const mockOnAction = jest.fn();
  const mockOnRetry = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
    // Mock icon components to render a simple Text for testing
    (AlertTriangle as jest.Mock).mockImplementation(() => <Text testID="icon-alert-triangle">AlertTriangleIcon</Text>);
    (Wifi as jest.Mock).mockImplementation(() => <Text testID="icon-wifi">WifiIcon</Text>);
    (RefreshCw as jest.Mock).mockImplementation(() => <Text testID="icon-refresh">RefreshIcon</Text>);
    (Search as jest.Mock).mockImplementation(() => <Text testID="icon-search">SearchIcon</Text>);
    (Lock as jest.Mock).mockImplementation(() => <Text testID="icon-lock">LockIcon</Text>);
    (Server as jest.Mock).mockImplementation(() => <Text testID="icon-server">ServerIcon</Text>);
    (AlertCircle as jest.Mock).mockImplementation(() => <Text testID="icon-alert-circle">AlertCircleIcon</Text>);
  });

  it('renders generic error state by default', () => {
    render(<ErrorState />);
    expect(screen.getByText('Something went wrong')).toBeOnTheScreen();
    expect(screen.getByText('An unexpected error occurred. Please try again.')).toBeOnTheScreen();
    expect(screen.getByText('Retry')).toBeOnTheScreen();
    expect(screen.getByTestId('icon-alert-triangle')).toBeOnTheScreen();
  });

  it('renders network error state', () => {
    render(<ErrorState type="network" />);
    expect(screen.getByText('No Internet Connection')).toBeOnTheScreen();
    expect(screen.getByText('Please check your internet connection and try again.')).toBeOnTheScreen();
    expect(screen.getByText('Retry')).toBeOnTheScreen();
    expect(screen.getByTestId('icon-wifi')).toBeOnTheScreen();
  });

  it('renders notFound error state', () => {
    render(<ErrorState type="notFound" />);
    expect(screen.getByText('Not Found')).toBeOnTheScreen();
    expect(screen.getByText('The content you\'re looking for could not be found.')).toBeOnTheScreen();
    expect(screen.getByText('Go Back')).toBeOnTheScreen();
    expect(screen.getByTestId('icon-search')).toBeOnTheScreen();
  });

  it('renders unauthorized error state', () => {
    render(<ErrorState type="unauthorized" />);
    expect(screen.getByText('Access Denied')).toBeOnTheScreen();
    expect(screen.getByText('You don\'t have permission to access this content.')).toBeOnTheScreen();
    expect(screen.getByText('Sign In')).toBeOnTheScreen();
    expect(screen.getByTestId('icon-lock')).toBeOnTheScreen();
  });

  it('renders server error state', () => {
    render(<ErrorState type="server" />);
    expect(screen.getByText('Server Error')).toBeOnTheScreen();
    expect(screen.getByText('Something went wrong on our end. Please try again later.')).toBeOnTheScreen();
    expect(screen.getByText('Retry')).toBeOnTheScreen();
    expect(screen.getByTestId('icon-server')).toBeOnTheScreen();
  });

  it('renders validation error state', () => {
    render(<ErrorState type="validation" />);
    expect(screen.getByText('Invalid Data')).toBeOnTheScreen();
    expect(screen.getByText('Please check your input and try again.')).toBeOnTheScreen();
    expect(screen.getByText('Try Again')).toBeOnTheScreen();
    expect(screen.getByTestId('icon-alert-circle')).toBeOnTheScreen();
  });

  it('uses custom title, message, and actionText when provided', () => {
    render(
      <ErrorState
        title="Custom Title"
        message="Custom Message"
        actionText="Custom Action"
      />
    );
    expect(screen.getByText('Custom Title')).toBeOnTheScreen();
    expect(screen.getByText('Custom Message')).toBeOnTheScreen();
    expect(screen.getByText('Custom Action')).toBeOnTheScreen();
  });

  it('calls onAction when primary button is pressed', () => {
    render(<ErrorState onAction={mockOnAction} />);
    fireEvent.press(screen.getByText('Retry'));
    expect(mockOnAction).toHaveBeenCalledTimes(1);
  });

  it('calls onRetry when primary button is pressed and onAction is not provided', () => {
    render(<ErrorState onRetry={mockOnRetry} />);
    fireEvent.press(screen.getByText('Retry'));
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it('shows secondary retry button when showRetry is true and onRetry and onAction are provided', () => {
    render(<ErrorState onAction={mockOnAction} onRetry={mockOnRetry} showRetry={true} />);
    expect(screen.getByText('Retry')).toBeOnTheScreen(); // Primary button
    expect(screen.getByText('Retry', { selector: 'Text' })).toBeOnTheScreen(); // Secondary button
    expect(screen.getByTestId('icon-refresh')).toBeOnTheScreen();
  });

  it('calls onRetry when secondary retry button is pressed', () => {
    render(<ErrorState onAction={mockOnAction} onRetry={mockOnRetry} showRetry={true} />);
    fireEvent.press(screen.getByTestId('icon-refresh')); // Pressing the secondary retry button
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
    expect(mockOnAction).not.toHaveBeenCalled();
  });

  it('applies dark mode styles', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    render(<ErrorState />);
    // Check for dark mode specific styles (e.g., background color, text color)
    // This is a simplified check, a snapshot test would be more comprehensive for styles.
    expect(screen.getByText('Something went wrong').props.style).toContainEqual({
      color: '#FFFFFF',
    });
  });
});