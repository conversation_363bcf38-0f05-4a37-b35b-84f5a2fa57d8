import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { AvatarUpload } from '../../../../src/components/profile/AvatarUpload';
import * as ImagePicker from 'expo-image-picker';
import { Alert } from 'react-native';

// Mock necessary modules
jest.mock('expo-image', () => ({
  Image: 'Image',
}));

jest.mock('expo-image-picker', () => ({
  requestCameraPermissionsAsync: jest.fn(),
  requestMediaLibraryPermissionsAsync: jest.fn(),
  launchCameraAsync: jest.fn(),
  launchImageLibraryAsync: jest.fn(),
  MediaTypeOptions: {
    Images: 'Images',
  },
}));

jest.mock('lucide-react-native', () => ({
  Camera: 'Camera',
  User: 'User',
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: 'test-user-id' } }),
}));

jest.mock('@/backend/supabase/services/common/profileService', () => ({
  updateCustomerAvatar: jest.fn(() => Promise.resolve({ success: true })),
}));

jest.mock('@/backend/supabase/services/storage/avatarUploadService', () => ({
  uploadAvatarImage: jest.fn(() => Promise.resolve({ success: true, url: 'https://example.com/new-avatar.png' })),
}));

jest.spyOn(Alert, 'alert');

describe('AvatarUpload', () => {
  const mockOnAvatarUpdated = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (ImagePicker.requestCameraPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'granted' });
    (ImagePicker.requestMediaLibraryPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'granted' });
    (ImagePicker.launchCameraAsync as jest.Mock).mockResolvedValue({ canceled: true });
    (ImagePicker.launchImageLibraryAsync as jest.Mock).mockResolvedValue({ canceled: true });
  });

  it('renders with initial avatar URL', () => {
    const { getByTestId } = render(
      <AvatarUpload initialAvatarUrl="https://example.com/avatar.png" />
    );
    expect(getByTestId('avatar-image')).toBeTruthy();
  });

  it('renders with placeholder when no initial avatar URL', () => {
    const { getByText } = render(<AvatarUpload userName="John Doe" />);
    expect(getByText('JD')).toBeTruthy();
  });

  it('shows image picker options on press', () => {
    const { getByTestId } = render(<AvatarUpload />);
    fireEvent.press(getByTestId('avatar-button'));
    expect(Alert.alert).toHaveBeenCalledWith(
      'Select Avatar',
      'Choose how you want to select your profile picture',
      expect.any(Array)
    );
  });

  it('handles camera image selection and upload success', async () => {
    (ImagePicker.launchCameraAsync as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/image.jpg' }],
    });

    const { getByTestId } = render(
      <AvatarUpload onAvatarUpdated={mockOnAvatarUpdated} />
    );
    fireEvent.press(getByTestId('avatar-button'));

    // Simulate pressing 'Camera' option in Alert
    const cameraOption = (Alert.alert as jest.Mock).mock.calls[0][2][0];
    cameraOption.onPress();

    await waitFor(() => {
      expect(ImagePicker.launchCameraAsync).toHaveBeenCalled();
      expect(mockOnAvatarUpdated).toHaveBeenCalledWith('https://example.com/new-avatar.png');
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Profile picture updated successfully!');
    });
  });

  it('handles gallery image selection and upload success', async () => {
    (ImagePicker.launchImageLibraryAsync as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/image.jpg' }],
    });

    const { getByTestId } = render(
      <AvatarUpload onAvatarUpdated={mockOnAvatarUpdated} />
    );
    fireEvent.press(getByTestId('avatar-button'));

    // Simulate pressing 'Gallery' option in Alert
    const galleryOption = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    galleryOption.onPress();

    await waitFor(() => {
      expect(ImagePicker.launchImageLibraryAsync).toHaveBeenCalled();
      expect(mockOnAvatarUpdated).toHaveBeenCalledWith('https://example.com/new-avatar.png');
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Profile picture updated successfully!');
    });
  });

  it('handles upload failure', async () => {
    (ImagePicker.launchImageLibraryAsync as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/image.jpg' }],
    });
    (require('@/backend/supabase/services/storage/avatarUploadService').uploadAvatarImage as jest.Mock).mockResolvedValueOnce({
      success: false, error: 'Upload failed'
    });

    const { getByTestId } = render(<AvatarUpload />);
    fireEvent.press(getByTestId('avatar-button'));

    const galleryOption = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    galleryOption.onPress();

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Upload failed');
    });
  });

  it('displays uploading state', async () => {
    (ImagePicker.launchImageLibraryAsync as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/image.jpg' }],
    });
    (require('@/backend/supabase/services/storage/avatarUploadService').uploadAvatarImage as jest.Mock).mockImplementationOnce(() => new Promise(resolve => setTimeout(() => resolve({ success: true, url: 'https://example.com/new-avatar.png' }), 100)));

    const { getByTestId, getByText } = render(<AvatarUpload />);
    fireEvent.press(getByTestId('avatar-button'));

    const galleryOption = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    galleryOption.onPress();

    await waitFor(() => {
      expect(getByText('Uploading...')).toBeTruthy();
      expect(getByTestId('activity-indicator')).toBeTruthy();
    });

    await waitFor(() => {
      expect(getByText('Tap to change picture')).toBeTruthy();
    }, { timeout: 200 });
  });

  it('requests camera permission', async () => {
    (ImagePicker.requestCameraPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'denied' });
    const { getByTestId } = render(<AvatarUpload />);
    fireEvent.press(getByTestId('avatar-button'));

    const cameraOption = (Alert.alert as jest.Mock).mock.calls[0][2][0];
    cameraOption.onPress();

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Permission Required', 'Camera permission is required to take photos');
    });
  });

  it('requests media library permission', async () => {
    (ImagePicker.requestMediaLibraryPermissionsAsync as jest.Mock).mockResolvedValue({ status: 'denied' });
    const { getByTestId } = render(<AvatarUpload />);
    fireEvent.press(getByTestId('avatar-button'));

    const galleryOption = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    galleryOption.onPress();

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Permission Required', 'Media library permission is required to select photos');
    });
  });
});
