import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import ImagePickerBottomSheet, {
  ImagePickerBottomSheetRef,
} from '@/src/components/pickers/ImagePickerBottomSheet';
import { useTheme } from '@/src/hooks/useTheme';

// Mock dependencies
jest.mock('@gorhom/bottom-sheet', () => {
  const RN = jest.requireActual('react-native');
  const React = jest.requireActual('react');
  return {
    __esModule: true,
    default: React.forwardRef(({ children }: any, ref: any) => {
      React.useImperativeHandle(ref, () => ({
        snapToIndex: jest.fn(),
        close: jest.fn(),
      }));
      return <RN.View testID="bottom-sheet">{children}</RN.View>;
    }),
    BottomSheetView: RN.View,
  };
});

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
      textPrimary: '#000',
      textSecondary: '#888',
      border: '#ccc',
    },
    isDark: false,
  }),
}));

describe('ImagePickerBottomSheet', () => {
  const mockOnCameraPress = jest.fn();
  const mockOnGalleryPress = jest.fn();

  const defaultProps = {
    onCameraPress: mockOnCameraPress,
    onGalleryPress: mockOnGalleryPress,
  };

  let bottomSheetRef: React.RefObject<ImagePickerBottomSheetRef>;

  beforeEach(() => {
    jest.clearAllMocks();
    bottomSheetRef = React.createRef<ImagePickerBottomSheetRef>();
  });

  it('renders correctly with default title and labels', () => {
    const { getByText } = render(
      <ImagePickerBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    expect(getByText('Select Photo')).toBeTruthy();
    expect(getByText('Take Photo')).toBeTruthy();
    expect(getByText('Choose from Gallery')).toBeTruthy();
  });

  it('renders correctly with custom title and labels', () => {
    const { getByText } = render(
      <ImagePickerBottomSheet
        ref={bottomSheetRef}
        {...defaultProps}
        title="Upload Image"
        cameraLabel="Use Camera"
        galleryLabel="Pick from Library"
      />
    );

    expect(getByText('Upload Image')).toBeTruthy();
    expect(getByText('Use Camera')).toBeTruthy();
    expect(getByText('Pick from Library')).toBeTruthy();
  });

  it('calls onCameraPress and closes when camera option is pressed', async () => {
    const { getByText } = render(
      <ImagePickerBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    fireEvent.press(getByText('Take Photo'));

    await waitFor(() => {
      expect(mockOnCameraPress).toHaveBeenCalled();
      expect(bottomSheetRef.current?.close).toHaveBeenCalled();
    });
  });

  it('calls onGalleryPress and closes when gallery option is pressed', async () => {
    const { getByText } = render(
      <ImagePickerBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    fireEvent.press(getByText('Choose from Gallery'));

    await waitFor(() => {
      expect(mockOnGalleryPress).toHaveBeenCalled();
      expect(bottomSheetRef.current?.close).toHaveBeenCalled();
    });
  });

  it('calls dismiss when close button is pressed', () => {
    const { getByTestId } = render(
      <ImagePickerBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    fireEvent.press(getByTestId('close-button'));
    expect(bottomSheetRef.current?.close).toHaveBeenCalled();
  });
});
