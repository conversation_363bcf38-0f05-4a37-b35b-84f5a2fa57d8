import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import ColorPickerBottomSheet, {
  ColorPickerBottomSheetRef,
} from '@/src/components/pickers/ColorPickerBottomSheet';
import { useTheme } from '@/src/hooks/useTheme';
import { getPredefinedOptionsForType } from '@/src/constants/predefinedVariants';

// Mock dependencies
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
      textPrimary: '#000',
      textSecondary: '#888',
      border: '#ccc',
      background: '#fff',
      mutedForeground: '#aaa',
    },
  }),
}));

jest.mock('@/src/constants/predefinedVariants', () => ({
  getPredefinedOptionsForType: jest.fn(() => [
    { display_value: 'Red', value: 'red', color_code: '#FF0000' },
    { display_value: 'Blue', value: 'blue', color_code: '#0000FF' },
    { display_value: 'Green', value: 'green', color_code: '#00FF00' },
    { display_value: 'White', value: 'white', color_code: '#FFFFFF' },
  ]),
}));

describe('ColorPickerBottomSheet', () => {
  const mockOnSelect = jest.fn();
  const mockOnClose = jest.fn();

  const defaultProps = {
    onSelect: mockOnSelect,
    onClose: mockOnClose,
  };

  let bottomSheetRef: React.RefObject<ColorPickerBottomSheetRef>;

  beforeEach(() => {
    jest.clearAllMocks();
    bottomSheetRef = React.createRef<ColorPickerBottomSheetRef>();
  });

  it('renders correctly when opened', async () => {
    const { getByText, getByPlaceholderText } = render(
      <ColorPickerBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    await waitFor(() => {
      expect(getByText('Select Color')).toBeTruthy();
      expect(getByPlaceholderText('Search colors...')).toBeTruthy();
      expect(getByText('Red')).toBeTruthy();
      expect(getByText('Blue')).toBeTruthy();
    });
  });

  it('calls onSelect and closes when a color is selected', async () => {
    const { getByText } = render(
      <ColorPickerBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    await waitFor(() => {
      fireEvent.press(getByText('Red'));
    });

    expect(mockOnSelect).toHaveBeenCalledWith({
      display_value: 'Red',
      value: 'red',
      color_code: '#FF0000',
    });
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('filters colors based on search query', async () => {
    const { getByPlaceholderText, getByText, queryByText } = render(
      <ColorPickerBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    const searchInput = getByPlaceholderText('Search colors...');
    fireEvent.changeText(searchInput, 'blue');

    await waitFor(() => {
      expect(getByText('Blue')).toBeTruthy();
      expect(queryByText('Red')).toBeNull();
    });
  });

  it('shows empty state when no colors match search', async () => {
    const { getByPlaceholderText, getByText } = render(
      <ColorPickerBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    const searchInput = getByPlaceholderText('Search colors...');
    fireEvent.changeText(searchInput, 'xyz');

    await waitFor(() => {
      expect(getByText('No colors found for "xyz"')).toBeTruthy();
    });
  });

  it('displays selected color with checkmark', async () => {
    const { getByText, getByTestId } = render(
      <ColorPickerBottomSheet ref={bottomSheetRef} {...defaultProps} selectedColor="red" />
    );

    act(() => {
      bottomSheetRef.current?.open();
    });

    await waitFor(() => {
      expect(getByText('Red')).toBeTruthy();
      expect(getByTestId('checkmark-red')).toBeTruthy(); // Assuming a testID for the checkmark
    });
  });
});
