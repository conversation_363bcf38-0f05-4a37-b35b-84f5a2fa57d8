import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { PhoneLinkingSection } from '../../../../src/components/settings/PhoneLinkingSection';
import { Alert } from 'react-native';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/backend/supabase/services/common/settingsService', () => ({
  linkCustomerPhone: jest.fn(),
  verifyPhoneOTP: jest.fn(),
}));

jest.mock('@/src/components/ui/Input', () => ({
  Input: jest.fn(({ value, onChangeText, placeholder, error, keyboardType, editable, containerStyle }) => (
    <TextInput
      testID={`input-${placeholder}`}
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      keyboardType={keyboardType}
      editable={editable}
    />
  )),
}));

jest.mock('@/src/components/ui/Button', () => ({
  Button: jest.fn(({ title, onPress, disabled, variant, icon, style }) => (
    <TouchableOpacity testID={`button-${title}`} onPress={onPress} disabled={disabled}>
      <Text>{title}</Text>
      {icon}
    </TouchableOpacity>
  )),
}));

jest.spyOn(Alert, 'alert');

describe('PhoneLinkingSection', () => {
  const mockOnPhoneUpdated = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mocks for each test
    require('@/backend/supabase/services/common/settingsService').linkCustomerPhone.mockReset();
    require('@/backend/supabase/services/common/settingsService').verifyPhoneOTP.mockReset();
  });

  it('renders correctly for phone user (update phone)', () => {
    const { getByText, getByPlaceholderText } = render(
      <PhoneLinkingSection
        currentPhone="+91*********0"
        registrationType="phone"
        onPhoneUpdated={mockOnPhoneUpdated}
      />
    );
    expect(getByText('Update Phone Number')).toBeTruthy();
    expect(getByText('+91 ************')).toBeTruthy();
    expect(getByPlaceholderText('XXX XXX XXXX')).toBeTruthy();
  });

  it('renders correctly for non-phone user (link phone)', () => {
    const { getByText, getByPlaceholderText } = render(
      <PhoneLinkingSection
        registrationType="email"
        onPhoneUpdated={mockOnPhoneUpdated}
      />
    );
    expect(getByText('Link Phone Number')).toBeTruthy();
    expect(getByText('No phone number is currently linked to your account.')).toBeTruthy();
    expect(getByPlaceholderText('XXX XXX XXXX')).toBeTruthy();
  });

  it('validates phone input', async () => {
    const { getByTestId, getByText } = render(
      <PhoneLinkingSection registrationType="email" />
    );
    const phoneInput = getByTestId('input-XXX XXX XXXX');
    const linkButton = getByTestId('button-Link Phone');

    fireEvent.changeText(phoneInput, '123'); // Too short
    fireEvent.press(linkButton);
    await waitFor(() => {
      expect(getByText('Phone number must be 10 digits')).toBeTruthy();
    });

    fireEvent.changeText(phoneInput, '**********'); // Invalid start digit
    fireEvent.press(linkButton);
    await waitFor(() => {
      expect(getByText('Phone number must start with 6, 7, 8, or 9')).toBeTruthy();
    });

    fireEvent.changeText(phoneInput, '*********'); // Too short
    fireEvent.press(linkButton);
    await waitFor(() => {
      expect(getByText('Phone number must be 10 digits')).toBeTruthy();
    });
  });

  it('sends OTP and transitions to OTP step on successful phone link', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerPhone.mockResolvedValue({
      success: true, requiresOTP: true
    });

    const { getByTestId, getByText } = render(
      <PhoneLinkingSection registrationType="email" />
    );
    const phoneInput = getByTestId('input-XXX XXX XXXX');
    const linkButton = getByTestId('button-Link Phone');

    fireEvent.changeText(phoneInput, '*********0');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(require('@/backend/supabase/services/common/settingsService').linkCustomerPhone).toHaveBeenCalledWith('*********0');
      expect(getByText("We've sent a 6-digit verification code to your phone number.")).toBeTruthy();
      expect(getByTestId('input-Enter 6-digit code')).toBeTruthy();
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Verification code sent to your phone!');
    });
  });

  it('links phone directly if no OTP is required', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerPhone.mockResolvedValue({
      success: true, requiresOTP: false
    });

    const { getByTestId, getByText } = render(
      <PhoneLinkingSection registrationType="email" onPhoneUpdated={mockOnPhoneUpdated} />
    );
    const phoneInput = getByTestId('input-XXX XXX XXXX');
    const linkButton = getByTestId('button-Link Phone');

    fireEvent.changeText(phoneInput, '*********0');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(require('@/backend/supabase/services/common/settingsService').linkCustomerPhone).toHaveBeenCalledWith('*********0');
      expect(getByText('Phone number has been linked to your account.')).toBeTruthy();
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Phone linked successfully!');
      expect(mockOnPhoneUpdated).toHaveBeenCalled();
    });
  });

  it('validates OTP input', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerPhone.mockResolvedValue({
      success: true, requiresOTP: true
    });

    const { getByTestId, getByText } = render(
      <PhoneLinkingSection registrationType="email" />
    );
    const phoneInput = getByTestId('input-XXX XXX XXXX');
    const linkButton = getByTestId('button-Link Phone');

    fireEvent.changeText(phoneInput, '*********0');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(getByTestId('input-Enter 6-digit code')).toBeTruthy();
    });

    const otpInput = getByTestId('input-Enter 6-digit code');
    const verifyButton = getByTestId('button-Verify Code');

    fireEvent.changeText(otpInput, '123'); // Invalid OTP length
    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(getByText('Verification code must be 6 digits')).toBeTruthy();
    });

    fireEvent.changeText(otpInput, 'abcde'); // Invalid OTP characters
    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(getByText('Verification code must contain only numbers')).toBeTruthy();
    });
  });

  it('verifies OTP and links phone successfully', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerPhone.mockResolvedValue({
      success: true, requiresOTP: true
    });
    require('@/backend/supabase/services/common/settingsService').verifyPhoneOTP.mockResolvedValue({
      success: true
    });

    const { getByTestId, getByText } = render(
      <PhoneLinkingSection registrationType="email" onPhoneUpdated={mockOnPhoneUpdated} />
    );
    const phoneInput = getByTestId('input-XXX XXX XXXX');
    const linkButton = getByTestId('button-Link Phone');

    fireEvent.changeText(phoneInput, '*********0');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(getByTestId('input-Enter 6-digit code')).toBeTruthy();
    });

    const otpInput = getByTestId('input-Enter 6-digit code');
    const verifyButton = getByTestId('button-Verify Code');

    fireEvent.changeText(otpInput, '123456');
    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(require('@/backend/supabase/services/common/settingsService').verifyPhoneOTP).toHaveBeenCalledWith('*********0', '123456');
      expect(getByText('Phone number has been linked to your account.')).toBeTruthy();
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Phone linked successfully!');
      expect(mockOnPhoneUpdated).toHaveBeenCalled();
    });
  });

  it('goes back to phone step from OTP step', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerPhone.mockResolvedValue({
      success: true, requiresOTP: true
    });

    const { getByTestId, getByText } = render(
      <PhoneLinkingSection registrationType="email" />
    );
    const phoneInput = getByTestId('input-XXX XXX XXXX');
    const linkButton = getByTestId('button-Link Phone');

    fireEvent.changeText(phoneInput, '*********0');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(getByTestId('input-Enter 6-digit code')).toBeTruthy();
    });

    const backButton = getByTestId('button-Back');
    fireEvent.press(backButton);

    await waitFor(() => {
      expect(getByTestId('input-XXX XXX XXXX')).toBeTruthy();
      expect(getByTestId('button-Link Phone')).toBeTruthy();
    });
  });
});
