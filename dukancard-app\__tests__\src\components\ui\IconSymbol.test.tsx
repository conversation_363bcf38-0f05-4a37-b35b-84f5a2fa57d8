import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { IconSymbol } from '@/src/components/ui/IconSymbol';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';

// Mock MaterialIcons
jest.mock('@expo/vector-icons/MaterialIcons', () => ({
  __esModule: true,
  default: jest.fn(() => null), // Mock MaterialIcons to return null
}));

describe('IconSymbol (Android/Web Fallback)', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (MaterialIcons as jest.Mock).mockImplementation((props) => <Text testID="material-icon" {...props}>MaterialIcon</Text>);
  });

  it('renders MaterialIcons with correct mapped name for house.fill', () => {
    render(<IconSymbol name="house.fill" color="blue" />);
    expect(MaterialIcons).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'home',
        color: 'blue',
        size: 24,
      }),
      {}
    );
  });

  it('renders MaterialIcons with correct mapped name for paperplane.fill', () => {
    render(<IconSymbol name="paperplane.fill" color="green" size={30} />);
    expect(MaterialIcons).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'send',
        color: 'green',
        size: 30,
      }),
      {}
    );
  });

  it('renders MaterialIcons with correct mapped name for chevron.left.forwardslash.chevron.right', () => {
    render(<IconSymbol name="chevron.left.forwardslash.chevron.right" color="purple" />);
    expect(MaterialIcons).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'code',
        color: 'purple',
        size: 24,
      }),
      {}
    );
  });

  it('renders MaterialIcons with correct mapped name for chevron.right', () => {
    render(<IconSymbol name="chevron.right" color="orange" />);
    expect(MaterialIcons).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'chevron-right',
        color: 'orange',
        size: 24,
      }),
      {}
    );
  });

  it('applies custom style prop', () => {
    render(<IconSymbol name="house.fill" color="black" style={{ opacity: 0.5 }} />);
    expect(MaterialIcons).toHaveBeenCalledWith(
      expect.objectContaining({
        style: expect.objectContaining({
          opacity: 0.5,
        }),
      }),
      {}
    );
  });
});