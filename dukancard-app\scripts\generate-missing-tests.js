const fs = require('fs');
const path = require('path');

const projectRoot = path.resolve(__dirname, '..'); // dukancard-app directory
const testDir = path.join(projectRoot, '__tests__');

const excludedDirs = [
    'node_modules',
    '.git',
    '.expo',
    'android',
    'releases',
    'keystore',
    '.security-backup',
    '.vscode',
    '__mocks__',
    'e2e',
    'docs',
    'instructions',
    'context-engineering-intro-main',
    'images',
    'assets',
    'backend',
    'scripts',
    'styles',
    '__tests__',
    'skeletons' // Exclude skeleton components
];

const excludedFiles = [
    'package.json',
    'package-lock.json',
    'README.md',
    'version.json',
    'app.config.js',
    'babel.config.js',
    'metro.config.js',
    'metro.config.security.js',
    'tsconfig.json',
    'eslint.config.js',
    'jest.config.js',
    'jest.setup.js',
    'expo-env.d.ts',
    'N8N MCP Guidelines.md',
    'CREATE-TEST-FILES.md',
    'dependency-graph.json',
    'DEVELOPER_TASK_PROCESS.md',
    'impact_analysis_prompt.md',
    '.detoxrc.js',
    '.eslintrc.js',
    '.dependency-cruiser.js'
];

function getSourceFiles(dir, fileList = []) {
    const items = fs.readdirSync(dir);

    for (const item of items) {
        const fullPath = path.join(dir, item);
        try {
            const stat = fs.statSync(fullPath);

            if (stat.isDirectory()) {
                if (!excludedDirs.includes(item)) {
                    getSourceFiles(fullPath, fileList);
                }
            } else {
                if (
                    (item.endsWith('.ts') || item.endsWith('.tsx') || item.endsWith('.js') || item.endsWith('.jsx')) &&
                    !item.includes('.test.') &&
                    !excludedFiles.includes(item) &&
                    !dir.includes('types') &&
                    !dir.includes('constants') &&
                    !dir.includes('hooks') &&
                    !dir.includes('services/discovery/types') &&
                    !dir.includes('utils') &&
                    item !== 'index.ts'
                ) {
                    fileList.push(fullPath);
                }
            }
        } catch (error) {
            console.error(`Error processing path: ${fullPath}`, error);
        }
    }
    return fileList;
}

function getMissingTestFiles() {
    const sourceFiles = getSourceFiles(projectRoot);
    const missingTests = [];

    for (const sourceFile of sourceFiles) {
        const relativePath = path.relative(projectRoot, sourceFile);
        
        const ext = path.extname(relativePath);
        const base = path.basename(relativePath, ext);
        const dir = path.dirname(relativePath);
        
        const testFileName = `${base}.test${ext}`;
        const testFilePath = path.join(testDir, dir, testFileName);

        if (!fs.existsSync(testFilePath)) {
            missingTests.push(sourceFile);
        }
    }

    return missingTests;
}

const missingTestFiles = getMissingTestFiles();
const outputPath = path.join(projectRoot, 'missing_tests.txt');

if (missingTestFiles.length > 0) {
    const outputContent = "Files missing unit tests:\n" + missingTestFiles.join('\r\n');
    fs.writeFileSync(outputPath, outputContent, 'utf8');
    console.log(`Found ${missingTestFiles.length} files missing unit tests. List saved to missing_tests.txt`);
} else {
    fs.writeFileSync(outputPath, 'All source files have corresponding unit tests.', 'utf8');
    console.log("All source files have corresponding unit tests.");
}