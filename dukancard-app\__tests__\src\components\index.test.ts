import * as Components from '../../../src/components';

// Mock individual UI components to ensure they are re-exported
jest.mock('../../../src/components/ui/Button', () => ({ Button: 'MockButton' }));
jest.mock('../../../src/components/ui/Input', () => ({ Input: 'MockInput' }));
jest.mock('../../../src/components/ui/OTPInput', () => ({ OTPInput: 'MockOTPInput' }));
jest.mock('../../../src/components/ui/Toast', () => ({
  ToastProvider: 'MockToastProvider',
  useToast: () => ({ show: jest.fn() }),
}));
jest.mock('../../../src/components/ui/DukancardLogo', () => ({ DukancardLogo: 'MockDukancardLogo' }));
jest.mock('../../../src/components/ui/GoogleIcon', () => ({ GoogleIcon: 'MockGoogleIcon' }));

// Mock feature components index
jest.mock('../../../src/components/features', () => ({
  FeatureComponentA: 'MockFeatureComponentA',
  FeatureComponentB: 'MockFeatureComponentB',
}));

// Mock layout components
jest.mock('../../../src/components/layout/ScreenContainer', () => ({
  AuthScreenContainer: 'MockAuthScreenContainer',
  DashboardScreenContainer: 'MockDashboardScreenContainer',
  FormScreenContainer: 'MockFormScreenContainer',
  OnboardingScreenContainer: 'MockOnboardingScreenContainer',
  ScreenContainer: 'MockScreenContainer',
}));
jest.mock('../../../src/components/layout/AuthContainer', () => ({
  AuthCard: 'MockAuthCard',
  AuthContainer: 'MockAuthContainer',
  AuthFooter: 'MockAuthFooter',
  AuthHeader: 'MockAuthHeader',
}));
jest.mock('../../../src/components/layout/OnboardingContainer', () => ({
  OnboardingCard: 'MockOnboardingCard',
  OnboardingContainer: 'MockOnboardingContainer',
}));
jest.mock('../../../src/components/layout/DashboardContainer', () => ({
  DashboardCard: 'MockDashboardCard',
  DashboardContainer: 'MockDashboardContainer',
  DashboardSection: 'MockDashboardSection',
}));

// Mock legacy components
jest.mock('../../../src/components/AuthGuard', () => ({ AuthGuard: 'MockAuthGuard' }));
jest.mock('../../../src/components/ErrorBoundary', () => ({ ErrorBoundary: 'MockErrorBoundary' }));
jest.mock('../../../src/components/Collapsible', () => ({ Collapsible: 'MockCollapsible' }));
jest.mock('../../../src/components/ExternalLink', () => ({ ExternalLink: 'MockExternalLink' }));
jest.mock('../../../src/components/HapticTab', () => ({ HapticTab: 'MockHapticTab' }));
jest.mock('../../../src/components/HelloWave', () => ({ HelloWave: 'MockHelloWave' }));
jest.mock('../../../src/components/ParallaxScrollView', () => ({ default: 'MockParallaxScrollView' }));
jest.mock('../../../src/components/ThemedText', () => ({ ThemedText: 'MockThemedText' }));
jest.mock('../../../src/components/ThemedView', () => ({ ThemedView: 'MockThemedView' }));

describe('Centralized Components Export', () => {
  // UI Components
  it('should export Button', () => {
    expect(Components.Button).toBe('MockButton');
  });
  it('should export Input', () => {
    expect(Components.Input).toBe('MockInput');
  });
  it('should export OTPInput', () => {
    expect(Components.OTPInput).toBe('MockOTPInput');
  });
  it('should export ToastProvider', () => {
    expect(Components.ToastProvider).toBe('MockToastProvider');
  });
  it('should export useToast', () => {
    expect(typeof Components.useToast).toBe('function');
  });
  it('should export DukancardLogo', () => {
    expect(Components.DukancardLogo).toBe('MockDukancardLogo');
  });
  it('should export GoogleIcon', () => {
    expect(Components.GoogleIcon).toBe('MockGoogleIcon');
  });

  // Feature Components
  it('should export FeatureComponentA', () => {
    expect(Components.FeatureComponentA).toBe('MockFeatureComponentA');
  });
  it('should export FeatureComponentB', () => {
    expect(Components.FeatureComponentB).toBe('MockFeatureComponentB');
  });

  // Layout Components
  it('should export AuthScreenContainer', () => {
    expect(Components.AuthScreenContainer).toBe('MockAuthScreenContainer');
  });
  it('should export DashboardScreenContainer', () => {
    expect(Components.DashboardScreenContainer).toBe('MockDashboardScreenContainer');
  });
  it('should export FormScreenContainer', () => {
    expect(Components.FormScreenContainer).toBe('MockFormScreenContainer');
  });
  it('should export OnboardingScreenContainer', () => {
    expect(Components.OnboardingScreenContainer).toBe('MockOnboardingScreenContainer');
  });
  it('should export ScreenContainer', () => {
    expect(Components.ScreenContainer).toBe('MockScreenContainer');
  });
  it('should export AuthCard', () => {
    expect(Components.AuthCard).toBe('MockAuthCard');
  });
  it('should export AuthContainer', () => {
    expect(Components.AuthContainer).toBe('MockAuthContainer');
  });
  it('should export AuthFooter', () => {
    expect(Components.AuthFooter).toBe('MockAuthFooter');
  });
  it('should export AuthHeader', () => {
    expect(Components.AuthHeader).toBe('MockAuthHeader');
  });
  it('should export OnboardingCard', () => {
    expect(Components.OnboardingCard).toBe('MockOnboardingCard');
  });
  it('should export OnboardingContainer', () => {
    expect(Components.OnboardingContainer).toBe('MockOnboardingContainer');
  });
  it('should export DashboardCard', () => {
    expect(Components.DashboardCard).toBe('MockDashboardCard');
  });
  it('should export DashboardContainer', () => {
    expect(Components.DashboardContainer).toBe('MockDashboardContainer');
  });
  it('should export DashboardSection', () => {
    expect(Components.DashboardSection).toBe('MockDashboardSection');
  });

  // Legacy exports
  it('should export AuthGuard', () => {
    expect(Components.AuthGuard).toBe('MockAuthGuard');
  });
  it('should export ErrorBoundary', () => {
    expect(Components.ErrorBoundary).toBe('MockErrorBoundary');
  });
  it('should export Collapsible', () => {
    expect(Components.Collapsible).toBe('MockCollapsible');
  });
  it('should export ExternalLink', () => {
    expect(Components.ExternalLink).toBe('MockExternalLink');
  });
  it('should export HapticTab', () => {
    expect(Components.HapticTab).toBe('MockHapticTab');
  });
  it('should export HelloWave', () => {
    expect(Components.HelloWave).toBe('MockHelloWave');
  });
  it('should export ParallaxScrollView', () => {
    expect(Components.ParallaxScrollView).toBe('MockParallaxScrollView');
  });
  it('should export ThemedText', () => {
    expect(Components.ThemedText).toBe('MockThemedText');
  });
  it('should export ThemedView', () => {
    expect(Components.ThemedView).toBe('MockThemedView');
  });
});
