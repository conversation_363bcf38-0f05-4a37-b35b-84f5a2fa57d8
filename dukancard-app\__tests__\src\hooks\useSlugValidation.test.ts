import { renderHook, act, waitFor } from '@testing-library/react-hooks';
import { useSlugValidation } from '@/src/hooks/useSlugValidation';
import { checkSlugAvailability } from '@/backend/supabase/services/common/onboardingService';

// Mock external dependencies
jest.mock('@/backend/supabase/services/common/onboardingService');

describe('useSlugValidation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    (checkSlugAvailability as jest.Mock).mockResolvedValue({ available: true, error: null });
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('should return initial state', () => {
    const { result } = renderHook(() => useSlugValidation());
    expect(result.current.isChecking).toBe(false);
    expect(result.current.isAvailable).toBeNull();
    expect(result.current.error).toBeNull();
  });

  describe('validateSlugFormat', () => {
    it('should return true for valid slug format', () => {
      const { result } = renderHook(() => useSlugValidation());
      expect(result.current.validateSlugFormat('my-business-slug')).toEqual({ isValid: true });
    });

    it('should return false for slug too short', () => {
      const { result } = renderHook(() => useSlugValidation());
      expect(result.current.validateSlugFormat('ab')).toEqual({ isValid: false, error: 'URL must be at least 3 characters' });
    });

    it('should return false for slug too long', () => {
      const { result } = renderHook(() => useSlugValidation());
      expect(result.current.validateSlugFormat('a'.repeat(51))).toEqual({ isValid: false, error: 'URL must be less than 50 characters' });
    });

    it('should return false for invalid characters', () => {
      const { result } = renderHook(() => useSlugValidation());
      expect(result.current.validateSlugFormat('My Business')).toEqual({ isValid: false, error: 'Invalid format (lowercase, numbers, hyphens only)' });
      expect(result.current.validateSlugFormat('my_business')).toEqual({ isValid: false, error: 'Invalid format (lowercase, numbers, hyphens only)' });
    });

    it('should return false for reserved words', () => {
      const { result } = renderHook(() => useSlugValidation());
      expect(result.current.validateSlugFormat('admin')).toEqual({ isValid: false, error: 'This URL is reserved and cannot be used' });
      expect(result.current.validateSlugFormat('dukancard')).toEqual({ isValid: false, error: 'This URL is reserved and cannot be used' });
    });
  });

  describe('validateSlug', () => {
    it('should debounce the availability check', async () => {
      const { result } = renderHook(() => useSlugValidation());

      act(() => {
        result.current.validateSlug('test-slug', 500);
      });
      expect(result.current.isChecking).toBe(true);
      expect(checkSlugAvailability).not.toHaveBeenCalled();

      jest.advanceTimersByTime(499);
      expect(checkSlugAvailability).not.toHaveBeenCalled();

      jest.advanceTimersByTime(1);
      await waitFor(() => expect(checkSlugAvailability).toHaveBeenCalledWith('test-slug'));
      expect(result.current.isChecking).toBe(false);
      expect(result.current.isAvailable).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it('should handle successful slug availability', async () => {
      const { result } = renderHook(() => useSlugValidation());

      act(() => {
        result.current.validateSlug('available-slug');
      });
      jest.runAllTimers();

      await waitFor(() => {
        expect(result.current.isAvailable).toBe(true);
        expect(result.current.error).toBeNull();
      });
    });

    it('should handle unavailable slug', async () => {
      (checkSlugAvailability as jest.Mock).mockResolvedValue({ available: false, error: null });
      const { result } = renderHook(() => useSlugValidation());

      act(() => {
        result.current.validateSlug('taken-slug');
      });
      jest.runAllTimers();

      await waitFor(() => {
        expect(result.current.isAvailable).toBe(false);
        expect(result.current.error).toBe('This URL is already taken');
      });
    });

    it('should handle API error during availability check', async () => {
      (checkSlugAvailability as jest.Mock).mockResolvedValue({ success: false, error: 'API Error' });
      const { result } = renderHook(() => useSlugValidation());

      act(() => {
        result.current.validateSlug('error-slug');
      });
      jest.runAllTimers();

      await waitFor(() => {
        expect(result.current.isAvailable).toBeNull();
        expect(result.current.error).toBe('Error checking availability. Please try again.');
      });
    });

    it('should immediately set error for invalid format', () => {
      const { result } = renderHook(() => useSlugValidation());

      act(() => {
        result.current.validateSlug('inv@lid');
      });

      expect(result.current.isChecking).toBe(false);
      expect(result.current.isAvailable).toBe(false);
      expect(result.current.error).toBe('Invalid format (lowercase, numbers, hyphens only)');
      expect(checkSlugAvailability).not.toHaveBeenCalled();
    });
  });

  it('reset should clear the state', () => {
    const { result } = renderHook(() => useSlugValidation());

    act(() => {
      result.current.validateSlug('test-slug');
    });
    jest.runAllTimers();

    expect(result.current.isAvailable).toBe(true);

    act(() => {
      result.current.reset();
    });

    expect(result.current.isChecking).toBe(false);
    expect(result.current.isAvailable).toBeNull();
    expect(result.current.error).toBeNull();
  });
});