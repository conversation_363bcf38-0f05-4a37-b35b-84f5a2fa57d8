import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { ActivityCard } from '../../../../src/components/profile/ActivityCard';
import { Alert } from 'react-native';

// Mock necessary modules
jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons',
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

// Mock Alert for confirmation dialogs
jest.spyOn(Alert, 'alert');

describe('ActivityCard', () => {
  const mockBusiness = {
    id: 'biz1',
    business_name: 'Test Business',
    business_slug: 'test-business',
    logo_url: 'https://example.com/logo.png',
    city: 'Someplace',
    state: 'SomeState',
    pincode: '123456',
  };

  const mockOnAction = jest.fn();
  const mockOnVisitBusiness = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders null if business data is missing', () => {
    const activity = {
      id: 'act1',
      type: 'like',
      business_profiles: null,
    };
    const { container } = render(
      <ActivityCard activity={activity} onAction={mockOnAction} />
    );
    expect(container.children.length).toBe(0);
  });

  it('renders business information correctly', () => {
    const activity = {
      id: 'act1',
      type: 'like',
      business_profiles: mockBusiness,
    };
    const { getByText, getByTestId } = render(
      <ActivityCard activity={activity} onAction={mockOnAction} />
    );

    expect(getByText('Test Business')).toBeTruthy();
    expect(getByText('Someplace, SomeState, 123456')).toBeTruthy();
    expect(getByTestId('logo-image')).toBeTruthy();
  });

  it('renders logo placeholder if logo_url is missing', () => {
    const activity = {
      id: 'act1',
      type: 'like',
      business_profiles: { ...mockBusiness, logo_url: null },
    };
    const { getByText } = render(
      <ActivityCard activity={activity} onAction={mockOnAction} />
    );
    expect(getByText('TB')).toBeTruthy(); // Initials of Test Business
  });

  it('calls onVisitBusiness when business card is pressed', () => {
    const activity = {
      id: 'act1',
      type: 'like',
      business_profiles: mockBusiness,
    };
    const { getByTestId } = render(
      <ActivityCard
        activity={activity}
        onAction={mockOnAction}
        onVisitBusiness={mockOnVisitBusiness}
      />
    );

    fireEvent.press(getByTestId('card-content'));
    expect(mockOnVisitBusiness).toHaveBeenCalledWith('test-business');
  });

  it('calls onVisitBusiness when visit button is pressed', () => {
    const activity = {
      id: 'act1',
      type: 'like',
      business_profiles: mockBusiness,
    };
    const { getByTestId } = render(
      <ActivityCard
        activity={activity}
        onAction={mockOnAction}
        onVisitBusiness={mockOnVisitBusiness}
      />
    );

    fireEvent.press(getByTestId('visit-button'));
    expect(mockOnVisitBusiness).toHaveBeenCalledWith('test-business');
  });

  describe('Activity Specific Info', () => {
    it('renders review specific info', () => {
      const activity = {
        id: 'act1',
        type: 'review',
        business_profiles: mockBusiness,
        rating: 4,
        review_text: 'Great service!',
        created_at: '2023-01-15T10:00:00Z',
        updated_at: '2023-01-15T10:00:00Z',
      };
      const { getByText } = render(
        <ActivityCard activity={activity} onAction={mockOnAction} />
      );
      expect(getByText('(4/5)')).toBeTruthy();
      expect(getByText('Great service!')).toBeTruthy();
      expect(getByText('Reviewed on 1/15/2023')).toBeTruthy();
    });

    it('renders like specific info', () => {
      const activity = {
        id: 'act1',
        type: 'like',
        business_profiles: mockBusiness,
        created_at: '2023-02-20T10:00:00Z',
      };
      const { getByText } = render(
        <ActivityCard activity={activity} onAction={mockOnAction} />
      );
      expect(getByText('Liked')).toBeTruthy();
      expect(getByText('Liked on 2/20/2023')).toBeTruthy();
    });

    it('renders subscription specific info', () => {
      const activity = {
        id: 'act1',
        type: 'subscription',
        business_profiles: mockBusiness,
        created_at: '2023-03-25T10:00:00Z',
      };
      const { getByText } = render(
        <ActivityCard activity={activity} onAction={mockOnAction} />
      );
      expect(getByText('Following')).toBeTruthy();
      expect(getByText('Subscribed on 3/25/2023')).toBeTruthy();
    });
  });

  describe('Action Button', () => {
    it('renders default action button for like activity', () => {
      const activity = {
        id: 'act1',
        type: 'like',
        business_profiles: mockBusiness,
      };
      const { getByText } = render(
        <ActivityCard activity={activity} onAction={mockOnAction} />
      );
      expect(getByText('Unlike')).toBeTruthy();
    });

    it('renders default action button for subscription activity', () => {
      const activity = {
        id: 'act1',
        type: 'subscription',
        business_profiles: mockBusiness,
      };
      const { getByText } = render(
        <ActivityCard activity={activity} onAction={mockOnAction} />
      );
      expect(getByText('Unsubscribe')).toBeTruthy();
    });

    it('renders default action button for review activity', () => {
      const activity = {
        id: 'act1',
        type: 'review',
        business_profiles: mockBusiness,
      };
      const { getByText } = render(
        <ActivityCard activity={activity} onAction={mockOnAction} />
      );
      expect(getByText('Delete')).toBeTruthy();
    });

    it('renders custom action button text and icon', () => {
      const activity = {
        id: 'act1',
        type: 'like',
        business_profiles: mockBusiness,
      };
      const { getByText } = render(
        <ActivityCard
          activity={activity}
          onAction={mockOnAction}
          actionButtonText="Custom Action"
          actionButtonIcon="star"
        />
      );
      expect(getByText('Custom Action')).toBeTruthy();
    });

    it('calls onAction with correct parameters after confirmation', async () => {
      const activity = {
        id: 'act1',
        type: 'like',
        business_profiles: mockBusiness,
      };
      const { getByText } = render(
        <ActivityCard activity={activity} onAction={mockOnAction} />
      );

      fireEvent.press(getByText('Unlike'));

      expect(Alert.alert).toHaveBeenCalledWith(
        'Unlike Business',
        'Are you sure you want to unlike Test Business?',
        expect.any(Array)
      );

      // Manually trigger the onPress of the confirm button
      const confirmButton = (Alert.alert as jest.Mock).mock.calls[0][2][1];
      await confirmButton.onPress();

      expect(mockOnAction).toHaveBeenCalledWith('act1', 'unlike');
    });

    it('shows loading indicator when action is in progress', async () => {
      const activity = {
        id: 'act1',
        type: 'like',
        business_profiles: mockBusiness,
      };
      mockOnAction.mockImplementationOnce(() => new Promise(resolve => setTimeout(resolve, 100)));

      const { getByText, getByTestId } = render(
        <ActivityCard activity={activity} onAction={mockOnAction} />
      );

      fireEvent.press(getByText('Unlike'));

      const confirmButton = (Alert.alert as jest.Mock).mock.calls[0][2][1];
      confirmButton.onPress();

      await waitFor(() => {
        expect(getByTestId('ActivityIndicator')).toBeTruthy();
      });

      await waitFor(() => {
        expect(getByText('Unlike')).toBeTruthy(); // Back to original text after loading
      }, { timeout: 200 });
    });
  });
});
