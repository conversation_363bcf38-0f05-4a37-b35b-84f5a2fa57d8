/**
 * Business Post Image Upload Service for React Native
 * Handles image compression and upload to Supabase storage
 */

import { supabase } from '@/lib/supabase';
import { compressFileUltraAggressive } from '@/src/utils/client-image-compression';
import { getBusinessPostImagePath } from '@/backend/supabase/utils/storage-paths';

export interface BusinessPostImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * Upload and process image for business post
 * Matches the Next.js implementation structure
 */
export async function uploadBusinessPostImage(
  imageUri: string,
  postId: string,
  postCreatedAt?: string
): Promise<BusinessPostImageUploadResult> {
  try {
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    const userId = user.id;

    if (!imageUri) {
      return { success: false, error: "No image provided." };
    }

    // Fetch the image and convert to blob (image is already compressed)
    const response = await fetch(imageUri);
    const blob = await response.blob();

    // Convert blob to file for upload
    const fileName = `business-post-${Date.now()}.webp`;
    const file = new File([blob], fileName, {
      type: blob.type || 'image/webp',
      lastModified: Date.now()
    });

    // Validate file size (5MB max since image is already compressed)
    const maxFileSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxFileSize) {
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
      return {
        success: false,
        error: `Compressed file size (${fileSizeMB}MB) exceeds the 5MB limit.`
      };
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      return {
        success: false,
        error: "Invalid file type. Please select JPG, PNG, WebP, or GIF images."
      };
    }

    // Image is already compressed, use it directly
    const compressedFile = file;

    // Create scalable path structure (matching Next.js implementation)
    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const bucketName = "businesses";
    const imagePath = getBusinessPostImagePath(userId, postId, 0, timestamp, postCreatedAt);

    // Convert file to array buffer for upload
    const fileBuffer = await compressedFile.arrayBuffer();

    // Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from(bucketName)
      .upload(imagePath, fileBuffer, {
        contentType: compressedFile.type,
        upsert: true
      });

    if (uploadError) {
      console.error("Business Post Image Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError.message}`,
      };
    }

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    return {
      success: true,
      url: urlData.publicUrl,
    };

  } catch (error) {
    console.error("Error processing business post image:", error);
    return {
      success: false,
      error: "Failed to process image. Please try a different image."
    };
  }
}

/**
 * Check if business has complete profile before creating posts
 * Matches the Next.js implementation
 */
export async function checkBusinessProfile(): Promise<boolean> {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return false;
    }

    const { data: profile, error: profileError } = await supabase
      .from('business_profiles')
      .select('city_slug, state_slug, locality_slug, pincode')
      .eq('id', user.id)
      .single();

    if (profileError || !profile) {
      return false;
    }

    // Check if all required profile fields are present
    const hasCompleteProfile = profile.city_slug && 
                              profile.state_slug && 
                              profile.locality_slug && 
                              profile.pincode;

    return !!hasCompleteProfile;
  } catch (error) {
    console.error('Error checking business profile:', error);
    return false;
  }
}
