import { renderHook, act } from '@testing-library/react-hooks';
import { useTheme, useThemeColors, useThemedStyles } from '@/src/hooks/useTheme';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { themes, themeConfig } from '@/lib/theme/colors';

// Mock external dependencies
jest.mock('@/src/hooks/useColorScheme');
jest.mock('@/lib/theme/colors', () => ({
  themes: {
    light: { text: '#000', background: '#FFF', primary: '#GOLD_LIGHT' },
    dark: { text: '#FFF', background: '#000', primary: '#GOLD_DARK' },
  },
  themeConfig: {
    spacing: { sm: 8, md: 16 },
    typography: { fontSize: { body: 16 }, fontWeight: { bold: '700' }, lineHeight: { normal: 24 } },
    borderRadius: { sm: 4, md: 8 },
    shadows: { sm: { elevation: 2 } },
    animations: { duration: 300 },
    breakpoints: { sm: 640 },
    brandColors: { gold: '#D4AF37' },
  },
}));

describe('useTheme', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
  });

  it('should return light theme properties when color scheme is light', () => {
    const { result } = renderHook(() => useTheme());

    expect(result.current.colors).toEqual(themes.light);
    expect(result.current.isDark).toBe(false);
    expect(result.current.isLight).toBe(true);
    expect(result.current.colorScheme).toBe('light');
    expect(result.current.spacing).toEqual(themeConfig.spacing);
    expect(result.current.typography).toEqual(themeConfig.typography);
    expect(result.current.borderRadius).toEqual(themeConfig.borderRadius);
    expect(result.current.shadows).toEqual(themeConfig.shadows);
    expect(result.current.animations).toEqual(themeConfig.animations);
    expect(result.current.breakpoints).toEqual(themeConfig.breakpoints);
    expect(result.current.brandColors).toEqual(themeConfig.brandColors);
  });

  it('should return dark theme properties when color scheme is dark', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    const { result } = renderHook(() => useTheme());

    expect(result.current.colors).toEqual(themes.dark);
    expect(result.current.isDark).toBe(true);
    expect(result.current.isLight).toBe(false);
    expect(result.current.colorScheme).toBe('dark');
  });

  it('getColor should return correct color', () => {
    const { result } = renderHook(() => useTheme());
    expect(result.current.getColor('text')).toBe('#000');
  });

  it('getSpacing should return correct spacing value', () => {
    const { result } = renderHook(() => useTheme());
    expect(result.current.getSpacing('md')).toBe(16);
  });

  it('getShadow should return correct shadow value', () => {
    const { result } = renderHook(() => useTheme());
    expect(result.current.getShadow('sm')).toEqual({ elevation: 2 });
  });

  it('getBorderRadius should return correct border radius value', () => {
    const { result } = renderHook(() => useTheme());
    expect(result.current.getBorderRadius('md')).toBe(8);
  });

  it('getFontSize should return correct font size value', () => {
    const { result } = renderHook(() => useTheme());
    expect(result.current.getFontSize('body')).toBe(16);
  });

  it('getFontWeight should return correct font weight value', () => {
    const { result } = renderHook(() => useTheme());
    expect(result.current.getFontWeight('bold')).toBe('700');
  });

  it('getLineHeight should return correct line height value', () => {
    const { result } = renderHook(() => useTheme());
    expect(result.current.getLineHeight('normal')).toBe(24);
  });
});

describe('useThemeColors', () => {
  it('should return the colors object from useTheme', () => {
    (useColorScheme as jest.Mock).mockReturnValue('light');
    const { result } = renderHook(() => useThemeColors());
    expect(result.current).toEqual(themes.light);
  });
});

describe('useThemedStyles', () => {
  it('should create styles with theme values', () => {
    (useColorScheme as jest.Mock).mockReturnValue('light');
    const styleCreator = (theme: ReturnType<typeof useTheme>) => ({
      container: {
        backgroundColor: theme.colors.background,
        padding: theme.spacing.md,
      },
      text: {
        color: theme.colors.text,
        fontSize: theme.typography.fontSize.body,
      },
    });

    const { result } = renderHook(() => useThemedStyles(styleCreator));

    expect(result.current).toEqual({
      container: {
        backgroundColor: themes.light.background,
        padding: themeConfig.spacing.md,
      },
      text: {
        color: themes.light.text,
        fontSize: themeConfig.typography.fontSize.body,
      },
    });
  });

  it('should update styles when theme changes', () => {
    const styleCreator = (theme: ReturnType<typeof useTheme>) => ({
      container: {
        backgroundColor: theme.colors.background,
      },
    });

    (useColorScheme as jest.Mock).mockReturnValue('light');
    const { result, rerender } = renderHook(() => useThemedStyles(styleCreator));
    expect(result.current.container.backgroundColor).toBe(themes.light.background);

    (useColorScheme as jest.Mock).mockReturnValue('dark');
    rerender();
    expect(result.current.container.backgroundColor).toBe(themes.dark.background);
  });
});