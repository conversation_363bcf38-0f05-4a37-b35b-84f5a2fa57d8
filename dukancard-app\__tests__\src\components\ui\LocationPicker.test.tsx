import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import { LocationPicker } from '@/src/components/ui/LocationPicker';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import * as Location from 'expo-location';
import { requestLocationPermission, reverseGeocodeCoordinates } from '@/backend/supabase/services/location/locationService';
import { useToast } from '@/src/components/ui/Toast';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme');
jest.mock('expo-location');
jest.mock('@/backend/supabase/services/location/locationService');
jest.mock('@/src/components/ui/Toast');

describe('LocationPicker', () => {
  const mockOnLocationDetected = jest.fn();
  const mockOnAddressDetected = jest.fn();
  const mockOnError = jest.fn();
  const mockToastError = jest.fn();
  const mockToastSuccess = jest.fn();
  const mockToastWarning = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (useToast as jest.Mock).mockReturnValue({
      error: mockToastError,
      success: mockToastSuccess,
      warning: mockToastWarning,
    });

    // Default mocks for location services
    (requestLocationPermission as jest.Mock).mockResolvedValue({ granted: true, canAskAgain: true });
    (Location.getCurrentPositionAsync as jest.Mock).mockResolvedValue({
      coords: { latitude: 10, longitude: 20 },
    });
    (reverseGeocodeCoordinates as jest.Mock).mockResolvedValue({
      success: true,
      pincode: '123456',
      city: 'Test City',
      state: 'Test State',
      locality: 'Test Locality',
    });
  });

  it('renders correctly with default props', () => {
    render(<LocationPicker onLocationDetected={mockOnLocationDetected} />);
    expect(screen.getByText('Use Current Location')).toBeOnTheScreen();
  });

  it('shows loading state when fetching location', async () => {
    (Location.getCurrentPositionAsync as jest.Mock).mockImplementationOnce(() => new Promise(() => {})); // Never resolve

    render(<LocationPicker onLocationDetected={mockOnLocationDetected} />);
    fireEvent.press(screen.getByText('Use Current Location'));

    expect(screen.getByText('Getting Location...')).toBeOnTheScreen();
    expect(screen.getByTestId('activity-indicator')).toBeOnTheScreen();
  });

  it('calls onLocationDetected with coordinates on success', async () => {
    render(<LocationPicker onLocationDetected={mockOnLocationDetected} />);
    fireEvent.press(screen.getByText('Use Current Location'));

    await waitFor(() => {
      expect(mockOnLocationDetected).toHaveBeenCalledWith(10, 20);
      expect(mockToastSuccess).toHaveBeenCalledWith('Location Detected', 'Your location coordinates have been captured successfully.');
    });
  });

  it('calls onAddressDetected with address details on success', async () => {
    render(
      <LocationPicker
        onLocationDetected={mockOnLocationDetected}
        onAddressDetected={mockOnAddressDetected}
      />
    );
    fireEvent.press(screen.getByText('Use Current Location'));

    await waitFor(() => {
      expect(mockOnAddressDetected).toHaveBeenCalledWith({
        pincode: '123456',
        city: 'Test City',
        state: 'Test State',
        locality: 'Test Locality',
      });
      expect(mockToastSuccess).toHaveBeenCalledWith('Location & Address Detected', 'Your exact location and nearest address details have been auto-filled from our database.');
    });
  });

  it('handles location permission denied', async () => {
    (requestLocationPermission as jest.Mock).mockResolvedValue({ granted: false, canAskAgain: true });

    render(<LocationPicker onLocationDetected={mockOnLocationDetected} onError={mockOnError} />);
    fireEvent.press(screen.getByText('Use Current Location'));

    await waitFor(() => {
      expect(mockToastError).toHaveBeenCalledWith(
        'Location Permission Required',
        expect.stringContaining('Location permission is mandatory')
      );
      expect(mockOnError).toHaveBeenCalledWith(expect.stringContaining('Location permission is mandatory'));
      expect(mockOnLocationDetected).not.toHaveBeenCalled();
    });
  });

  it('handles location permission permanently denied', async () => {
    (requestLocationPermission as jest.Mock).mockResolvedValue({ granted: false, canAskAgain: false });

    render(<LocationPicker onLocationDetected={mockOnLocationDetected} onError={mockOnError} />);
    fireEvent.press(screen.getByText('Use Current Location'));

    await waitFor(() => {
      expect(mockToastError).toHaveBeenCalledWith(
        'Location Permission Required',
        expect.stringContaining('Please go to Settings > Apps')
      );
      expect(mockOnError).toHaveBeenCalledWith(expect.stringContaining('Location permission is mandatory'));
    });
  });

  it('handles reverse geocoding failure', async () => {
    (reverseGeocodeCoordinates as jest.Mock).mockResolvedValue({ success: false });

    render(
      <LocationPicker
        onLocationDetected={mockOnLocationDetected}
        onAddressDetected={mockOnAddressDetected}
      />
    );
    fireEvent.press(screen.getByText('Use Current Location'));

    await waitFor(() => {
      expect(mockOnLocationDetected).toHaveBeenCalled();
      expect(mockOnAddressDetected).not.toHaveBeenCalled();
      expect(mockToastWarning).toHaveBeenCalledWith(
        'Location Detected',
        'GPS coordinates captured successfully, but could not auto-detect address details. Please enter your pincode manually to continue.'
      );
    });
  });

  it('handles general location fetching errors', async () => {
    (Location.getCurrentPositionAsync as jest.Mock).mockRejectedValue(new Error('GPS Error'));

    render(<LocationPicker onLocationDetected={mockOnLocationDetected} onError={mockOnError} />);
    fireEvent.press(screen.getByText('Use Current Location'));

    await waitFor(() => {
      expect(mockToastError).toHaveBeenCalledWith('Location Error', 'Failed to get your location: GPS Error');
      expect(mockOnError).toHaveBeenCalledWith('GPS Error');
      expect(mockOnLocationDetected).not.toHaveBeenCalled();
    });
  });

  it('is disabled when disabled prop is true', () => {
    render(<LocationPicker onLocationDetected={mockOnLocationDetected} disabled={true} />);
    fireEvent.press(screen.getByText('Use Current Location'));
    expect(requestLocationPermission).not.toHaveBeenCalled();
  });

  it('is disabled when loading', () => {
    (Location.getCurrentPositionAsync as jest.Mock).mockImplementationOnce(() => new Promise(() => {})); // Never resolve

    render(<LocationPicker onLocationDetected={mockOnLocationDetected} />);
    fireEvent.press(screen.getByText('Use Current Location'));
    fireEvent.press(screen.getByText('Getting Location...')); // Try pressing again while loading
    expect(requestLocationPermission).toHaveBeenCalledTimes(1); // Should only be called once
  });
});