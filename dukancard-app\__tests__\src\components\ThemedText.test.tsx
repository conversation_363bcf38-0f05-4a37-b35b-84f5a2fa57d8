import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { ThemedText } from '@/src/components/ThemedText';
import { useThemeColor } from '@/src/hooks/useThemeColor';
import { createThemedTextStyles } from '@/styles/components/ThemedText-styles';

// Mock the hooks and styles
jest.mock('@/src/hooks/useThemeColor');
jest.mock('@/styles/components/ThemedText-styles');

describe('ThemedText', () => {
  beforeEach(() => {
    (useThemeColor as jest.Mock).mockReturnValue('#000000'); // Default to black text
    (createThemedTextStyles as jest.Mock).mockReturnValue({
      default: { fontSize: 16, color: '#000' },
      title: { fontSize: 24, fontWeight: 'bold' },
      defaultSemiBold: { fontSize: 16, fontWeight: '600' },
      subtitle: { fontSize: 18 },
      link: { fontSize: 16, color: '#007AFF' },
    });
  });

  it('renders with default type and color', () => {
    render(<ThemedText>Hello World</ThemedText>);
    expect(screen.getByText('Hello World')).toBeOnTheScreen();
    expect(screen.getByText('Hello World').props.style).toContainEqual({
      fontSize: 16,
      color: '#000000',
    });
  });

  it('applies title type styles', () => {
    render(<ThemedText type="title">Title Text</ThemedText>);
    expect(screen.getByText('Title Text')).toBeOnTheScreen();
    expect(screen.getByText('Title Text').props.style).toContainEqual({
      fontSize: 24,
      fontWeight: 'bold',
    });
  });

  it('applies link type styles and ignores useThemeColor', () => {
    render(<ThemedText type="link">Link Text</ThemedText>);
    expect(screen.getByText('Link Text')).toBeOnTheScreen();
    expect(screen.getByText('Link Text').props.style).toContainEqual({
      fontSize: 16,
      color: '#007AFF',
    });
    expect(screen.getByText('Link Text').props.style).not.toContainEqual({
      color: '#000000',
    });
  });

  it('applies custom lightColor when provided', () => {
    (useThemeColor as jest.Mock).mockReturnValue('#FF0000'); // Mock for light mode
    render(<ThemedText lightColor="#FF0000">Red Text</ThemedText>);
    expect(screen.getByText('Red Text').props.style).toContainEqual({
      color: '#FF0000',
    });
  });

  it('applies custom darkColor when provided', () => {
    (useThemeColor as jest.Mock).mockReturnValue('#0000FF'); // Mock for dark mode
    render(<ThemedText darkColor="#0000FF">Blue Text</ThemedText>);
    expect(screen.getByText('Blue Text').props.style).toContainEqual({
      color: '#0000FF',
    });
  });

  it('merges custom style prop', () => {
    render(<ThemedText style={{ opacity: 0.5 }}>Faded Text</ThemedText>);
    expect(screen.getByText('Faded Text').props.style).toContainEqual({
      opacity: 0.5,
    });
  });
});