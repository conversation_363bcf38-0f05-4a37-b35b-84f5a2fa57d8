import * as Yup from 'yup';
import {
  IndianMobileSchema,
  EmailSchema,
  BusinessNameSchema,
  MemberNameSchema,
  TitleSchema,
  BusinessCategorySchema,
  BusinessSlugSchema,
  AddressLineSchema,
  PincodeSchema,
  CitySchema,
  StateSchema,
  LocalitySchema,
  BusinessStatusSchema,
  PlanIdSchema,
  IsTrialSchema,
  businessDetailsSchema,
  cardInformationSchema,
  addressInformationSchema,
  planSelectionSchema,
  completeOnboardingSchema,
  customerProfileCompletionSchema,
  validateStep,
  getStepValidationErrors,
} from '@/src/utils/validationSchemas';

describe('Validation Schemas', () => {
  // Helper function to test schema validation
  const testSchema = async (schema: Yup.AnySchema, validData: any, invalidData: any, expectedErrorPath?: string) => {
    if (validData !== undefined) {
      await expect(schema.validate(validData)).resolves.toBe(validData);
    }
    if (invalidData !== undefined) {
      await expect(schema.validate(invalidData)).rejects.toThrow(Yup.ValidationError);
      if (expectedErrorPath) {
        try {
          await schema.validate(invalidData, { abortEarly: false });
        } catch (e: any) {
          const errorPaths = e.inner ? e.inner.map((err: any) => err.path) : [e.path];
          expect(errorPaths).toContain(expectedErrorPath);
        }
      }
    }
  };

  describe('IndianMobileSchema', () => {
    it('should validate a valid Indian mobile number', async () => {
      await testSchema(IndianMobileSchema, '9876543210', '123', undefined);
    });
    it('should invalidate an invalid mobile number', async () => {
      await expect(IndianMobileSchema.validate('123')).rejects.toThrow(Yup.ValidationError);
      await expect(IndianMobileSchema.validate('987654321')).rejects.toThrow(Yup.ValidationError);
      await expect(IndianMobileSchema.validate('abcde12345')).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('EmailSchema', () => {
    it('should validate a valid email address', async () => {
      await expect(EmailSchema.validate('<EMAIL>')).resolves.toBe('<EMAIL>');
    });
    it('should invalidate an invalid email address', async () => {
      await expect(EmailSchema.validate('invalid-email')).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('BusinessNameSchema', () => {
    it('should validate a valid business name', async () => {
      await expect(BusinessNameSchema.validate('My Business')).resolves.toBe('My Business');
    });
    it('should invalidate an invalid business name', async () => {
      await expect(BusinessNameSchema.validate('a')).rejects.toThrow(Yup.ValidationError);
      await expect(BusinessNameSchema.validate('a'.repeat(101))).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('MemberNameSchema', () => {
    it('should validate a valid member name', async () => {
      await expect(MemberNameSchema.validate('John Doe')).resolves.toBe('John Doe');
    });
    it('should invalidate an invalid member name', async () => {
      await expect(MemberNameSchema.validate('a')).rejects.toThrow(Yup.ValidationError);
      await expect(MemberNameSchema.validate('a'.repeat(51))).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('TitleSchema', () => {
    it('should validate a valid title', async () => {
      await expect(TitleSchema.validate('CEO')).resolves.toBe('CEO');
    });
    it('should invalidate an invalid title', async () => {
      await expect(TitleSchema.validate('a')).rejects.toThrow(Yup.ValidationError);
      await expect(TitleSchema.validate('a'.repeat(51))).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('BusinessCategorySchema', () => {
    it('should validate a valid business category', async () => {
      await expect(BusinessCategorySchema.validate('Retail')).resolves.toBe('Retail');
    });
    it('should invalidate an invalid business category', async () => {
      await expect(BusinessCategorySchema.validate('')).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('BusinessSlugSchema', () => {
    it('should validate a valid business slug', async () => {
      await expect(BusinessSlugSchema.validate('my-business-slug')).resolves.toBe('my-business-slug');
    });
    it('should invalidate an invalid business slug', async () => {
      await expect(BusinessSlugSchema.validate('ab')).rejects.toThrow(Yup.ValidationError);
      await expect(BusinessSlugSchema.validate('invalid slug')).rejects.toThrow(Yup.ValidationError);
      await expect(BusinessSlugSchema.validate('a'.repeat(31))).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('AddressLineSchema', () => {
    it('should validate a valid address line', async () => {
      await expect(AddressLineSchema.validate('123 Main St')).resolves.toBe('123 Main St');
    });
    it('should invalidate an invalid address line', async () => {
      await expect(AddressLineSchema.validate('a')).rejects.toThrow(Yup.ValidationError);
      await expect(AddressLineSchema.validate('a'.repeat(201))).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('PincodeSchema', () => {
    it('should validate a valid pincode', async () => {
      await expect(PincodeSchema.validate('123456')).resolves.toBe('123456');
    });
    it('should invalidate an invalid pincode', async () => {
      await expect(PincodeSchema.validate('123')).rejects.toThrow(Yup.ValidationError);
      await expect(PincodeSchema.validate('12345')).rejects.toThrow(Yup.ValidationError);
      await expect(PincodeSchema.validate('1234567')).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('CitySchema', () => {
    it('should validate a valid city', async () => {
      await expect(CitySchema.validate('Mumbai')).resolves.toBe('Mumbai');
    });
    it('should invalidate an invalid city', async () => {
      await expect(CitySchema.validate('')).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('StateSchema', () => {
    it('should validate a valid state', async () => {
      await expect(StateSchema.validate('Maharashtra')).resolves.toBe('Maharashtra');
    });
    it('should invalidate an invalid state', async () => {
      await expect(StateSchema.validate('')).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('LocalitySchema', () => {
    it('should validate a valid locality', async () => {
      await expect(LocalitySchema.validate('Bandra')).resolves.toBe('Bandra');
    });
    it('should invalidate an invalid locality', async () => {
      await expect(LocalitySchema.validate('')).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('BusinessStatusSchema', () => {
    it('should validate a valid business status', async () => {
      await expect(BusinessStatusSchema.validate('online')).resolves.toBe('online');
      await expect(BusinessStatusSchema.validate('offline')).resolves.toBe('offline');
    });
    it('should invalidate an invalid business status', async () => {
      await expect(BusinessStatusSchema.validate('invalid')).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('PlanIdSchema', () => {
    it('should validate a valid plan ID', async () => {
      await expect(PlanIdSchema.validate('basic')).resolves.toBe('basic');
    });
    it('should invalidate an invalid plan ID', async () => {
      await expect(PlanIdSchema.validate('')).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('IsTrialSchema', () => {
    it('should validate a boolean value', async () => {
      await expect(IsTrialSchema.validate(true)).resolves.toBe(true);
      await expect(IsTrialSchema.validate(false)).resolves.toBe(false);
    });
    it('should invalidate a non-boolean value', async () => {
      await expect(IsTrialSchema.validate('true' as any)).rejects.toThrow(Yup.ValidationError);
    });
  });

  describe('businessDetailsSchema', () => {
    it('should validate valid business details', async () => {
      const data = { businessName: 'Test Biz', email: '<EMAIL>' };
      await expect(businessDetailsSchema.validate({ businessName: 'a' })).rejects.toThrow(Yup.ValidationError);
      try {
        await businessDetailsSchema.validate({ businessName: 'a' }, { abortEarly: false });
      } catch (e: any) {
        const errorPaths = e.inner ? e.inner.map((err: any) => err.path) : [e.path];
        expect(errorPaths).toContain('businessName');
      }
    });
  });

  describe('cardInformationSchema', () => {
    it('should validate valid card information', async () => {
      const data = {
        memberName: 'John Doe',
        title: 'Manager',
        phone: '9876543210',
        businessCategory: 'Food',
        businessSlug: 'john-doe-biz',
      };
      await expect(cardInformationSchema.validate(data)).resolves.toBe(data);
      await expect(cardInformationSchema.validate({ memberName: 'a' })).rejects.toThrow(Yup.ValidationError);
      try {
        await cardInformationSchema.validate({ memberName: 'a' }, { abortEarly: false });
      } catch (e: any) {
        const errorPaths = e.inner ? e.inner.map((err: any) => err.path) : [e.path];
        expect(errorPaths).toContain('memberName');
      }
    });
  });

  describe('addressInformationSchema', () => {
    it('should validate valid address information', async () => {
      const data = {
        addressLine: '123 Main St',
        pincode: '123456',
        locality: 'Downtown',
        city: 'Metropolis',
        state: 'State',
        businessStatus: 'online',
        latitude: 10,
        longitude: 20,
      };
      await expect(addressInformationSchema.validate(data)).resolves.toBe(data);
      await expect(addressInformationSchema.validate({ addressLine: 'a' })).rejects.toThrow(Yup.ValidationError);
      try {
        await addressInformationSchema.validate({ addressLine: 'a' }, { abortEarly: false });
      } catch (e: any) {
        const errorPaths = e.inner ? e.inner.map((err: any) => err.path) : [e.path];
        expect(errorPaths).toContain('addressLine');
      }
    });
  });

  describe('planSelectionSchema', () => {
    it('should validate valid plan selection', async () => {
      const data = { planId: 'pro' };
      await expect(planSelectionSchema.validate(data)).resolves.toBe(data);
      await expect(planSelectionSchema.validate({ planId: '' })).rejects.toThrow(Yup.ValidationError);
      try {
        await planSelectionSchema.validate({ planId: '' }, { abortEarly: false });
      } catch (e: any) {
        const errorPaths = e.inner ? e.inner.map((err: any) => err.path) : [e.path];
        expect(errorPaths).toContain('planId');
      }
    });
  });

  describe('completeOnboardingSchema', () => {
    it('should validate complete onboarding data', async () => {
      const data = {
        businessName: 'Test Biz',
        email: '<EMAIL>',
        memberName: 'John Doe',
        title: 'Manager',
        phone: '9876543210',
        businessCategory: 'Food',
        businessSlug: 'test-biz',
        addressLine: '123 Main St',
        pincode: '123456',
        locality: 'Downtown',
        city: 'Metropolis',
        state: 'State',
        businessStatus: 'online',
        planId: 'pro',
        isTrial: true,
      };
      await expect(completeOnboardingSchema.validate(data)).resolves.toBe(data);
      await expect(completeOnboardingSchema.validate({ businessName: 'a' })).rejects.toThrow(Yup.ValidationError);
      try {
        await completeOnboardingSchema.validate({ businessName: 'a' }, { abortEarly: false });
      } catch (e: any) {
        const errorPaths = e.inner ? e.inner.map((err: any) => err.path) : [e.path];
        expect(errorPaths).toContain('businessName');
      }
    });
  });

  describe('customerProfileCompletionSchema', () => {
    it('should validate customer profile completion data', async () => {
      const data = {
        name: 'Jane Doe',
        pincode: '654321',
        locality: 'Suburb',
        city: 'Townsville',
        state: 'Province',
        latitude: 30,
        longitude: 40,
      };
      await expect(customerProfileCompletionSchema.validate(data)).resolves.toBe(data);
      await expect(customerProfileCompletionSchema.validate({ name: 'a' })).rejects.toThrow(Yup.ValidationError);
      try {
        await customerProfileCompletionSchema.validate({ name: 'a' }, { abortEarly: false });
      } catch (e: any) {
        const errorPaths = e.inner ? e.inner.map((err: any) => err.path) : [e.path];
        expect(errorPaths).toContain('name');
      }
    });
  });

  describe('validateStep', () => {
    it('should validate step 1 correctly', async () => {
      const validData = { businessName: 'Test Biz', email: '<EMAIL>' };
      const invalidData = { businessName: 'a', email: 'invalid' };
      expect(await validateStep(1, validData)).toBe(true);
      expect(await validateStep(1, invalidData)).toBe(false);
    });

    it('should validate step 2 correctly', async () => {
      const validData = {
        memberName: 'John Doe',
        title: 'Manager',
        phone: '9876543210',
        businessCategory: 'Food',
        businessSlug: 'john-doe-biz',
      };
      const invalidData = { memberName: 'a', title: 'a', phone: '123', businessCategory: '', businessSlug: 'a' };
      expect(await validateStep(2, validData)).toBe(true);
      expect(await validateStep(2, invalidData)).toBe(false);
    });

    it('should validate step 3 correctly', async () => {
      const validData = {
        addressLine: '123 Main St',
        pincode: '123456',
        locality: 'Downtown',
        city: 'Metropolis',
        state: 'State',
        businessStatus: 'online',
        latitude: 10,
        longitude: 20,
      };
      const invalidData = { addressLine: 'a', pincode: '123', locality: '', city: '', state: '', businessStatus: 'invalid', latitude: undefined, longitude: undefined };
      expect(await validateStep(3, validData)).toBe(true);
      expect(await validateStep(3, invalidData)).toBe(false);
    });

    it('should validate step 4 correctly', async () => {
      const validData = { planId: 'pro' };
      const invalidData = { planId: '' };
      expect(await validateStep(4, validData)).toBe(true);
      expect(await validateStep(4, invalidData)).toBe(false);
    });

    it('should return false for invalid step number', async () => {
      expect(await validateStep(99, {})).toBe(false);
    });
  });

  describe('getStepValidationErrors', () => {
    it('should return empty object for valid step data', async () => {
      const validData = { businessName: 'Test Biz', email: '<EMAIL>' };
      expect(await getStepValidationErrors(1, validData)).toEqual({});
    });

    it('should return errors for invalid step data', async () => {
      const invalidData = { businessName: 'a', email: 'invalid' };
      const errors = await getStepValidationErrors(1, invalidData);
      expect(errors).toHaveProperty('businessName');
      expect(errors).toHaveProperty('email');
    });

    it('should return empty object for invalid step number', async () => {
      expect(await getStepValidationErrors(99, {})).toEqual({});
    });
  });
});