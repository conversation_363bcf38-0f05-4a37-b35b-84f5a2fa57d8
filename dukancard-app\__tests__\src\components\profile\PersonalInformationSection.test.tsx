import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import PersonalInformationSection from '../../../../src/components/profile/PersonalInformationSection';
import { useForm, FormProvider } from 'react-hook-form';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

const TestWrapper = ({ children, defaultValues = {} }) => {
  const methods = useForm({
    defaultValues: {
      name: '',
      ...defaultValues,
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('PersonalInformationSection', () => {
  const commonProps = {
    textColor: '#000',
    borderColor: '#ccc',
    styles: {
      section: {},
      sectionTitle: {},
      inputGroup: {},
      label: {},
      input: {},
      inputError: {},
      errorText: {},
    },
  };

  it('renders correctly with initial name', () => {
    const { getByDisplayValue } = render(
      <TestWrapper defaultValues={{ name: '<PERSON>' }}>
        <PersonalInformationSection {...commonProps} />
      </TestWrapper>
    );

    expect(getByDisplayValue('Jane Doe')).toBeTruthy();
  });

  it('updates name on text input change', () => {
    const { getByPlaceholderText } = render(
      <TestWrapper>
        <PersonalInformationSection {...commonProps} />
      </TestWrapper>
    );
    const nameInput = getByPlaceholderText('Enter your full name');

    fireEvent.changeText(nameInput, 'John Smith');
    expect(nameInput.props.value).toBe('John Smith');
  });

  it('displays error message when there is an error', () => {
    const { getByText, getByPlaceholderText } = render(
      <TestWrapper>
        <PersonalInformationSection
          {...commonProps}
          control={{
            ...useForm().control,
            _fields: {
              name: {
                _f: { name: 'name' },
                _errors: { type: 'required', message: 'Name is required' },
              },
            },
          }}
        />
      </TestWrapper>
    );

    // Simulate an error by directly setting the error state for the field
    const nameInput = getByPlaceholderText('Enter your full name');
    fireEvent.changeText(nameInput, ''); // Trigger validation

    expect(getByText('Name is required')).toBeTruthy();
  });
});
