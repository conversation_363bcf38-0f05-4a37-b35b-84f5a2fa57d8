import React, { useState, useEffect, useRef } from 'react';
import { View, Text } from 'react-native';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useIsFocused } from '@react-navigation/native';
import { formatIndianNumberShort } from '@/lib/utils';
import { realtimeService } from '@/backend/supabase/services/realtime/realtimeService';
import { createBusinessProfileStyles } from '@/styles/dashboard/business/business-profile-styles';

interface BusinessProfile {
  id: string;
  business_name: string;
  total_likes: number;
  total_subscriptions: number;
  average_rating: number;
}

interface BusinessProfileStatsProps {
  initialProfile: BusinessProfile;
  userId: string;
}

export const BusinessProfileStats: React.FC<BusinessProfileStatsProps> = ({
  initialProfile,
  userId,
}) => {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const isFocused = useIsFocused(); // Only subscribe when screen is focused
  const [profile, setProfile] = useState<BusinessProfile>(initialProfile);
  const isMountedRef = useRef(true);
  const subscriptionRef = useRef<any>(null);
  const styles = createBusinessProfileStyles(colorScheme);

  // Cleanup function for subscription
  const cleanupSubscription = () => {
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe();
      subscriptionRef.current = null;
    }
  };

  // Real-time subscription effect
  useEffect(() => {
    isMountedRef.current = true;

    if (isFocused) {
      // Screen is focused - start real-time subscription
      subscriptionRef.current = realtimeService.subscribeToBusinessUpdates(
        userId,
        (event: any) => {
          if (!isMountedRef.current) return;

          if (event.new) {
            const newData = event.new as BusinessProfile;
            // Update the profile state with new data
            setProfile(newData);
          }
        }
      );
    } else {
      // Screen is not focused - cleanup subscription
      cleanupSubscription();
    }

    // Cleanup subscription when focus changes or component unmounts
    return () => {
      cleanupSubscription();
    };
  }, [isFocused, userId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      cleanupSubscription();
    };
  }, []);

  return (
    <View style={[styles.profileStats, { backgroundColor: isDark ? '#2a2a2a' : '#F9FAFB' }]} testID="profile-stats-container">
      <View style={styles.profileStatItem}>
        <Text style={[styles.statValue, { color: isDark ? '#D4AF37' : '#D4AF37' }]} testID="stat-value">
          {formatIndianNumberShort(profile.total_likes)}
        </Text>
        <Text style={[styles.statLabel, { color: isDark ? '#999' : '#666' }]} testID="stat-label">Likes</Text>
      </View>
      <View style={styles.profileStatItem}>
        <Text style={[styles.statValue, { color: isDark ? '#D4AF37' : '#D4AF37' }]} testID="stat-value">
          {formatIndianNumberShort(profile.total_subscriptions)}
        </Text>
        <Text style={[styles.statLabel, { color: isDark ? '#999' : '#666' }]} testID="stat-label">Subscribers</Text>
      </View>
      <View style={styles.profileStatItem}>
        <Text style={[styles.statValue, { color: isDark ? '#D4AF37' : '#D4AF37' }]} testID="stat-value">
          {profile.average_rating.toFixed(1)}
        </Text>
        <Text style={[styles.statLabel, { color: isDark ? '#999' : '#666' }]} testID="stat-label">Rating</Text>
      </View>
    </View>
  );
};
