import * as Yup from 'yup';

// Indian mobile number validation schema
export const IndianMobileSchema = Yup.string()
  .matches(/^\d{10}$/, 'Invalid mobile number (must be 10 digits)')
  .required('Phone number is required');

// Email validation schema
export const EmailSchema = Yup.string()
  .email('Please enter a valid email address')
  .required('Email is required');

// Business name validation schema
export const BusinessNameSchema = Yup.string()
  .min(2, 'Business name must be at least 2 characters')
  .max(100, 'Business name must be less than 100 characters')
  .required('Business name is required');

// Member name validation schema
export const MemberNameSchema = Yup.string()
  .min(2, 'Your name must be at least 2 characters')
  .max(50, 'Your name must be less than 50 characters')
  .required('Your name is required');

// Title validation schema
export const TitleSchema = Yup.string()
  .min(2, 'Title must be at least 2 characters')
  .max(50, 'Title must be less than 50 characters')
  .required('Title/designation is required');

// Business category validation schema
export const BusinessCategorySchema = Yup.string()
  .min(1, 'Business category is required')
  .required('Business category is required');

// Business slug validation schema
export const BusinessSlugSchema = Yup.string()
  .min(3, 'URL slug must be at least 3 characters')
  .max(30, 'URL slug must be less than 30 characters')
  .matches(/^[a-z0-9-]+$/, 'URL slug can only contain lowercase letters, numbers, and hyphens')
  .required('Business slug is required');

// Address line validation schema
export const AddressLineSchema = Yup.string()
  .min(5, 'Address must be at least 5 characters')
  .max(200, 'Address must be less than 200 characters')
  .required('Address line is required');

// Pincode validation schema
export const PincodeSchema = Yup.string()
  .matches(/^\d{6}$/, 'Pincode must be exactly 6 digits')
  .required('Pincode is required');

// City validation schema
export const CitySchema = Yup.string()
  .required('City is required');

// State validation schema
export const StateSchema = Yup.string()
  .required('State is required');

// Locality validation schema
export const LocalitySchema = Yup.string()
  .required('Locality/area is required');

// Business status validation schema
export const BusinessStatusSchema = Yup.string()
  .oneOf(['online', 'offline'], 'Business status must be either online or offline')
  .required('Business status is required');

// Plan ID validation schema
export const PlanIdSchema = Yup.string()
  .min(1, 'Please select a plan')
  .required('Please select a plan');

// isTrial validation schema
export const IsTrialSchema = Yup.boolean().required().strict();

// Step 1: Business Details Schema
export const businessDetailsSchema = Yup.object().shape({
  businessName: BusinessNameSchema,
  email: EmailSchema,
});

// Step 2: Card Information Schema
export const cardInformationSchema = Yup.object().shape({
  memberName: MemberNameSchema,
  title: TitleSchema,
  phone: IndianMobileSchema,
  businessCategory: BusinessCategorySchema,
  businessSlug: BusinessSlugSchema,
});

// Step 3: Address Information Schema
export const addressInformationSchema = Yup.object().shape({
  addressLine: AddressLineSchema,
  pincode: PincodeSchema,
  locality: LocalitySchema,
  city: CitySchema,
  state: StateSchema,
  businessStatus: BusinessStatusSchema,
  latitude: Yup.number().required('GPS coordinates are required for accurate business location'),
  longitude: Yup.number().required('GPS coordinates are required for accurate business location'),
});

// Step 4: Plan Selection Schema
export const planSelectionSchema = Yup.object().shape({
  planId: PlanIdSchema,
});

// Complete onboarding schema (all steps combined)
export const completeOnboardingSchema = Yup.object().shape({
  // Step 1 fields
  businessName: BusinessNameSchema,
  email: EmailSchema,

  // Step 2 fields
  memberName: MemberNameSchema,
  title: TitleSchema,
  phone: IndianMobileSchema,
  businessCategory: BusinessCategorySchema,
  businessSlug: BusinessSlugSchema,

  // Step 3 fields
  addressLine: AddressLineSchema,
  pincode: PincodeSchema,
  locality: LocalitySchema,
  city: CitySchema,
  state: StateSchema,
  businessStatus: BusinessStatusSchema,

  // Step 4 fields
  planId: PlanIdSchema,
  isTrial: IsTrialSchema,
});

// Customer Profile Completion Schema
export const customerProfileCompletionSchema = Yup.object().shape({
  name: MemberNameSchema, // Reuse member name validation
  address: Yup.string()
    .max(100, 'Address cannot exceed 100 characters')
    .optional(),
  pincode: PincodeSchema,
  locality: LocalitySchema,
  city: CitySchema,
  state: StateSchema,
  latitude: Yup.number().required('GPS coordinates are required for accurate location'),
  longitude: Yup.number().required('GPS coordinates are required for accurate location'),
});

// Type definitions for form data
export interface BusinessDetailsFormData {
  businessName: string;
  email: string;
}

export interface CardInformationFormData {
  memberName: string;
  title: string;
  phone: string;
  businessCategory: string;
  businessSlug: string;
}

export interface AddressInformationFormData {
  addressLine: string;
  pincode: string;
  locality: string;
  city: string;
  state: string;
  businessStatus: 'online' | 'offline';
  latitude?: number;
  longitude?: number;
}

export interface PlanSelectionFormData {
  planId: string;
}

export interface CustomerProfileCompletionFormData {
  name: string;
  address?: string;
  pincode: string;
  locality: string;
  city: string;
  state: string;
  latitude: number;
  longitude: number;
}

export interface CompleteOnboardingFormData extends
  BusinessDetailsFormData,
  CardInformationFormData,
  AddressInformationFormData,
  PlanSelectionFormData {
    isTrial: boolean;
  }

// Validation helper functions
export const validateStep = async (step: number, data: any): Promise<boolean> => {
  try {
    switch (step) {
      case 1:
        await businessDetailsSchema.validate(data, { abortEarly: false });
        break;
      case 2:
        await cardInformationSchema.validate(data, { abortEarly: false });
        break;
      case 3:
        await addressInformationSchema.validate(data, { abortEarly: false });
        break;
      case 4:
        await planSelectionSchema.validate(data, { abortEarly: false });
        break;
      default:
        return false;
    }
    return true;
  } catch (error) {
    return false;
  }
};

// Get validation errors for a specific step
export const getStepValidationErrors = async (step: number, data: any): Promise<Record<string, string>> => {
  try {
    switch (step) {
      case 1:
        await businessDetailsSchema.validate(data, { abortEarly: false });
        break;
      case 2:
        await cardInformationSchema.validate(data, { abortEarly: false });
        break;
      case 3:
        await addressInformationSchema.validate(data, { abortEarly: false });
        break;
      case 4:
        await planSelectionSchema.validate(data, { abortEarly: false });
        break;
      default:
        return {};
    }
    return {};
  } catch (error: any) {
    const errors: Record<string, string> = {};
    if (error.inner) {
      error.inner.forEach((err: any) => {
        if (err.path) {
          errors[err.path] = err.message;
        }
      });
    }
    return errors;
  }
};