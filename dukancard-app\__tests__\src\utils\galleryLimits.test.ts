import { getGalleryLimit, canAddMoreGalleryImages, applyGalleryPlanLimits, getPlanDisplayName, getGalleryLimitDescription, PlanType } from '@/src/utils/galleryLimits';

describe('galleryLimits', () => {
  describe('getGalleryLimit', () => {
    it('should return correct limit for each plan type', () => {
      expect(getGalleryLimit('free')).toBe(1);
      expect(getGalleryLimit('basic')).toBe(3);
      expect(getGalleryLimit('growth')).toBe(10);
      expect(getGalleryLimit('pro')).toBe(50);
      expect(getGalleryLimit('enterprise')).toBe(100);
      expect(getGalleryLimit('trial')).toBe(3);
    });

    it('should return default limit for unknown or null plan types', () => {
      expect(getGalleryLimit(null)).toBe(1);
      expect(getGalleryLimit(undefined)).toBe(1);
      expect(getGalleryLimit('unknown')).toBe(1);
    });
  });

  describe('canAddMoreGalleryImages', () => {
    it('should return true if current count is less than limit', () => {
      expect(canAddMoreGalleryImages('free', 0)).toBe(true);
      expect(canAddMoreGalleryImages('basic', 2)).toBe(true);
      expect(canAddMoreGalleryImages('growth', 9)).toBe(true);
    });

    it('should return false if current count is equal to limit', () => {
      expect(canAddMoreGalleryImages('free', 1)).toBe(false);
      expect(canAddMoreGalleryImages('basic', 3)).toBe(false);
    });

    it('should return false if current count is greater than limit', () => {
      expect(canAddMoreGalleryImages('free', 2)).toBe(false);
      expect(canAddMoreGalleryImages('basic', 4)).toBe(false);
    });
  });

  describe('applyGalleryPlanLimits', () => {
    const mockImages = ['img1', 'img2', 'img3', 'img4', 'img5'];

    it('should return limited array based on plan type', () => {
      expect(applyGalleryPlanLimits(mockImages, 'free')).toEqual(['img1']);
      expect(applyGalleryPlanLimits(mockImages, 'basic')).toEqual(['img1', 'img2', 'img3']);
      expect(applyGalleryPlanLimits(mockImages, 'growth')).toEqual(['img1', 'img2', 'img3', 'img4', 'img5']);
    });

    it('should return all images if limit is greater than array length', () => {
      expect(applyGalleryPlanLimits(mockImages, 'pro')).toEqual(mockImages);
    });

    it('should handle empty images array', () => {
      expect(applyGalleryPlanLimits([], 'free')).toEqual([]);
    });
  });

  describe('getPlanDisplayName', () => {
    it('should return correct display name for each plan', () => {
      expect(getPlanDisplayName('free')).toBe('Free');
      expect(getPlanDisplayName('basic')).toBe('Basic');
      expect(getPlanDisplayName('growth')).toBe('Growth');
      expect(getPlanDisplayName('pro')).toBe('Pro');
      expect(getPlanDisplayName('enterprise')).toBe('Enterprise');
      expect(getPlanDisplayName('trial')).toBe('Trial');
    });

    it('should return Free for unknown or null plan types', () => {
      expect(getPlanDisplayName(null)).toBe('Free');
      expect(getPlanDisplayName(undefined)).toBe('Free');
      expect(getPlanDisplayName('unknown')).toBe('Free');
    });
  });

  describe('getGalleryLimitDescription', () => {
    it('should return correct description for single photo limit', () => {
      expect(getGalleryLimitDescription('free')).toBe('Free plan allows 1 photo.');
    });

    it('should return correct description for multiple photos limit', () => {
      expect(getGalleryLimitDescription('basic')).toBe('Basic plan allows 3 photos.');
      expect(getGalleryLimitDescription('pro')).toBe('Pro plan allows 50 photos.');
    });

    it('should handle unknown plan types gracefully', () => {
      expect(getGalleryLimitDescription('unknown')).toBe('Free plan allows 1 photo.');
    });
  });
});