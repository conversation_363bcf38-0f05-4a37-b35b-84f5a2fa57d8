import { getSortingColumn, getSortingDirection } from '@/src/services/discovery/utils/sortMappings';

describe('sortMappings', () => {
  it('getSortingColumn should return correct column for business sort options', () => {
    expect(getSortingColumn('created_desc', false)).toBe('created_at');
    expect(getSortingColumn('name_asc', false)).toBe('business_name');
    expect(getSortingColumn('likes_desc', false)).toBe('total_likes');
    expect(getSortingColumn('subscriptions_desc', false)).toBe('total_subscriptions');
    expect(getSortingColumn('rating_desc', false)).toBe('average_rating');
  });

  it('getSortingColumn should return correct column for product sort options', () => {
    expect(getSortingColumn('newest', true)).toBe('created_at');
    expect(getSortingColumn('price_low', true)).toBe('price');
    expect(getSortingColumn('name_asc', true)).toBe('name');
  });

  it('getSortingDirection should return correct direction', () => {
    expect(getSortingDirection('created_desc')).toBe(false);
    expect(getSortingDirection('name_asc')).toBe(true);
    expect(getSortingDirection('likes_desc')).toBe(false);
    expect(getSortingDirection('price_low')).toBe(true);
  });
});