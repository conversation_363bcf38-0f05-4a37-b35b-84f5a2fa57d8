import { renderHook, act } from '@testing-library/react-hooks';
import NetInfo from '@react-native-community/netinfo';
import { useNetworkStatus, isOnline, isInternetReachable } from '@/src/utils/networkStatus';

// Mock NetInfo
jest.mock('@react-native-community/netinfo', () => ({
  __esModule: true,
  default: {
    fetch: jest.fn(),
    addEventListener: jest.fn(),
  },
}));

describe('networkStatus', () => {
  let mockAddEventListener: jest.Mock;
  let mockFetch: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mockAddEventListener = NetInfo.addEventListener as jest.Mock;
    mockFetch = NetInfo.fetch as jest.Mock;

    // Default mock implementations
    mockFetch.mockResolvedValue({
      isConnected: true,
      isInternetReachable: true,
      type: 'wifi',
    });
    mockAddEventListener.mockImplementation((callback) => {
      // Store the callback to be triggered manually
      return { remove: jest.fn() };
    });
  });

  describe('useNetworkStatus', () => {
    it('should return initial network state', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useNetworkStatus());

      // Initial state before fetch resolves
      expect(result.current.isConnected).toBe(true);
      expect(result.current.isInternetReachable).toBeNull();
      expect(result.current.type).toBeNull();

      await waitForNextUpdate();

      // State after fetch resolves
      expect(result.current.isConnected).toBe(true);
      expect(result.current.isInternetReachable).toBe(true);
      expect(result.current.type).toBe('wifi');
    });

    it('should update network state on change event', async () => {
      let listenerCallback: any;
      mockAddEventListener.mockImplementation((callback) => {
        listenerCallback = callback;
        return { remove: jest.fn() };
      });

      const { result, waitForNextUpdate } = renderHook(() => useNetworkStatus());
      await waitForNextUpdate(); // Wait for initial fetch

      act(() => {
        listenerCallback({
          isConnected: false,
          isInternetReachable: false,
          type: 'none',
        });
      });

      expect(result.current.isConnected).toBe(false);
      expect(result.current.isInternetReachable).toBe(false);
      expect(result.current.type).toBe('none');
    });

    it('should remove event listener on unmount', () => {
      const mockRemove = jest.fn();
      mockAddEventListener.mockReturnValue({ remove: mockRemove });

      const { unmount } = renderHook(() => useNetworkStatus());
      unmount();
      expect(mockRemove).toHaveBeenCalledTimes(1);
    });
  });

  describe('isOnline', () => {
    it('should return true when connected', async () => {
      mockFetch.mockResolvedValueOnce({ isConnected: true });
      await expect(isOnline()).resolves.toBe(true);
    });

    it('should return false when not connected', async () => {
      mockFetch.mockResolvedValueOnce({ isConnected: false });
      await expect(isOnline()).resolves.toBe(false);
    });

    it('should return false on error', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));
      await expect(isOnline()).resolves.toBe(false);
    });
  });

  describe('isInternetReachable', () => {
    it('should return true when internet is reachable', async () => {
      mockFetch.mockResolvedValueOnce({ isInternetReachable: true });
      await expect(isInternetReachable()).resolves.toBe(true);
    });

    it('should return false when internet is not reachable', async () => {
      mockFetch.mockResolvedValueOnce({ isInternetReachable: false });
      await expect(isInternetReachable()).resolves.toBe(false);
    });

    it('should return false on error', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));
      await expect(isInternetReachable()).resolves.toBe(false);
    });
  });
});