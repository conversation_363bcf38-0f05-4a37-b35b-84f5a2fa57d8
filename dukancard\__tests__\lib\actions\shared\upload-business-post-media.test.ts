import { uploadBusinessPostImage } from '@/lib/actions/shared/upload-business-post-media';
import { createClient, createAdminClient } from '@/utils/supabase/server';
import { getBusinessPostImagePath } from '@/lib/utils/storage-paths';

// Mock dependencies
jest.mock('@/utils/supabase/server');
jest.mock('@/lib/utils/storage-paths');

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>;
const mockCreateAdminClient = createAdminClient as jest.MockedFunction<typeof createAdminClient>;
const mockGetBusinessPostImagePath = getBusinessPostImagePath as jest.MockedFunction<typeof getBusinessPostImagePath>;

describe('uploadBusinessPostImage', () => {
  const mockUser = { id: 'user-123' };
  const mockPostId = 'post-123';
  const mockCreatedAt = '2024-01-01T00:00:00Z';
  const mockImagePath = 'users/us/er/user-123/business-posts/2024/01/post-123/image_0_1234567890.webp';
  const mockPublicUrl = 'https://example.com/storage/business/users/us/er/user-123/business-posts/2024/01/post-123/image_0_1234567890.webp';

  let mockSupabase: any;
  let mockAdminSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock regular supabase client
    mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null
        })
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: mockUser.id },
              error: null
            })
          })
        })
      })
    };

    // Mock admin supabase client
    mockAdminSupabase = {
      storage: {
        from: jest.fn().mockReturnValue({
          upload: jest.fn().mockResolvedValue({ error: null }),
          getPublicUrl: jest.fn().mockReturnValue({
            data: { publicUrl: mockPublicUrl }
          })
        })
      }
    };

    mockCreateClient.mockResolvedValue(mockSupabase);
    mockCreateAdminClient.mockReturnValue(mockAdminSupabase);
    mockGetBusinessPostImagePath.mockReturnValue(mockImagePath);
  });

  it('should upload business post image successfully', async () => {
    const mockFile = new File(['test image content'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId, mockCreatedAt);

    expect(result).toEqual({
      success: true,
      url: mockPublicUrl
    });

    expect(mockSupabase.auth.getUser).toHaveBeenCalled();
    expect(mockSupabase.from).toHaveBeenCalledWith('business_profiles');
    expect(mockGetBusinessPostImagePath).toHaveBeenCalledWith(
      mockUser.id,
      mockPostId,
      0,
      expect.any(Number),
      mockCreatedAt
    );
    expect(mockAdminSupabase.storage.from).toHaveBeenCalledWith('business');
    expect(mockAdminSupabase.storage.from().upload).toHaveBeenCalledWith(
      mockImagePath,
      expect.any(Buffer),
      {
        contentType: 'image/jpeg',
        upsert: true
      }
    );
  });

  it('should return error when user is not authenticated', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: null },
      error: { message: 'Not authenticated' }
    });

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'User not authenticated.'
    });
  });

  it('should return error when no image file is provided', async () => {
    const formData = new FormData();

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'No image file provided.'
    });
  });

  it('should return error when file size exceeds limit', async () => {
    const largeContent = 'x'.repeat(16 * 1024 * 1024); // 16MB
    const mockFile = new File([largeContent], 'large.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'File size (16.0MB) exceeds the 15MB limit.'
    });
  });

  it('should return error when file type is invalid', async () => {
    const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'Invalid file type. Please select JPG, PNG, WebP, or GIF images.'
    });
  });

  it('should return error when business profile is not found', async () => {
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: null,
      error: { message: 'Business profile not found' }
    });

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'Business profile not found. Please complete your business profile first.'
    });
  });

  it('should return error when storage upload fails', async () => {
    mockAdminSupabase.storage.from().upload.mockResolvedValue({
      error: { message: 'Storage upload failed' }
    });

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'Failed to upload image: Storage upload failed'
    });
  });

  it('should return error when public URL cannot be retrieved', async () => {
    mockAdminSupabase.storage.from().getPublicUrl.mockReturnValue({
      data: { publicUrl: null }
    });

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'Could not retrieve public URL after upload.'
    });
  });

  it('should handle unexpected errors gracefully', async () => {
    mockSupabase.auth.getUser.mockRejectedValue(new Error('Unexpected error'));

    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    const result = await uploadBusinessPostImage(formData, mockPostId);

    expect(result).toEqual({
      success: false,
      error: 'Failed to process image. Please try a different image.'
    });
  });

  it('should use correct bucket name for business', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const formData = new FormData();
    formData.append('imageFile', mockFile);

    await uploadBusinessPostImage(formData, mockPostId, mockCreatedAt);

    expect(mockAdminSupabase.storage.from).toHaveBeenCalledWith('business');
  });
});
