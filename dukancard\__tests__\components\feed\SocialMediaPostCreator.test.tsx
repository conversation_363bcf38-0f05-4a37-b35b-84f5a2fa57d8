import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { toast } from 'sonner';
import SocialMediaPostCreator from '@/components/feed/shared/SocialMediaPostCreator';
import { createCustomerPost, updateCustomerPost, deleteCustomerPost } from '@/lib/actions/customerPosts';
import { uploadCustomerPostImage } from '@/lib/actions/shared/upload-customer-post-media';
import { compressImageUltraAggressiveClient } from '@/lib/utils/client-image-compression';

// Mock dependencies
jest.mock('sonner');
jest.mock('@/lib/actions/customerPosts');
jest.mock('@/lib/actions/shared/upload-customer-post-media');
jest.mock('@/lib/utils/client-image-compression');
jest.mock('@/utils/supabase/client', () => ({
  createClient: () => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })
    }
  })
}));

const mockCreateCustomerPost = createCustomerPost as jest.MockedFunction<typeof createCustomerPost>;
const mockUpdateCustomerPost = updateCustomerPost as jest.MockedFunction<typeof updateCustomerPost>;
const mockDeleteCustomerPost = deleteCustomerPost as jest.MockedFunction<typeof deleteCustomerPost>;
const mockUploadCustomerPostImage = uploadCustomerPostImage as jest.MockedFunction<typeof uploadCustomerPostImage>;
const mockCompressImage = compressImageUltraAggressiveClient as jest.MockedFunction<typeof compressImageUltraAggressiveClient>;
const mockToast = toast as jest.Mocked<typeof toast>;

describe('SocialMediaPostCreator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockToast.success = jest.fn();
    mockToast.error = jest.fn();
  });

  it('should create a text-only post successfully', async () => {
    mockCreateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    render(<SocialMediaPostCreator />);

    // Click to expand the post creator
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Type content
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: 'Test post content' } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreateCustomerPost).toHaveBeenCalledWith({
        content: 'Test post content',
        image_url: null,
        mentioned_business_ids: []
      });
      expect(mockToast.success).toHaveBeenCalledWith('Post created successfully!');
    });
  });

  it('should create a post with image successfully', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockCompressedBlob = new Blob(['compressed'], { type: 'image/webp' });

    mockCreateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockCompressImage.mockResolvedValue({
      blob: mockCompressedBlob,
      finalSizeKB: 50
    });

    mockUploadCustomerPostImage.mockResolvedValue({
      success: true,
      url: 'https://example.com/image.webp'
    });

    mockUpdateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post updated'
    });

    render(<SocialMediaPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add content
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: 'Post with image' } });

    // Add image
    const fileInput = screen.getByLabelText(/Add image/);
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCompressImage).toHaveBeenCalledWith(mockFile, {
        maxDimension: 1200,
        targetSizeKB: 100
      });
      expect(mockCreateCustomerPost).toHaveBeenCalledWith({
        content: 'Post with image',
        image_url: null,
        mentioned_business_ids: []
      });
      expect(mockUploadCustomerPostImage).toHaveBeenCalled();
      expect(mockUpdateCustomerPost).toHaveBeenCalledWith('post-123', {
        content: 'Post with image',
        image_url: 'https://example.com/image.webp',
        mentioned_business_ids: []
      });
      expect(mockToast.success).toHaveBeenCalledWith('Post created successfully!');
    });
  });

  it('should rollback post creation when image upload fails', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockCompressedBlob = new Blob(['compressed'], { type: 'image/webp' });

    mockCreateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockCompressImage.mockResolvedValue({
      blob: mockCompressedBlob,
      finalSizeKB: 50
    });

    mockUploadCustomerPostImage.mockResolvedValue({
      success: false,
      error: 'Upload failed'
    });

    mockDeleteCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post deleted'
    });

    render(<SocialMediaPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add content and image
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: 'Post with image' } });

    const fileInput = screen.getByLabelText(/Add image/);
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreateCustomerPost).toHaveBeenCalled();
      expect(mockUploadCustomerPostImage).toHaveBeenCalled();
      expect(mockDeleteCustomerPost).toHaveBeenCalledWith('post-123');
      expect(mockToast.error).toHaveBeenCalledWith('Failed to upload image. Please try again.');
    });
  });

  it('should handle compression errors with rollback', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    mockCreateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockCompressImage.mockRejectedValue(new Error('Compression failed'));

    mockDeleteCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post deleted'
    });

    render(<SocialMediaPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add content and image
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: 'Post with image' } });

    const fileInput = screen.getByLabelText(/Add image/);
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreateCustomerPost).toHaveBeenCalled();
      expect(mockDeleteCustomerPost).toHaveBeenCalledWith('post-123');
      expect(mockToast.error).toHaveBeenCalledWith('Failed to upload image. Please try again.');
    });
  });

  it('should validate required content', async () => {
    render(<SocialMediaPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Try to submit without content
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Please add some content or an image');
      expect(mockCreateCustomerPost).not.toHaveBeenCalled();
    });
  });
});
