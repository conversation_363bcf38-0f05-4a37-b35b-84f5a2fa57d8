import React from 'react';
import { render } from '@testing-library/react-native';
import IndexScreen from '@/app/index';
import { useTheme } from '@/src/hooks/useTheme';

// Mock external modules and components
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: jest.fn(),
}));
jest.mock('lucide-react-native', () => ({
  Loader2: () => <></>,
}));
jest.mock('@/lib/theme/colors', () => ({
  responsiveFontSize: jest.fn((size) => size),
}));

describe('IndexScreen', () => {
  const mockTheme = {
    colors: {
      background: '#fff',
      primary: '#D4AF37',
    },
  };

  beforeEach(() => {
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { toJSON } = render(<IndexScreen />);
    expect(toJSON()).toBeTruthy();
  });

  it('applies correct background color from theme', () => {
    const { toJSON } = render(<IndexScreen />);
    const tree = toJSON();
    expect(tree).toMatchSnapshot();
  });
});
