import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { ThemedView } from '@/src/components/ThemedView';
import { useThemeColor } from '@/src/hooks/useThemeColor';

// Mock the useThemeColor hook
jest.mock('@/src/hooks/useThemeColor');

describe('ThemedView', () => {
  beforeEach(() => {
    (useThemeColor as jest.Mock).mockReturnValue('#FFFFFF'); // Default to white background
  });

  it('renders correctly with default background color', () => {
    render(<ThemedView />);
    expect(screen.getByTestId('themed-view')).toBeOnTheScreen();
    expect(screen.getByTestId('themed-view').props.style).toContainEqual({
      backgroundColor: '#FFFFFF',
    });
  });

  it('applies custom lightColor when provided', () => {
    (useThemeColor as jest.Mock).mockReturnValue('#FF0000'); // Mock for light mode
    render(<ThemedView lightColor="#FF0000" />);
    expect(screen.getByTestId('themed-view').props.style).toContainEqual({
      backgroundColor: '#FF0000',
    });
  });

  it('applies custom darkColor when provided', () => {
    (useThemeColor as jest.Mock).mockReturnValue('#0000FF'); // Mock for dark mode
    render(<ThemedView darkColor="#0000FF" />);
    expect(screen.getByTestId('themed-view').props.style).toContainEqual({
      backgroundColor: '#0000FF',
    });
  });

  it('merges custom style prop', () => {
    render(<ThemedView style={{ opacity: 0.5 }} />);
    expect(screen.getByTestId('themed-view').props.style).toContainEqual({
      opacity: 0.5,
    });
  });
});