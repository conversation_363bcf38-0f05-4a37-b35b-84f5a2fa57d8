import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Share, Alert } from 'react-native';
import PostShareButton from '../../src/components/post/PostShareButton';
import { useColorScheme } from '../../src/hooks/useColorScheme';
import { generatePostUrl } from '../../src/utils/postUrl';

// Mock external modules
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Share: {
    share: jest.fn(),
  },
  Alert: {
    alert: jest.fn(),
  },
}));

jest.mock('../../src/hooks/useColorScheme', () => ({
  useColorScheme: jest.fn(),
}));

jest.mock('../../src/utils/postUrl', () => ({
  generatePostUrl: jest.fn(),
}));

describe('PostShareButton', () => {
  const postId = 'test-post-id';
  const mockPostUrl = `https://dukancard.com/post/${postId}`;

  beforeEach(() => {
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (generatePostUrl as jest.Mock).mockReturnValue(mockPostUrl);
    (Share.share as jest.Mock).mockClear();
    (Alert.alert as jest.Mock).mockClear();
  });

  it('renders correctly with initial state', () => {
    const { getByText, getByTestId } = render(<PostShareButton postId={postId} />);
    expect(getByText('Share')).toBeTruthy();
    expect(getByTestId('share-icon')).toBeTruthy();
  });

  it('calls Share.share with correct parameters on press', async () => {
    (Share.share as jest.Mock).mockResolvedValue({ action: Share.sharedAction });

    const { getByText } = render(<PostShareButton postId={postId} />);
    fireEvent.press(getByText('Share'));

    await waitFor(() => {
      expect(Share.share).toHaveBeenCalledWith({
        message: `Check out this post on Dukancard: ${mockPostUrl}`,
        url: mockPostUrl,
        title: 'Dukancard Post',
      });
    });
    expect(getByText('Share')).toBeTruthy(); // Button text should revert after sharing
  });

  it('shows "Sharing..." text when sharing is in progress', async () => {
    (Share.share as jest.Mock).mockReturnValue(new Promise(() => {})); // Never resolve to simulate pending

    const { getByText } = render(<PostShareButton postId={postId} />);
    fireEvent.press(getByText('Share'));

    expect(getByText('Sharing...')).toBeTruthy();
  });

  it('handles share dismissal', async () => {
    (Share.share as jest.Mock).mockResolvedValue({ action: Share.dismissedAction });

    const { getByText } = render(<PostShareButton postId={postId} />);
    fireEvent.press(getByText('Share'));

    await waitFor(() => {
      expect(Share.share).toHaveBeenCalled();
    });
    expect(getByText('Share')).toBeTruthy();
  });

  it('shows an alert if sharing fails', async () => {
    const errorMessage = 'Network error';
    (Share.share as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const { getByText } = render(<PostShareButton postId={postId} />);
    fireEvent.press(getByText('Share'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith(
        'Share Failed',
        'Unable to share this post. Please try again.',
        [{ text: 'OK' }]
      );
    });
    expect(getByText('Share')).toBeTruthy();
  });

  it('applies custom style', () => {
    const customStyle = { backgroundColor: 'red' };
    const { getByTestId } = render(<PostShareButton postId={postId} style={customStyle} />);
    expect(getByTestId('share-button').props.style).toContainEqual(customStyle);
  });
});
