import { 
  validateQRCodeUrl,
  generateDukancardUrl,
  extractBusinessSlugFromUrl,
  isDukancardBusinessUrl,
  validateQRCodeForUser,
} from '@/src/utils/qrCodeUtils';
import { BACKEND_CONFIG } from '@/src/config/publicKeys';
import { validateBusinessSlug } from '@/backend/supabase/utils/validation';

// Mock external dependencies
jest.mock('@/src/config/publicKeys', () => ({
  BACKEND_CONFIG: {
    baseUrl: 'https://dukancard.in',
  },
}));
jest.mock('@/backend/supabase/utils/validation', () => ({
  validateBusinessSlug: jest.fn((slug: string) => {
    if (slug === 'valid-slug') return { isValid: true };
    if (slug === 'reserved') return { isValid: false, error: 'This URL is reserved and cannot be used' };
    return { isValid: false, error: 'Invalid format' };
  }),
}));

describe('qrCodeUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateQRCodeUrl', () => {
    it('should return valid for a valid Dukancard URL', () => {
      const result = validateQRCodeUrl('https://dukancard.in/valid-slug');
      expect(result.isValid).toBe(true);
      expect(result.businessSlug).toBe('valid-slug');
      expect(result.url).toBe('https://dukancard.in/valid-slug');
    });

    it('should return valid for a valid www.dukancard.in URL', () => {
      const result = validateQRCodeUrl('https://www.dukancard.in/valid-slug');
      expect(result.isValid).toBe(true);
      expect(result.businessSlug).toBe('valid-slug');
      expect(result.url).toBe('https://www.dukancard.in/valid-slug');
    });

    it('should return valid for a URL without protocol', () => {
      const result = validateQRCodeUrl('dukancard.in/valid-slug');
      expect(result.isValid).toBe(true);
      expect(result.businessSlug).toBe('valid-slug');
      expect(result.url).toBe('https://dukancard.in/valid-slug');
    });

    it('should return invalid for non-string data', () => {
      const result = validateQRCodeUrl(null as any);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid QR code data');
    });

    it('should return invalid for empty string', () => {
      const result = validateQRCodeUrl(' ');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Empty QR code data');
    });

    it('should return invalid for non-URL string', () => {
      const result = validateQRCodeUrl('just some text');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('QR code does not contain a valid URL');
    });

    it('should return invalid for non-Dukancard domain', () => {
      const result = validateQRCodeUrl('https://example.com/some-slug');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('QR code is not from Dukancard');
    });

    it('should return invalid if no business slug in path', () => {
      const result = validateQRCodeUrl('https://dukancard.in/');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('QR code does not contain a business profile URL');
    });

    it('should return invalid if business slug format is invalid', () => {
      const result = validateQRCodeUrl('https://dukancard.in/invalid-!');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid format');
    });

    it('should return invalid if business slug is reserved', () => {
      const result = validateQRCodeUrl('https://dukancard.in/reserved');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('This URL is reserved and cannot be used');
    });
  });

  describe('generateDukancardUrl', () => {
    it('should generate a correct Dukancard URL', () => {
      expect(generateDukancardUrl('test-business')).toBe('https://dukancard.in/test-business');
    });
  });

  describe('extractBusinessSlugFromUrl', () => {
    it('should extract slug from valid URL', () => {
      expect(extractBusinessSlugFromUrl('https://dukancard.in/valid-slug')).toBe('valid-slug');
    });

    it('should return null for invalid URL', () => {
      expect(extractBusinessSlugFromUrl('https://example.com/some-slug')).toBeNull();
    });
  });

  describe('isDukancardBusinessUrl', () => {
    it('should return true for valid Dukancard URL', () => {
      expect(isDukancardBusinessUrl('https://dukancard.in/valid-slug')).toBe(true);
    });

    it('should return false for invalid Dukancard URL', () => {
      expect(isDukancardBusinessUrl('https://example.com/some-slug')).toBe(false);
    });
  });

  describe('validateQRCodeForUser', () => {
    it('should return valid result for valid QR data', () => {
      const result = validateQRCodeForUser('https://dukancard.in/valid-slug');
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should return user-friendly error for invalid QR data', () => {
      const result = validateQRCodeForUser(null as any);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Unable to read QR code. Please try again.');
    });

    it('should return user-friendly error for empty QR data', () => {
      const result = validateQRCodeForUser(' ');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('QR code appears to be empty. Please try scanning again.');
    });

    it('should return user-friendly error for non-Dukancard URL', () => {
      const result = validateQRCodeForUser('https://example.com/some-slug');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('This QR code is not from Dukancard. Please scan a Dukancard business QR code.');
    });

    it('should return user-friendly error for invalid business slug format', () => {
      const result = validateQRCodeForUser('https://dukancard.in/invalid-!');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('This business URL format is not valid.');
    });
  });
});