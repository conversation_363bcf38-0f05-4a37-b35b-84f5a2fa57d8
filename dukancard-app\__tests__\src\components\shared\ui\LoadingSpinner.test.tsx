import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { LoadingSpinner } from '../../../../src/components/shared/ui/LoadingSpinner';
import { ActivityIndicator } from 'react-native';

// Mock ActivityIndicator to easily test its presence and props
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    ActivityIndicator: jest.fn((props) => <RN.View testID="mock-activity-indicator" {...props} />),
    Modal: jest.fn((props) => (props.visible ? <RN.View testID="mock-modal">{props.children}</RN.View> : null)),
  };
});

describe('LoadingSpinner', () => {
  it('renders ActivityIndicator by default', () => {
    render(<LoadingSpinner />);
    expect(screen.getByTestId('mock-activity-indicator')).toBeTruthy();
  });

  it('applies size and color props to ActivityIndicator', () => {
    render(<LoadingSpinner size="small" color="red" />);
    const indicator = screen.getByTestId('mock-activity-indicator');
    expect(indicator.props.size).toBe('small');
    expect(indicator.props.color).toBe('red');
  });

  it('displays text when provided', () => {
    render(<LoadingSpinner text="Loading data..." />);
    expect(screen.getByText('Loading data...')).toBeTruthy();
  });

  it('renders as a Modal with overlay when overlay prop is true', () => {
    render(<LoadingSpinner overlay={true} visible={true} />);
    expect(screen.getByTestId('mock-modal')).toBeTruthy();
    // Check for backdrop and overlay content within the modal
    expect(screen.getByTestId('mock-modal').findByTestId('backdrop')).toBeTruthy();
    expect(screen.getByTestId('mock-modal').findByTestId('overlay-content')).toBeTruthy();
  });

  it('does not render Modal when overlay is true but visible is false', () => {
    render(<LoadingSpinner overlay={true} visible={false} />);
    expect(screen.queryByTestId('mock-modal')).toBeNull();
  });

  it('does not render Modal when overlay is false', () => {
    render(<LoadingSpinner overlay={false} visible={true} />);
    expect(screen.queryByTestId('mock-modal')).toBeNull();
  });

  it('applies custom style to the container', () => {
    const customStyle = { backgroundColor: 'blue' };
    const { getByTestId } = render(<LoadingSpinner style={customStyle} />);
    expect(getByTestId('container').props.style).toContainEqual(customStyle);
  });
});
