import { StyleSheet } from 'react-native';
import { Theme } from '@/src/hooks/useTheme';

export const createReviewCardStyles = (theme: Theme) => {
  return StyleSheet.create({
    listItem: {
      backgroundColor: theme.isDark ? '#000000' : theme.colors.card,
    },
    businessHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    logoContainer: {
      marginRight: 12,
    },
    logo: {
      width: 40,
      height: 40,
      borderRadius: 20,
    },
    logoPlaceholder: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: '#D4AF37',
      justifyContent: 'center',
      alignItems: 'center',
    },
    logoText: {
      color: '#fff',
      fontSize: 14,
      fontWeight: 'bold',
    },
    businessInfo: {
      flex: 1,
    },
    businessName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.textPrimary,
      marginBottom: 2,
    },
    reviewDate: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    visitButton: {
      width: 28,
      height: 28,
      borderRadius: 14,
      backgroundColor: 'rgba(212, 175, 55, 0.1)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    reviewContent: {
      padding: 16,
    },
    starsContainer: {
      flexDirection: 'row',
      marginBottom: 12,
    },
    star: {
      marginRight: 4,
    },
    reviewText: {
      fontSize: 14,
      color: theme.colors.textPrimary,
      lineHeight: 20,
      marginBottom: 16,
    },
    actions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    editActionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(212, 175, 55, 0.1)',
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      flex: 1,
      marginRight: 8,
      justifyContent: 'center',
    },
    editActionText: {
      color: '#D4AF37',
      fontSize: 14,
      fontWeight: '500',
      marginLeft: 4,
    },
    deleteActionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#ff4444',
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      flex: 1,
      marginLeft: 8,
      justifyContent: 'center',
    },
    deleteActionButtonDisabled: {
      backgroundColor: '#ccc',
    },
    deleteActionText: {
      color: '#fff',
      fontSize: 14,
      fontWeight: '500',
      marginLeft: 4,
    },
    editSection: {
      marginBottom: 16,
    },
    editLabel: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.textPrimary,
      marginBottom: 8,
    },
    editTextInput: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      padding: 12,
      fontSize: 14,
      color: theme.colors.textPrimary,
      minHeight: 80,
    },
    editActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    cancelButton: {
      backgroundColor: theme.colors.muted,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      flex: 1,
      marginRight: 8,
      alignItems: 'center',
    },
    cancelButtonText: {
      color: theme.colors.textSecondary,
      fontSize: 14,
      fontWeight: '500',
    },
    saveButton: {
      backgroundColor: '#D4AF37',
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      flex: 1,
      marginLeft: 8,
      alignItems: 'center',
    },
    saveButtonDisabled: {
      backgroundColor: '#ccc',
    },
    saveButtonText: {
      color: '#fff',
      fontSize: 14,
      fontWeight: '500',
    },
    // New list design styles
    actionButtons: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    editButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: 'rgba(0, 122, 255, 0.1)',
    },
    deleteButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 68, 68, 0.1)',
    },
    deleteButtonDisabled: {
      backgroundColor: 'rgba(204, 204, 204, 0.1)',
    },
    separator: {
      height: 1,
      backgroundColor: theme.colors.border,
      marginLeft: 72, // Align with text content (logo width + margin)
    },
  });
};