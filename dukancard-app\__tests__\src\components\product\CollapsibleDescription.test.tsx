import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import CollapsibleDescription from '../../../../src/components/product/CollapsibleDescription';

describe('CollapsibleDescription', () => {
  const shortDescription = 'This is a short description.';
  const longDescription = 'This is a very long description that should span multiple lines and trigger the collapsible behavior. It needs to be long enough to exceed the default maxLines of 3.';
  const textColor = '#000000';

  it('renders correctly with a short description (not collapsed)', () => {
    const { getByText, queryByText } = render(
      <CollapsibleDescription description={shortDescription} textColor={textColor} maxLines={3} />
    );

    expect(getByText(shortDescription)).toBeTruthy();
    expect(queryByText('Show More')).toBeNull();
  });

  it('renders correctly with a long description (collapsed initially)', () => {
    const { getByText } = render(
      <CollapsibleDescription description={longDescription} textColor={textColor} maxLines={3} />
    );

    expect(getByText(longDescription)).toBeTruthy();
    expect(getByText('Show More')).toBeTruthy();
  });

  it('expands and collapses when "Show More" and "Show Less" are pressed', () => {
    const { getByText, queryByText } = render(
      <CollapsibleDescription description={longDescription} textColor={textColor} maxLines={3} />
    );

    // Initially collapsed
    expect(getByText('Show More')).toBeTruthy();
    expect(queryByText('Show Less')).toBeNull();

    // Expand
    fireEvent.press(getByText('Show More'));
    expect(queryByText('Show More')).toBeNull();
    expect(getByText('Show Less')).toBeTruthy();

    // Collapse
    fireEvent.press(getByText('Show Less'));
    expect(getByText('Show More')).toBeTruthy();
    expect(queryByText('Show Less')).toBeNull();
  });
});
