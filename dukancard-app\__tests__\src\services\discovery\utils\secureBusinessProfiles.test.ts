import { applySorting, getSecureBusinessProfiles, getSecureBusinessProfileIdsForDiscover, createSubscriptionMap, getCurrentISOTimestamp } from '@/src/services/discovery/utils/secureBusinessProfiles';
import { supabase } from '@/src/config/supabase';

// Mock external dependencies
jest.mock('@/src/config/supabase');

describe('secureBusinessProfiles', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock supabase.from chainable methods
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
      ilike: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      // Default successful response for queries that return data and count
      then: jest.fn((resolve) => resolve({ data: [], count: 0, error: null })),
    });
  });

  describe('applySorting', () => {
    it('should apply name_asc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'name_asc');
      expect(mockQuery.order).toHaveBeenCalledWith('business_name', { ascending: true });
    });

    it('should apply name_desc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'name_desc');
      expect(mockQuery.order).toHaveBeenCalledWith('business_name', { ascending: false });
    });

    it('should apply created_asc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'created_asc');
      expect(mockQuery.order).toHaveBeenCalledWith('created_at', { ascending: true });
    });

    it('should apply created_desc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'created_desc');
      expect(mockQuery.order).toHaveBeenCalledWith('created_at', { ascending: false });
    });

    it('should apply likes_asc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'likes_asc');
      expect(mockQuery.order).toHaveBeenCalledWith('total_likes', { ascending: true });
    });

    it('should apply likes_desc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'likes_desc');
      expect(mockQuery.order).toHaveBeenCalledWith('total_likes', { ascending: false });
    });

    it('should apply subscriptions_asc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'subscriptions_asc');
      expect(mockQuery.order).toHaveBeenCalledWith('total_subscriptions', { ascending: true });
    });

    it('should apply subscriptions_desc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'subscriptions_desc');
      expect(mockQuery.order).toHaveBeenCalledWith('total_subscriptions', { ascending: false });
    });

    it('should apply rating_asc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'rating_asc');
      expect(mockQuery.order).toHaveBeenCalledWith('average_rating', { ascending: true });
    });

    it('should apply rating_desc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'rating_desc');
      expect(mockQuery.order).toHaveBeenCalledWith('average_rating', { ascending: false });
    });

    it('should default to created_desc sorting', () => {
      const mockQuery = { order: jest.fn() };
      applySorting(mockQuery, 'unknown_sort_by' as any);
      expect(mockQuery.order).toHaveBeenCalledWith('created_at', { ascending: false });
    });
  });

  describe('getSecureBusinessProfiles', () => {
    const mockBusinessData = [
      { id: '1', business_name: 'Test Biz', status: 'online', latitude: 10, longitude: 20 },
    ];

    it('should fetch business profiles with default parameters', async () => {
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        ilike: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        range: jest.fn().mockResolvedValue({
          data: mockBusinessData,
          count: 1,
          error: null,
        }),
      });

      const result = await getSecureBusinessProfiles();

      expect(result.data?.length).toBe(1);
      expect(result.count).toBe(1);
      expect(result.data?.[0].business_name).toBe('Test Biz');
      expect(supabase.from).toHaveBeenCalledWith('business_profiles');
      expect(supabase.from('business_profiles').eq).toHaveBeenCalledWith('status', 'online');
      expect(supabase.from('business_profiles').range).toHaveBeenCalledWith(0, 19);
    });

    it('should apply search term filter', async () => {
      await getSecureBusinessProfiles('search_term');
      expect(supabase.from('business_profiles').ilike).toHaveBeenCalledWith('business_name', '%search_term%');
    });

    it('should apply location filters', async () => {
      await getSecureBusinessProfiles(null, '123456', 'Locality', 'City');
      expect(supabase.from('business_profiles').eq).toHaveBeenCalledWith('pincode', '123456');
      expect(supabase.from('business_profiles').eq).toHaveBeenCalledWith('locality', 'Locality');
      expect(supabase.from('business_profiles').eq).toHaveBeenCalledWith('city', 'City');
    });

    it('should apply category filter', async () => {
      await getSecureBusinessProfiles(null, null, null, null, 1, 20, 'created_desc', 'Food');
      expect(supabase.from('business_profiles').eq).toHaveBeenCalledWith('business_category', 'Food');
    });

    it('should handle no data found', async () => {
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        ilike: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        range: jest.fn().mockResolvedValue({
          data: [],
          count: 0,
          error: null,
        }),
      });
      const result = await getSecureBusinessProfiles('nonexistent');
      expect(result.data).toEqual([]);
      expect(result.count).toBe(0);
    });

    it('should handle database errors', async () => {
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        ilike: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        range: jest.fn().mockResolvedValue({
          data: null,
          count: null,
          error: { message: 'DB Error' },
        }),
      });
      const result = await getSecureBusinessProfiles();
      expect(result.error).toBe('Database error counting profiles.');
    });
  });

  describe('getSecureBusinessProfileIdsForDiscover', () => {
    const mockBusinessIds = [{ id: 'biz1' }, { id: 'biz2' }];

    it('should fetch business IDs for given pincodes', async () => {
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnThis(),
        in: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockBusinessIds,
          error: null,
        }),
      });

      const result = await getSecureBusinessProfileIdsForDiscover(['123456']);

      expect(result.data).toEqual(['biz1', 'biz2']);
      expect(supabase.from).toHaveBeenCalledWith('business_profiles');
      expect(supabase.from('business_profiles').in).toHaveBeenCalledWith('pincode', ['123456']);
      expect(supabase.from('business_profiles').eq).toHaveBeenCalledWith('status', 'online');
    });

    it('should return error if no pincodes are provided', async () => {
      const result = await getSecureBusinessProfileIdsForDiscover([]);
      expect(result.error).toBe('At least one pincode is required.');
    });

    it('should handle database errors', async () => {
      (supabase.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnThis(),
        in: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: null,
          error: { message: 'DB Error' },
        }),
      });
      const result = await getSecureBusinessProfileIdsForDiscover(['123456']);
      expect(result.error).toBe('Database error fetching profile IDs.');
    });
  });

  describe('createSubscriptionMap', () => {
    it('should create a map of subscriptions', () => {
      const subscriptionsData = [
        { business_profile_id: 'biz1', subscription_status: 'active', plan_id: 'planA' },
        { business_profile_id: 'biz2', subscription_status: 'inactive', plan_id: 'planB' },
      ];
      const map = createSubscriptionMap(subscriptionsData);
      expect(map.get('biz1')).toEqual({ subscription_status: 'active', plan_id: 'planA' });
      expect(map.get('biz2')).toEqual({ subscription_status: 'inactive', plan_id: 'planB' });
    });

    it('should handle null subscription data', () => {
      const map = createSubscriptionMap(null);
      expect(map.size).toBe(0);
    });
  });

  describe('getCurrentISOTimestamp', () => {
    it('should return a valid ISO timestamp', () => {
      const timestamp = getCurrentISOTimestamp();
      expect(timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
    });
  });
});