import { renderHook, act } from '@testing-library/react-hooks';
import { Dimensions } from 'react-native';
import { useIsMobile, useScreenDimensions, useIsTablet } from '@/src/hooks/use-mobile';

describe('useIsMobile', () => {
  const mockDimensions = (width: number) => {
    (Dimensions.get as jest.Mock).mockReturnValue({ width, height: 100 });
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (Dimensions.addEventListener as jest.Mock).mockClear();
  });

  it('returns true for mobile width', () => {
    mockDimensions(400);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(true);
  });

  it('returns false for tablet width', () => {
    mockDimensions(800);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(false);
  });

  it('updates when dimensions change', () => {
    let changeHandler: any;
    (Dimensions.addEventListener as jest.Mock).mockImplementation((event, handler) => {
      if (event === 'change') {
        changeHandler = handler;
      }
      return { remove: jest.fn() };
    });

    mockDimensions(400);
    const { result } = renderHook(() => useIsMobile());
    expect(result.current).toBe(true);

    act(() => {
      mockDimensions(800);
      changeHandler();
    });
    expect(result.current).toBe(false);
  });
});

describe('useScreenDimensions', () => {
  const mockDimensions = (width: number, height: number) => {
    (Dimensions.get as jest.Mock).mockReturnValue({ width, height });
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (Dimensions.addEventListener as jest.Mock).mockClear();
  });

  it('returns initial screen dimensions', () => {
    mockDimensions(300, 600);
    const { result } = renderHook(() => useScreenDimensions());
    expect(result.current).toEqual({ width: 300, height: 600 });
  });

  it('updates when dimensions change', () => {
    let changeHandler: any;
    (Dimensions.addEventListener as jest.Mock).mockImplementation((event, handler) => {
      if (event === 'change') {
        changeHandler = handler;
      }
      return { remove: jest.fn() };
    });

    mockDimensions(300, 600);
    const { result } = renderHook(() => useScreenDimensions());
    expect(result.current).toEqual({ width: 300, height: 600 });

    act(() => {
      mockDimensions(400, 800);
      changeHandler({ window: { width: 400, height: 800 }, screen: {} });
    });
    expect(result.current).toEqual({ width: 400, height: 800 });
  });
});

describe('useIsTablet', () => {
  const mockDimensions = (width: number) => {
    (Dimensions.get as jest.Mock).mockReturnValue({ width, height: 100 });
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (Dimensions.addEventListener as jest.Mock).mockClear();
  });

  it('returns true for tablet width', () => {
    mockDimensions(768);
    const { result } = renderHook(() => useIsTablet());
    expect(result.current).toBe(true);

    mockDimensions(1000);
    const { rerender } = renderHook(() => useIsTablet());
    expect(rerender().current).toBe(true);
  });

  it('returns false for mobile width', () => {
    mockDimensions(700);
    const { result } = renderHook(() => useIsTablet());
    expect(result.current).toBe(false);
  });

  it('returns false for desktop width', () => {
    mockDimensions(1200);
    const { result } = renderHook(() => useIsTablet());
    expect(result.current).toBe(false);
  });

  it('updates when dimensions change', () => {
    let changeHandler: any;
    (Dimensions.addEventListener as jest.Mock).mockImplementation((event, handler) => {
      if (event === 'change') {
        changeHandler = handler;
      }
      return { remove: jest.fn() };
    });

    mockDimensions(700);
    const { result } = renderHook(() => useIsTablet());
    expect(result.current).toBe(false);

    act(() => {
      mockDimensions(800);
      changeHandler();
    });
    expect(result.current).toBe(true);
  });
});