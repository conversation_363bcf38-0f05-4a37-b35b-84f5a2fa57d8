import { 
  getScalableUserPath,
  getProfileImagePath,
  getProductImagePath,
  getProductBaseImagePath,
  getProductVariantImagePath,
  getGalleryImagePath,
  getPostImagePath,
  getPostFolderPath,
  getCustomerAvatarPath,
  getCustomerPostImagePath,
  getCustomAdImagePath,
  getCustomHeaderImagePath,
  getThemeSpecificHeaderImagePath,
  PathValidator,
  StorageAnalytics,
} from '@/src/utils/storage-paths';

describe('storage-paths utilities', () => {
  const mockUserId = 'a1b2c3d4-e5f6-7890-abcd-ef1234567890';
  const mockTimestamp = 1678886400000; // March 15, 2023 00:00:00 GMT

  describe('getScalableUserPath', () => {
    it('should generate a scalable user path', () => {
      expect(getScalableUserPath(mockUserId)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890');
    });

    it('should throw error for invalid userId (too short)', () => {
      expect(() => getScalableUserPath('abc')).toThrow('Invalid userId: must be at least 4 characters long.');
    });

    it('should throw error for invalid userId (not string)', () => {
      expect(() => getScalableUserPath(null as any)).toThrow('Invalid userId: expected string, got object.');
    });
  });

  describe('getProfileImagePath', () => {
    it('should generate a profile image path', () => {
      expect(getProfileImagePath(mockUserId, mockTimestamp)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/profile/logo_1678886400000.webp');
    });
  });

  describe('getProductImagePath', () => {
    it('should generate a product image path (deprecated)', () => {
      expect(getProductImagePath(mockUserId, 'prod123', 0, mockTimestamp)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/products/prod123/image_0_1678886400000.webp');
    });
  });

  describe('getProductBaseImagePath', () => {
    it('should generate a base product image path', () => {
      expect(getProductBaseImagePath(mockUserId, 'prod123', 0, mockTimestamp)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/products/prod123/base/image_0_1678886400000.webp');
    });
  });

  describe('getProductVariantImagePath', () => {
    it('should generate a product variant image path', () => {
      expect(getProductVariantImagePath(mockUserId, 'prod123', 'variant456', 0, mockTimestamp)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/products/prod123/variant456/image_0_1678886400000.webp');
    });
  });

  describe('getGalleryImagePath', () => {
    it('should generate a gallery image path', () => {
      expect(getGalleryImagePath(mockUserId, mockTimestamp)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/gallery/gallery_1678886400000.webp');
    });
  });

  describe('getPostImagePath', () => {
    it('should generate a post image path with createdAt', () => {
      const postId = 'post789';
      const createdAt = '2023-03-15T10:00:00Z';
      expect(getPostImagePath(mockUserId, postId, 0, mockTimestamp, createdAt)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/posts/2023/03/post789/image_0_1678886400000.webp');
    });

    it('should generate a post image path without createdAt (uses current date)', () => {
      const postId = 'post789';
      const now = new Date(mockTimestamp);
      jest.spyOn(global, 'Date').mockImplementation(() => now as any);
      expect(getPostImagePath(mockUserId, postId, 0, mockTimestamp)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/posts/2023/03/post789/image_0_1678886400000.webp');
      jest.restoreAllMocks();
    });
  });

  describe('getPostFolderPath', () => {
    it('should generate a post folder path', () => {
      const postId = 'post789';
      const createdAt = '2023-03-15T10:00:00Z';
      expect(getPostFolderPath(mockUserId, postId, createdAt)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/posts/2023/03/post789');
    });
  });

  describe('getCustomerAvatarPath', () => {
    it('should generate a customer avatar path', () => {
      expect(getCustomerAvatarPath(mockUserId, mockTimestamp)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/avatar/avatar_1678886400000.webp');
    });
  });

  describe('getCustomerPostImagePath', () => {
    it('should generate a customer post image path with createdAt', () => {
      const postId = 'custpost123';
      const createdAt = '2023-03-15T10:00:00Z';
      expect(getCustomerPostImagePath(mockUserId, postId, 0, mockTimestamp, createdAt)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/posts/2023/03/custpost123/image_0_1678886400000.webp');
    });
  });

  describe('getCustomAdImagePath', () => {
    it('should generate a custom ad image path', () => {
      expect(getCustomAdImagePath(mockUserId, mockTimestamp)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/ads/custom_ad_1678886400000.webp');
    });
  });

  describe('getCustomHeaderImagePath', () => {
    it('should generate a custom header image path', () => {
      expect(getCustomHeaderImagePath(mockUserId, mockTimestamp)).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/branding/header_1678886400000.webp');
    });
  });

  describe('getThemeSpecificHeaderImagePath', () => {
    it('should generate a theme-specific header image path', () => {
      expect(getThemeSpecificHeaderImagePath(mockUserId, mockTimestamp, 'light')).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/branding/header_light_1678886400000.webp');
      expect(getThemeSpecificHeaderImagePath(mockUserId, mockTimestamp, 'dark')).toBe('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/branding/header_dark_1678886400000.webp');
    });
  });

  describe('PathValidator', () => {
    describe('isScalablePath', () => {
      it('should return true for a scalable path', () => {
        expect(PathValidator.isScalablePath('users/a1/b2/some-user-id/profile.webp')).toBe(true);
      });

      it('should return false for a non-scalable path', () => {
        expect(PathValidator.isScalablePath('old-path/profile.webp')).toBe(false);
        expect(PathValidator.isScalablePath('users/a1/some-user-id/profile.webp')).toBe(false);
      });
    });

    describe('extractUserIdFromPath', () => {
      it('should extract user ID from a scalable path', () => {
        expect(PathValidator.extractUserIdFromPath('users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/profile.webp')).toBe(mockUserId);
      });

      it('should return null for a non-scalable path', () => {
        expect(PathValidator.extractUserIdFromPath('old-path/profile.webp')).toBeNull();
      });
    });

    describe('validatePathStructure', () => {
      it('should return true if path structure is valid for the user', () => {
        expect(PathValidator.validatePathStructure(mockUserId, 'users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890/profile.webp')).toBe(true);
      });

      it('should return false if path structure is invalid for the user', () => {
        expect(PathValidator.validatePathStructure(mockUserId, 'users/x1/y2/some-other-user/profile.webp')).toBe(false);
      });
    });
  });

  describe('StorageAnalytics', () => {
    describe('getDistributionInfo', () => {
      it('should return correct distribution info', () => {
        const info = StorageAnalytics.getDistributionInfo(mockUserId);
        expect(info.prefix).toBe('a1');
        expect(info.midfix).toBe('b2');
        expect(info.bucket).toBe('a1/b2');
        expect(info.estimatedPeers).toBeGreaterThan(0);
      });
    });
  });
});