import React from 'react';
import { render, screen, waitFor } from '@testing-library/react-native';
import { DiscoverProvider, useDiscoverContext } from '@/src/contexts/DiscoveryContext';
import { useAuth } from '@/src/contexts/AuthContext';
import { supabase } from '@/src/config/supabase';
import { loadLocationData, saveLocationData } from '@/src/services/locationStorageService';
import { discoveryService } from '@/src/services/discovery/DiscoveryService';

// Mock all external dependencies
jest.mock('@/src/contexts/AuthContext');
jest.mock('@/src/config/supabase');
jest.mock('@/src/services/locationStorageService');
jest.mock('@/src/services/discovery/DiscoveryService', () => ({
  discoveryService: {
    search: jest.fn(),
  },
}));

describe('DiscoverContext', () => {
  const mockUser = { id: 'user123' };
  const mockLocationData = {
    latitude: 10,
    longitude: 20,
    city: 'TestCity',
    pincode: '123456',
    locality: 'TestLocality',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue({ user: mockUser });
    (loadLocationData as jest.Mock).mockResolvedValue({ success: false }); // Default to no saved location
    (saveLocationData as jest.Mock).mockResolvedValue({ success: true });
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'customer_profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({ data: mockLocationData }),
            }),
          }),
        };
      } else if (tableName === 'business_profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({ data: null }), // No business profile by default
            }),
          }),
        };
      }
      return jest.fn();
    });
    (discoveryService.search as jest.Mock).mockResolvedValue({
      data: {
        location: mockLocationData,
        businesses: [],
        products: [],
        isAuthenticated: true,
        totalCount: 0,
        hasMore: false,
        nextPage: null,
      },
    });
  });

  const TestComponent = () => {
    const context = useDiscoverContext();
    return (
      <>
        <Text testID="view-type">{context.viewType}</Text>
        <Text testID="is-searching">{context.isSearching.toString()}</Text>
        <Text testID="user-location">{context.userLocation?.city || 'N/A'}</Text>
        <Text testID="is-authenticated">{context.isAuthenticated.toString()}</Text>
        <Text testID="search-term">{context.searchTerm}</Text>
        <Text testID="selected-category">{context.selectedCategory || 'N/A'}</Text>
        <Text testID="total-count">{context.totalCount.toString()}</Text>
        <Text testID="has-more">{context.hasMore.toString()}</Text>
        <Text testID="current-page">{context.currentPage.toString()}</Text>
        <Text testID="is-location-loading">{context.isLocationLoading.toString()}</Text>
        <Text testID="search-error">{context.searchError || 'N/A'}</Text>
        <Text testID="product-filter-by">{context.productFilterBy}</Text>
        <Text testID="product-sort-by">{context.productSortBy}</Text>
        <Text testID="business-sort-by">{context.sortBy}</Text>

        <button onPress={() => context.handleViewChange('products')}>Change View</button>
        <button onPress={() => context.handleSearch('new query')}>Search</button>
        <button onPress={() => context.handleBusinessSortChange('distance_asc')}>Sort Business</button>
        <button onPress={() => context.handleProductSortChange('price_asc')}>Sort Product</button>
        <button onPress={() => context.handleProductFilterChange('new')}>Filter Product</button>
        <button onPress={() => context.handleCategoryChange('Electronics')}>Change Category</button>
        <button onPress={() => context.loadMore()}>Load More</button>
        <button onPress={() => context.refresh()}>Refresh</button>
        <button onPress={() => context.updateLocation({ latitude: 30, longitude: 40, city: 'NewCity' })}>Update Location</button>
        <button onPress={() => context.handleClearSearch()}>Clear Search</button>
      </>
    );
  };

  it('provides initial context values', async () => {
    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('view-type').props.children).toBe('products');
      expect(screen.getByTestId('is-searching').props.children).toBe('false');
      expect(screen.getByTestId('user-location').props.children).toBe('TestCity');
      expect(screen.getByTestId('is-authenticated').props.children).toBe('true');
      expect(screen.getByTestId('search-term').props.children).toBe('');
      expect(screen.getByTestId('selected-category').props.children).toBe('N/A');
      expect(screen.getByTestId('total-count').props.children).toBe('0');
      expect(screen.getByTestId('has-more').props.children).toBe('false');
      expect(screen.getByTestId('current-page').props.children).toBe('1');
      expect(screen.getByTestId('is-location-loading').props.children).toBe('false');
      expect(screen.getByTestId('search-error').props.children).toBe('N/A');
      expect(screen.getByTestId('product-filter-by').props.children).toBe('all');
      expect(screen.getByTestId('product-sort-by').props.children).toBe('newest');
      expect(screen.getByTestId('business-sort-by').props.children).toBe('created_desc');
    });
  });

  it('updates viewType and triggers search on handleViewChange', async () => {
    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    fireEvent.press(screen.getByText('Change View'));

    await waitFor(() => {
      expect(screen.getByTestId('view-type').props.children).toBe('products');
      expect(discoveryService.search).toHaveBeenCalledTimes(2); // Initial search + view change search
    });
  });

  it('updates searchTerm and triggers search on handleSearch', async () => {
    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    fireEvent.press(screen.getByText('Search'));

    await waitFor(() => {
      expect(screen.getByTestId('search-term').props.children).toBe('new query');
      expect(discoveryService.search).toHaveBeenCalledTimes(2);
    });
  });

  it('updates sortBy and triggers search on handleBusinessSortChange', async () => {
    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    fireEvent.press(screen.getByText('Sort Business'));

    await waitFor(() => {
      expect(screen.getByTestId('business-sort-by').props.children).toBe('distance_asc');
      expect(discoveryService.search).toHaveBeenCalledTimes(2);
    });
  });

  it('updates productSortBy and triggers search on handleProductSortChange', async () => {
    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    fireEvent.press(screen.getByText('Sort Product'));

    await waitFor(() => {
      expect(screen.getByTestId('product-sort-by').props.children).toBe('price_asc');
      expect(discoveryService.search).toHaveBeenCalledTimes(2);
    });
  });

  it('updates productFilterBy and triggers search on handleProductFilterChange', async () => {
    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    fireEvent.press(screen.getByText('Filter Product'));

    await waitFor(() => {
      expect(screen.getByTestId('product-filter-by').props.children).toBe('new');
      expect(discoveryService.search).toHaveBeenCalledTimes(2);
    });
  });

  it('updates selectedCategory and triggers search on handleCategoryChange', async () => {
    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    fireEvent.press(screen.getByText('Change Category'));

    await waitFor(() => {
      expect(screen.getByTestId('selected-category').props.children).toBe('Electronics');
      expect(discoveryService.search).toHaveBeenCalledTimes(2);
    });
  });

  it('loads more results on loadMore', async () => {
    (discoveryService.search as jest.Mock).mockResolvedValueOnce({
      data: {
        location: mockLocationData,
        businesses: [],
        products: [{ id: 'p1' }],
        isAuthenticated: true,
        totalCount: 1,
        hasMore: true,
        nextPage: 2,
      },
    }).mockResolvedValueOnce({
      data: {
        location: mockLocationData,
        businesses: [],
        products: [{ id: 'p2' }],
        isAuthenticated: true,
        totalCount: 2,
        hasMore: false,
        nextPage: null,
      },
    });

    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    await waitFor(() => expect(screen.getByTestId('total-count').props.children).toBe('1'));

    fireEvent.press(screen.getByText('Load More'));

    await waitFor(() => {
      expect(screen.getByTestId('current-page').props.children).toBe('2');
      expect(screen.getByTestId('total-count').props.children).toBe('2');
      expect(screen.getByTestId('has-more').props.children).toBe('false');
    });
  });

  it('refreshes search results on refresh', async () => {
    (discoveryService.search as jest.Mock).mockResolvedValueOnce({
      data: {
        location: mockLocationData,
        businesses: [],
        products: [{ id: 'p1' }],
        isAuthenticated: true,
        totalCount: 1,
        hasMore: false,
        nextPage: null,
      },
    }).mockResolvedValueOnce({
      data: {
        location: mockLocationData,
        businesses: [],
        products: [{ id: 'p3' }],
        isAuthenticated: true,
        totalCount: 1,
        hasMore: false,
        nextPage: null,
      },
    });

    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    await waitFor(() => expect(screen.getByTestId('total-count').props.children).toBe('1'));

    fireEvent.press(screen.getByText('Refresh'));

    await waitFor(() => {
      expect(screen.getByTestId('current-page').props.children).toBe('1');
      expect(screen.getByTestId('total-count').props.children).toBe('1');
    });
  });

  it('updates location and triggers search on updateLocation', async () => {
    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    fireEvent.press(screen.getByText('Update Location'));

    await waitFor(() => {
      expect(screen.getByTestId('user-location').props.children).toBe('NewCity');
      expect(saveLocationData).toHaveBeenCalledWith({ latitude: 30, longitude: 40, city: 'NewCity' });
      expect(discoveryService.search).toHaveBeenCalledTimes(2);
    });
  });

  it('clears search term and triggers search on handleClearSearch', async () => {
    render(
      <DiscoverProvider>
        <TestComponent />
      </DiscoverProvider>
    );

    fireEvent.press(screen.getByText('Search')); // Set a search term first
    await waitFor(() => expect(screen.getByTestId('search-term').props.children).toBe('new query'));

    fireEvent.press(screen.getByText('Clear Search'));

    await waitFor(() => {
      expect(screen.getByTestId('search-term').props.children).toBe('');
      expect(discoveryService.search).toHaveBeenCalledTimes(3); // Initial + search + clear search
    });
  });

  it('throws error if useDiscoverContext is used outside provider', () => {
    expect(() => render(<useDiscoverContext />)).toThrow(
      'useDiscoverContext must be used within a DiscoverProvider'
    );
  });
});