import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { DukancardBottomTabs } from '../../../../src/components/shared/navigation/BottomNavigation';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Home, Search, Store, User } from 'lucide-react-native';

// Mock useSafeAreaInsets
jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: jest.fn(() => ({
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  })),
}));

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  Home: 'HomeIcon',
  QrCode: 'QrCodeIcon',
  Search: 'SearchIcon',
  Store: 'StoreIcon',
  User: 'UserIcon',
  Users: 'UsersIcon',
}));

describe('DukancardBottomTabs', () => {
  const mockOnPressHome = jest.fn();
  const mockOnPressSearch = jest.fn();
  const mockOnPressQR = jest.fn();
  const mockOnPressStore = jest.fn();
  const mockOnPressUser = jest.fn();

  const mockTabs = [
    {
      key: 'home',
      icon: Home,
      label: 'Home',
      onPress: mockOnPressHome,
      isActive: true,
    },
    {
      key: 'search',
      icon: Search,
      label: 'Search',
      onPress: mockOnPressSearch,
      isActive: false,
    },
    {
      key: 'store',
      icon: Store,
      label: 'Store',
      onPress: mockOnPressStore,
      isActive: false,
    },
    {
      key: 'profile',
      icon: User,
      label: 'Profile',
      onPress: mockOnPressUser,
      isActive: false,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all tabs and QR scan button', () => {
    const { getByText, getByTestId } = render(
      <DukancardBottomTabs tabs={mockTabs} onQRScanPress={mockOnPressQR} />
    );

    expect(getByText('Home')).toBeTruthy();
    expect(getByText('Search')).toBeTruthy();
    expect(getByText('Store')).toBeTruthy();
    expect(getByText('Profile')).toBeTruthy();
    expect(getByText('Scan')).toBeTruthy();
    expect(getByTestId('qr-button')).toBeTruthy();
  });

  it('calls correct onPress handler when a tab is pressed', () => {
    const { getByText } = render(
      <DukancardBottomTabs tabs={mockTabs} onQRScanPress={mockOnPressQR} />
    );

    fireEvent.press(getByText('Search'));
    expect(mockOnPressSearch).toHaveBeenCalledTimes(1);
    expect(mockOnPressHome).not.toHaveBeenCalled();
    expect(mockOnPressStore).not.toHaveBeenCalled();
    expect(mockOnPressUser).not.toHaveBeenCalled();
  });

  it('calls onQRScanPress when QR scan button is pressed', () => {
    const { getByText } = render(
      <DukancardBottomTabs tabs={mockTabs} onQRScanPress={mockOnPressQR} />
    );

    fireEvent.press(getByText('Scan'));
    expect(mockOnPressQR).toHaveBeenCalledTimes(1);
  });

  it('applies active styles to the active tab', () => {
    const { getByText } = render(
      <DukancardBottomTabs tabs={mockTabs} onQRScanPress={mockOnPressQR} />
    );

    // Home tab is active in mockTabs
    const homeTab = getByText('Home');
    expect(homeTab.props.style.color).toBe('#D4AF37'); // Gold color for active

    const searchTab = getByText('Search');
    expect(searchTab.props.style.color).not.toBe('#D4AF37'); // Inactive color
  });

  it('applies disabled styles to disabled tabs', () => {
    const disabledTabs = [
      {
        key: 'disabled',
        icon: Home,
        label: 'Disabled',
        onPress: jest.fn(),
        isActive: false,
        disabled: true,
      },
    ];
    const { getByText } = render(
      <DukancardBottomTabs tabs={disabledTabs} onQRScanPress={mockOnPressQR} />
    );

    const disabledTab = getByText('Disabled');
    fireEvent.press(disabledTab);
    expect(disabledTabs[0].onPress).not.toHaveBeenCalled();
    expect(disabledTab.parent.props.style.opacity).toBe(0.5); // Check for disabled opacity
  });

  it('adjusts padding based on safe area insets', () => {
    (useSafeAreaInsets as jest.Mock).mockReturnValue({
      top: 10,
      bottom: 20,
      left: 0,
      right: 0,
    });

    const { getByTestId } = render(
      <DukancardBottomTabs tabs={mockTabs} onQRScanPress={mockOnPressQR} />
    );

    const container = getByTestId('bottom-navigation-container');
    expect(container.props.style.paddingBottom).toBe(20); // Should use max(insets.bottom, 8)
  });
});
