
const ReactNative = jest.requireActual('react-native');
const React = require('react');

module.exports = {
  ...ReactNative,
  Text: ({ children, ...props }) => React.createElement(ReactNative.Text, props, children),
  View: ({ children, ...props }) => React.createElement(ReactNative.View, props, children),
  ActivityIndicator: ({ ...props }) => React.createElement(ReactNative.View, props),
  KeyboardAvoidingView: ({ children }) => React.createElement(React.Fragment, null, children),
  Platform: { OS: 'ios' },
  SafeAreaView: ({ children }) => React.createElement(React.Fragment, null, children),
  Alert: {
    alert: jest.fn(),
  },
  StyleSheet: {
    ...ReactNative.StyleSheet,
    create: (styles) => styles,
  },
};
