import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { SortSelector } from '@/src/components/social/SortSelector';
import { Ionicons } from '@expo/vector-icons';

// Mock Ionicons
jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons', // Mock Ionicons as a simple string
}));

describe('SortSelector', () => {
  const mockOptions = [
    { label: 'Option A', value: 'a' },
    { label: 'Option B', value: 'b' },
    { label: 'Option C', value: 'c' },
  ];
  const onValueChangeMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with selected value', () => {
    render(
      <SortSelector
        value="b"
        onValueChange={onValueChangeMock}
        options={mockOptions}
      />
    );
    expect(screen.getByText('Sort by: Option B')).toBeOnTheScreen();
  });

  it('opens modal on selector press', () => {
    render(
      <SortSelector
        value="a"
        onValueChange={onValueChangeMock}
        options={mockOptions}
      />
    );
    fireEvent.press(screen.getByText('Sort by: Option A'));
    expect(screen.getByText('Sort by')).toBeOnTheScreen(); // Modal header
    expect(screen.getByText('Option A')).toBeOnTheScreen();
    expect(screen.getByText('Option B')).toBeOnTheScreen();
  });

  it('closes modal on overlay press', () => {
    render(
      <SortSelector
        value="a"
        onValueChange={onValueChangeMock}
        options={mockOptions}
      />
    );
    fireEvent.press(screen.getByText('Sort by: Option A')); // Open modal
    fireEvent.press(screen.getByLabelText('overlay')); // Press overlay
    expect(screen.queryByText('Sort by')).toBeNull(); // Modal should be closed
  });

  it('closes modal on close button press', () => {
    render(
      <SortSelector
        value="a"
        onValueChange={onValueChangeMock}
        options={mockOptions}
      />
    );
    fireEvent.press(screen.getByText('Sort by: Option A')); // Open modal
    fireEvent.press(screen.getByLabelText('close-button')); // Press close button
    expect(screen.queryByText('Sort by')).toBeNull(); // Modal should be closed
  });

  it('calls onValueChange and closes modal on option select', () => {
    render(
      <SortSelector
        value="a"
        onValueChange={onValueChangeMock}
        options={mockOptions}
      />
    );
    fireEvent.press(screen.getByText('Sort by: Option A')); // Open modal
    fireEvent.press(screen.getByText('Option B')); // Select Option B

    expect(onValueChangeMock).toHaveBeenCalledWith('b');
    expect(screen.queryByText('Sort by')).toBeNull(); // Modal should be closed
  });

  it('displays checkmark for selected option', () => {
    render(
      <SortSelector
        value="b"
        onValueChange={onValueChangeMock}
        options={mockOptions}
      />
    );
    fireEvent.press(screen.getByText('Sort by: Option B')); // Open modal
    expect(screen.getByLabelText('checkmark-icon')).toBeOnTheScreen(); // Checkmark for Option B
    expect(screen.queryByLabelText('checkmark-icon', { name: 'Option A' })).toBeNull(); // No checkmark for Option A
  });
});