import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { toast } from 'sonner';
import SocialMediaBusinessPostCreator from '@/components/feed/shared/SocialMediaBusinessPostCreator';
import { createPost, updatePost, deletePost } from '@/lib/actions/posts';
import { uploadBusinessPostImage } from '@/lib/actions/shared/upload-business-post-media';
import { compressImageUltraAggressiveClient } from '@/lib/utils/client-image-compression';

// Mock dependencies
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

jest.mock('@/lib/actions/posts', () => ({
  createPost: jest.fn(),
  updatePost: jest.fn(),
  deletePost: jest.fn()
}));

jest.mock('@/lib/actions/shared/upload-business-post-media', () => ({
  uploadBusinessPostImage: jest.fn()
}));

jest.mock('@/lib/utils/client-image-compression', () => ({
  compressImageUltraAggressiveClient: jest.fn()
}));

jest.mock('@/utils/supabase/client', () => ({
  createClient: () => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null
      })
    },
    from: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { name: 'Test Business', avatar_url: null },
            error: null
          })
        })
      })
    })
  })
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    refresh: jest.fn()
  })
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: 'div',
    button: 'button',
    textarea: 'textarea'
  },
  AnimatePresence: ({ children }: any) => children
}));

jest.mock('lucide-react', () => ({
  Image: () => <div>Image Icon</div>,
  MapPin: () => <div>MapPin Icon</div>,
  X: () => <div>X Icon</div>,
  Send: () => <div>Send Icon</div>,
  Loader2: () => <div>Loader Icon</div>,
  Package: () => <div>Package Icon</div>
}));

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => <button {...props}>{children}</button>
}));

jest.mock('@/components/ui/avatar', () => ({
  Avatar: ({ children }: any) => <div>{children}</div>,
  AvatarFallback: ({ children }: any) => <div>{children}</div>,
  AvatarImage: ({ children }: any) => <div>{children}</div>
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ alt, ...props }: any) => <img alt={alt} {...props} />
}));

jest.mock('@/components/feed/shared/forms/LocationDisplay', () => {
  return function LocationDisplay() {
    return <div>Location Display</div>;
  };
});

const mockCreatePost = createPost as jest.MockedFunction<typeof createPost>;
const mockUpdatePost = updatePost as jest.MockedFunction<typeof updatePost>;
const mockDeletePost = deletePost as jest.MockedFunction<typeof deletePost>;
const mockUploadBusinessPostImage = uploadBusinessPostImage as jest.MockedFunction<typeof uploadBusinessPostImage>;
const mockCompressImage = compressImageUltraAggressiveClient as jest.MockedFunction<typeof compressImageUltraAggressiveClient>;
const mockToast = toast as jest.Mocked<typeof toast>;

describe('SocialMediaBusinessPostCreator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockToast.success = jest.fn();
    mockToast.error = jest.fn();
  });

  it('should create a business post with products successfully', async () => {
    mockCreatePost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    render(<SocialMediaBusinessPostCreator />);

    // Click to expand the post creator
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Type content
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: 'Check out our new products!' } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreatePost).toHaveBeenCalledWith({
        content: 'Check out our new products!',
        image_url: null,
        product_ids: [],
        mentioned_business_ids: []
      });
      expect(mockToast.success).toHaveBeenCalledWith('Post created successfully!');
    });
  });

  it('should create a business post with image successfully', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockCompressedBlob = new Blob(['compressed'], { type: 'image/webp' });

    mockCreatePost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockCompressImage.mockResolvedValue({
      blob: mockCompressedBlob,
      finalSizeKB: 50,
      compressionRatio: 0.5,
      dimensions: { width: 800, height: 600 }
    });

    mockUploadBusinessPostImage.mockResolvedValue({
      success: true,
      url: 'https://example.com/business-image.webp'
    });

    mockUpdatePost.mockResolvedValue({
      success: true,
      message: 'Post updated'
    });

    render(<SocialMediaBusinessPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add content
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: 'Business post with image' } });

    // Add image
    const fileInput = screen.getByLabelText(/Add image/);
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCompressImage).toHaveBeenCalledWith(mockFile, {
        maxDimension: 1200,
        targetSizeKB: 100
      });
      expect(mockCreatePost).toHaveBeenCalledWith({
        content: 'Business post with image',
        image_url: null,
        product_ids: [],
        mentioned_business_ids: []
      });
      expect(mockUploadBusinessPostImage).toHaveBeenCalled();
      expect(mockUpdatePost).toHaveBeenCalledWith('post-123', {
        content: 'Business post with image',
        image_url: 'https://example.com/business-image.webp',
        product_ids: [],
        mentioned_business_ids: []
      });
      expect(mockToast.success).toHaveBeenCalledWith('Post created successfully!');
    });
  });

  it('should rollback business post creation when image upload fails', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockCompressedBlob = new Blob(['compressed'], { type: 'image/webp' });

    mockCreatePost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockCompressImage.mockResolvedValue({
      blob: mockCompressedBlob,
      finalSizeKB: 50,
      compressionRatio: 0.5,
      dimensions: { width: 800, height: 600 }
    });

    mockUploadBusinessPostImage.mockResolvedValue({
      success: false,
      error: 'Business image upload failed'
    });

    mockDeletePost.mockResolvedValue({
      success: true,
      message: 'Post deleted'
    });

    render(<SocialMediaBusinessPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add content and image
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: 'Business post with image' } });

    const fileInput = screen.getByLabelText(/Add image/);
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreatePost).toHaveBeenCalled();
      expect(mockUploadBusinessPostImage).toHaveBeenCalled();
      expect(mockDeletePost).toHaveBeenCalledWith('post-123');
      expect(mockToast.error).toHaveBeenCalledWith('Failed to upload image. Please try again.');
    });
  });

  it('should handle business profile validation', async () => {
    // Mock business profile check failure
    mockCreatePost.mockResolvedValue({
      success: false,
      message: 'Business profile required',
      error: 'Please complete your business profile first'
    });

    render(<SocialMediaBusinessPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add content
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: 'Business post' } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreatePost).toHaveBeenCalled();
      expect(mockToast.error).toHaveBeenCalledWith('Please complete your business profile first');
    });
  });

  it('should validate content length limits', async () => {
    const longContent = 'a'.repeat(3001); // Assuming 3000 char limit

    render(<SocialMediaBusinessPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add very long content
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: longContent } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Post content is too long');
      expect(mockCreatePost).not.toHaveBeenCalled();
    });
  });

  it('should handle compression errors with rollback', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

    mockCreatePost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockCompressImage.mockRejectedValue(new Error('Business compression failed'));

    mockDeletePost.mockResolvedValue({
      success: true,
      message: 'Post deleted'
    });

    render(<SocialMediaBusinessPostCreator />);

    // Click to expand
    const expandButton = screen.getByText(/What's on your mind/);
    fireEvent.click(expandButton);

    // Add content and image
    const textArea = screen.getByPlaceholderText(/Share your thoughts/);
    fireEvent.change(textArea, { target: { value: 'Business post with image' } });

    const fileInput = screen.getByLabelText(/Add image/);
    fireEvent.change(fileInput, { target: { files: [mockFile] } });

    // Submit post
    const submitButton = screen.getByText('Post');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreatePost).toHaveBeenCalled();
      expect(mockDeletePost).toHaveBeenCalledWith('post-123');
      expect(mockToast.error).toHaveBeenCalledWith('Failed to upload image. Please try again.');
    });
  });
});
