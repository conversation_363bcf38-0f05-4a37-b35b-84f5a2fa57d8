import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import { AvatarUpload } from '@/src/components/ui/AvatarUpload';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Camera, X } from 'lucide-react-native';
import ImagePickerBottomSheet from '@/src/components/pickers/ImagePickerBottomSheet';
import { openCameraForAvatar, openGalleryForAvatar } from '@/backend/supabase/services/storage/avatarUploadService';
import * as ImageManipulator from 'expo-image-manipulator';
import { ActivityIndicator } from 'react-native';

// Mock necessary modules and functions
jest.mock('@/src/hooks/useColorScheme');
jest.mock('lucide-react-native', () => ({
  Camera: jest.fn(() => null),
  X: jest.fn(() => null),
}));
jest.mock('@/src/components/pickers/ImagePickerBottomSheet', () => ({
  __esModule: true,
  default: jest.fn(({ onCameraPress, onGalleryPress, title, cameraLabel, galleryLabel }, ref) => {
    React.useImperativeHandle(ref, () => ({
      present: jest.fn(() => {
        // Simulate presenting the bottom sheet
        // In a real test, you might render a mock bottom sheet component here
      }),
      dismiss: jest.fn(),
    }));
    return (
      <></>
    );
  }),
}));
jest.mock('@/backend/supabase/services/storage/avatarUploadService');
jest.mock('expo-image-manipulator');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  ActivityIndicator: jest.fn(() => null), // Mock ActivityIndicator to return null
}));

describe('AvatarUpload', () => {
  const onImageUploadMock = jest.fn();
  const onImageDeleteMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (Camera as jest.Mock).mockImplementation(() => <Text testID="camera-icon">Camera</Text>);
    (X as jest.Mock).mockImplementation(() => <Text testID="x-icon">X</Text>);
    (ActivityIndicator as jest.Mock).mockImplementation(() => <Text testID="activity-indicator">Loading</Text>);

    (openCameraForAvatar as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/camera_image.jpg' }],
    });
    (openGalleryForAvatar as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/gallery_image.jpg' }],
    });
    (ImageManipulator.manipulateAsync as jest.Mock).mockResolvedValue({
      uri: 'file://mock/manipulated_image.jpg',
    });
  });

  it('renders correctly with default props', () => {
    render(<AvatarUpload onImageUpload={onImageUploadMock} />);
    expect(screen.getByText('Profile Picture')).toBeOnTheScreen();
    expect(screen.getByText('Tap to change picture')).toBeOnTheScreen();
    expect(screen.getByTestId('camera-icon')).toBeOnTheScreen();
  });

  it('displays initial image when provided', () => {
    render(<AvatarUpload onImageUpload={onImageUploadMock} initialImageUri="https://example.com/initial.jpg" />);
    expect(screen.getByTestId('avatar-image')).toBeOnTheScreen();
    expect(screen.queryByTestId('camera-icon')).toBeNull();
  });

  it('calls onImageUpload with manipulated URI after camera selection', async () => {
    render(<AvatarUpload onImageUpload={onImageUploadMock} />);
    fireEvent.press(screen.getByTestId('avatar-upload-button')); // Assuming a testID for the TouchableOpacity

    // Simulate camera selection via the mocked bottom sheet
    const imagePickerBottomSheetInstance = (ImagePickerBottomSheet as jest.Mock).mock.results[0].value;
    imagePickerBottomSheetInstance.onCameraPress();

    await waitFor(() => {
      expect(openCameraForAvatar).toHaveBeenCalled();
      expect(ImageManipulator.manipulateAsync).toHaveBeenCalledWith(
        'file://mock/camera_image.jpg',
        [{ resize: { width: 400, height: 400 } }],
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
      );
      expect(onImageUploadMock).toHaveBeenCalledWith('file://mock/manipulated_image.jpg');
    });
  });

  it('calls onImageUpload with manipulated URI after gallery selection', async () => {
    render(<AvatarUpload onImageUpload={onImageUploadMock} />);
    fireEvent.press(screen.getByTestId('avatar-upload-button'));

    // Simulate gallery selection via the mocked bottom sheet
    const imagePickerBottomSheetInstance = (ImagePickerBottomSheet as jest.Mock).mock.results[0].value;
    imagePickerBottomSheetInstance.onGalleryPress();

    await waitFor(() => {
      expect(openGalleryForAvatar).toHaveBeenCalled();
      expect(ImageManipulator.manipulateAsync).toHaveBeenCalledWith(
        'file://mock/gallery_image.jpg',
        [{ resize: { width: 400, height: 400 } }],
        { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
      );
      expect(onImageUploadMock).toHaveBeenCalledWith('file://mock/manipulated_image.jpg');
    });
  });

  it('calls onImageDelete when delete button is pressed', () => {
    render(
      <AvatarUpload
        onImageUpload={onImageUploadMock}
        onImageDelete={onImageDeleteMock}
        initialImageUri="https://example.com/initial.jpg"
      />
    );
    fireEvent.press(screen.getByTestId('delete-button')); // Assuming a testID for the delete button
    expect(onImageDeleteMock).toHaveBeenCalledTimes(1);
  });

  it('shows activity indicator when selecting image', async () => {
    (openCameraForAvatar as jest.Mock).mockImplementationOnce(() => new Promise(resolve => setTimeout(() => resolve({
      canceled: false,
      assets: [{ uri: 'file://mock/camera_image.jpg' }],
    }), 100)));

    render(<AvatarUpload onImageUpload={onImageUploadMock} />);
    fireEvent.press(screen.getByTestId('avatar-upload-button'));

    const imagePickerBottomSheetInstance = (ImagePickerBottomSheet as jest.Mock).mock.results[0].value;
    imagePickerBottomSheetInstance.onCameraPress();

    expect(screen.getByTestId('activity-indicator')).toBeOnTheScreen();
    await waitFor(() => expect(screen.queryByTestId('activity-indicator')).toBeNull());
  });

  it('disables interactions when disabled prop is true', () => {
    render(<AvatarUpload onImageUpload={onImageUploadMock} disabled={true} />);
    fireEvent.press(screen.getByTestId('avatar-upload-button'));
    expect(ImagePickerBottomSheet).not.toHaveBeenCalled();
  });
});