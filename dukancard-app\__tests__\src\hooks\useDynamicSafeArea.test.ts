import { renderHook, act } from '@testing-library/react-hooks';
import { AppState } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDynamicSafeArea } from '@/src/hooks/useDynamicSafeArea';

// Mock necessary modules
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  AppState: {
    addEventListener: jest.fn(),
    currentState: 'active',
  },
}));
jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: jest.fn(),
}));

describe('useDynamicSafeArea', () => {
  const mockInsets = { top: 10, bottom: 20, left: 0, right: 0 };
  let appStateChangeHandler: any;

  beforeEach(() => {
    jest.clearAllMocks();
    (useSafeAreaInsets as jest.Mock).mockReturnValue(mockInsets);
    (AppState.addEventListener as jest.Mock).mockImplementation((event, handler) => {
      if (event === 'change') {
        appStateChangeHandler = handler;
      }
      return { remove: jest.fn() };
    });
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('should return safe area insets', () => {
    const { result } = renderHook(() => useDynamicSafeArea());
    expect(result.current).toEqual(mockInsets);
  });

  it('should add and remove AppState change listener', () => {
    const { unmount } = renderHook(() => useDynamicSafeArea());
    expect(AppState.addEventListener).toHaveBeenCalledWith('change', expect.any(Function));
    unmount();
    expect(AppState.addEventListener).toHaveBeenCalledWith('change', expect.any(Function)); // Called once for add, once for remove
  });

  it('should trigger setTimeout when app state changes from inactive/background to active', () => {
    const { result } = renderHook(() => useDynamicSafeArea());

    // Simulate app going to background
    act(() => {
      AppState.currentState = 'background';
      appStateChangeHandler('background');
    });

    // Simulate app becoming active
    act(() => {
      AppState.currentState = 'active';
      appStateChangeHandler('active');
    });

    expect(setTimeout).toHaveBeenCalledTimes(1);
    expect(setTimeout).toHaveBeenCalledWith(expect.any(Function), 100);
  });

  it('should not trigger setTimeout if app state does not change to active from inactive/background', () => {
    const { result } = renderHook(() => useDynamicSafeArea());

    // Simulate app state change within active states
    act(() => {
      AppState.currentState = 'active';
      appStateChangeHandler('active');
    });

    expect(setTimeout).not.toHaveBeenCalled();

    // Simulate app state change from active to background
    act(() => {
      AppState.currentState = 'background';
      appStateChangeHandler('background');
    });

    expect(setTimeout).not.toHaveBeenCalled();
  });
});