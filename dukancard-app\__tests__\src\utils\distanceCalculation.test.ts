import { 
  calculateDistance,
  validateCoordinates,
  formatDistance,
  calculateDistanceSafe,
  calculateDistanceFromCurrentLocation,
  calculateDistanceWithFallback,
} from '@/src/utils/distanceCalculation';

describe('distanceCalculation', () => {
  describe('calculateDistance', () => {
    it('should calculate distance between two points correctly', () => {
      // New York to Los Angeles (approx 3940 km)
      const lat1 = 40.7128;
      const lon1 = -74.0060;
      const lat2 = 34.0522;
      const lon2 = -118.2437;
      const distance = calculateDistance(lat1, lon1, lat2, lon2);
      expect(distance).toBeCloseTo(3935.74, 2);
    });

    it('should return 0 for same coordinates', () => {
      const lat1 = 40.7128;
      const lon1 = -74.0060;
      const distance = calculateDistance(lat1, lon1, lat1, lon1);
      expect(distance).toBe(0);
    });
  });

  describe('validateCoordinates', () => {
    it('should return true for valid coordinates', () => {
      expect(validateCoordinates(0, 0)).toBe(true);
      expect(validateCoordinates(90, 180)).toBe(true);
      expect(validateCoordinates(-90, -180)).toBe(true);
    });

    it('should return false for invalid latitude', () => {
      expect(validateCoordinates(91, 0)).toBe(false);
      expect(validateCoordinates(-91, 0)).toBe(false);
      expect(validateCoordinates(NaN, 0)).toBe(false);
    });

    it('should return false for invalid longitude', () => {
      expect(validateCoordinates(0, 181)).toBe(false);
      expect(validateCoordinates(0, -181)).toBe(false);
      expect(validateCoordinates(0, NaN)).toBe(false);
    });

    it('should return false for non-numeric coordinates', () => {
      expect(validateCoordinates('abc' as any, 0)).toBe(false);
      expect(validateCoordinates(0, 'def' as any)).toBe(false);
    });
  });

  describe('formatDistance', () => {
    it('should format distance in km', () => {
      expect(formatDistance(10.5)).toBe('10.5 km');
      expect(formatDistance(100)).toBe('100.0 km');
    });

    it('should format distance in meters for less than 1km', () => {
      expect(formatDistance(0.5)).toBe('500 m');
      expect(formatDistance(0.05)).toBe('50 m');
    });

    it('should format distance as < 0.1 km for very small distances', () => {
      expect(formatDistance(0.005)).toBe('< 0.1 km');
    });

    it('should not show unit if showUnit is false', () => {
      expect(formatDistance(10.5, false)).toBe('10.5');
      expect(formatDistance(0.5, false)).toBe('500');
      expect(formatDistance(0.005, false)).toBe('< 0.1');
    });
  });

  describe('calculateDistanceSafe', () => {
    it('should calculate distance if coordinates are valid', () => {
      const distance = calculateDistanceSafe(0, 0, 0.008993216, 0.008993216);
      expect(distance).toBeCloseTo(1.414, 2);
    });

    it('should return null if any coordinate is invalid', () => {
      const distance = calculateDistanceSafe(91, 0, 0, 0);
      expect(distance).toBeNull();
    });
  });

  describe('calculateDistanceFromCurrentLocation', () => {
    const mockCurrentLocation = { latitude: 0, longitude: 0, timestamp: Date.now() };

    it('should calculate distance from current location', () => {
      const distance = calculateDistanceFromCurrentLocation(mockCurrentLocation, 0.008993216, 0.008993216);
      expect(distance).toBeCloseTo(1.414, 2);
    });

    it('should return null if currentLocation is null', () => {
      const distance = calculateDistanceFromCurrentLocation(null, 10, 20);
      expect(distance).toBeNull();
    });
  });

  describe('calculateDistanceWithFallback', () => {
    const mockCurrentLocation = { latitude: 0, longitude: 0, timestamp: Date.now() };

    it('should use currentLocation if available', () => {
      const distance = calculateDistanceWithFallback(mockCurrentLocation, 5, 5, 0.008993216, 0.008993216);
      expect(distance).toBeCloseTo(1.414, 2);
    });

    it('should use profile location if currentLocation is null', () => {
      const distance = calculateDistanceWithFallback(null, 1, 1, 0.008993216, 0.008993216);
      expect(distance).toBeCloseTo(157.25, 2);
    });

    it('should return null if no location data is available', () => {
      const distance = calculateDistanceWithFallback(null, null, null, 10, 20);
      expect(distance).toBeNull();
    });
  });
});