import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import LocalityBottomSheetPicker, {
  LocalityBottomSheetPickerRef,
} from '@/src/components/pickers/LocalityBottomSheetPicker';
import { useTheme } from '@/src/hooks/useTheme';

// Mock dependencies
jest.mock('@gorhom/bottom-sheet', () => {
  const RN = jest.requireActual('react-native');
  const React = jest.requireActual('react');
  return {
    __esModule: true,
    default: React.forwardRef(({ children }: any, ref: any) => {
      React.useImperativeHandle(ref, () => ({
        expand: jest.fn(),
        close: jest.fn(),
      }));
      return <RN.View testID="bottom-sheet">{children}</RN.View>;
    }),
    BottomSheetView: RN.View,
    BottomSheetFlatList: RN.FlatList,
    BottomSheetTextInput: RN.TextInput,
  };
});

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
      textPrimary: '#000',
      textSecondary: '#888',
      border: '#ccc',
      background: '#fff',
    },
    isDark: false,
  }),
}));

describe('LocalityBottomSheetPicker', () => {
  const mockOnLocalitySelect = jest.fn();
  const mockOnClose = jest.fn();

  const defaultProps = {
    localities: ['Locality A', 'Locality B', 'Locality C'],
    onLocalitySelect: mockOnLocalitySelect,
    onClose: mockOnClose,
  };

  let bottomSheetRef: React.RefObject<LocalityBottomSheetPickerRef>;

  beforeEach(() => {
    jest.clearAllMocks();
    bottomSheetRef = React.createRef<LocalityBottomSheetPickerRef>();
  });

  it('renders correctly with localities', async () => {
    const { getByText, getByPlaceholderText } = render(
      <LocalityBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.present();
    });

    await waitFor(() => {
      expect(getByText('Select Locality')).toBeTruthy();
      expect(getByPlaceholderText('Search localities...')).toBeTruthy();
      expect(getByText('Locality A')).toBeTruthy();
      expect(getByText('Locality B')).toBeTruthy();
    });
  });

  it('calls onLocalitySelect and closes when a locality is selected', async () => {
    const { getByText } = render(
      <LocalityBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.present();
    });

    await waitFor(() => {
      fireEvent.press(getByText('Locality A'));
    });

    expect(mockOnLocalitySelect).toHaveBeenCalledWith('Locality A');
    expect(bottomSheetRef.current?.close).toHaveBeenCalled();
  });

  it('filters localities based on search query', async () => {
    const { getByPlaceholderText, getByText, queryByText } = render(
      <LocalityBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.present();
    });

    const searchInput = getByPlaceholderText('Search localities...');
    fireEvent.changeText(searchInput, 'Locality B');

    await waitFor(() => {
      expect(getByText('Locality B')).toBeTruthy();
      expect(queryByText('Locality A')).toBeNull();
    });
  });

  it('shows empty state when no localities match search', async () => {
    const { getByPlaceholderText, getByText } = render(
      <LocalityBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.present();
    });

    const searchInput = getByPlaceholderText('Search localities...');
    fireEvent.changeText(searchInput, 'xyz');

    await waitFor(() => {
      expect(getByText('No localities found')).toBeTruthy();
      expect(getByText('No localities match "xyz"')).toBeTruthy();
    });
  });

  it('calls dismiss when close button is pressed', () => {
    const { getByTestId } = render(
      <LocalityBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    act(() => {
      bottomSheetRef.current?.present();
    });

    fireEvent.press(getByTestId('close-button'));
    expect(bottomSheetRef.current?.close).toHaveBeenCalled();
  });
});
