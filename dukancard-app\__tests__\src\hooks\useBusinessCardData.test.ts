import { renderHook, act, waitFor } from '@testing-library/react-hooks';
import { useBusinessCardData, useProductsPagination, useReviewsPagination } from '@/src/hooks/useBusinessCardData';
import { fetchBusinessCardData, fetchBusinessProductsPaginated, fetchBusinessReviewsPaginated } from '@/backend/supabase/services/business/businessCardDataService';
import { fetchCustomAd } from '@/backend/supabase/services/ads/adService';

// Mock external dependencies
jest.mock('@/backend/supabase/services/business/businessCardDataService');
jest.mock('@/backend/supabase/services/ads/adService');

describe('useBusinessCardData', () => {
  const mockBusinessId = 'biz123';
  const mockBusinessSlug = 'test-business';
  const mockPincode = '123456';

  const mockCardData = {
    id: mockBusinessId,
    business_name: 'Test Business',
    business_slug: mockBusinessSlug,
    // ... other card data fields
  };

  const mockAdData = { id: 'ad1', title: 'Test Ad' };

  beforeEach(() => {
    jest.clearAllMocks();
    (fetchBusinessCardData as jest.Mock).mockResolvedValue({
      success: true,
      data: mockCardData,
    });
    (fetchCustomAd as jest.Mock).mockResolvedValue(mockAdData);
  });

  it('should return initial loading state', () => {
    const { result } = renderHook(() => useBusinessCardData(mockBusinessId, mockBusinessSlug, mockPincode));
    expect(result.current.loadingCardData).toBe(true);
    expect(result.current.adLoading).toBe(true);
  });

  it('should fetch and set card data and ad data on mount', async () => {
    const { result } = renderHook(() => useBusinessCardData(mockBusinessId, mockBusinessSlug, mockPincode));

    await waitFor(() => {
      expect(result.current.loadingCardData).toBe(false);
      expect(result.current.adLoading).toBe(false);
      expect(result.current.cardData).toEqual(mockCardData);
      expect(result.current.adData).toEqual(mockAdData);
      expect(fetchBusinessCardData).toHaveBeenCalledWith(mockBusinessId, mockBusinessSlug);
      expect(fetchCustomAd).toHaveBeenCalledWith(mockPincode);
    });
  });

  it('should handle error when fetching card data', async () => {
    (fetchBusinessCardData as jest.Mock).mockResolvedValue({
      success: false,
      error: 'Failed to fetch card data',
    });

    const { result } = renderHook(() => useBusinessCardData(mockBusinessId, mockBusinessSlug, mockPincode));

    await waitFor(() => {
      expect(result.current.loadingCardData).toBe(false);
      expect(result.current.cardData).toBeNull();
    });
  });

  it('should handle error when fetching ad data', async () => {
    (fetchCustomAd as jest.Mock).mockResolvedValue(null); // Simulate ad fetch failure

    const { result } = renderHook(() => useBusinessCardData(mockBusinessId, mockBusinessSlug, mockPincode));

    await waitFor(() => {
      expect(result.current.adLoading).toBe(false);
      expect(result.current.adData).toBeNull();
    });
  });
});

describe('useProductsPagination', () => {
  const mockBusinessId = 'biz123';
  const mockInitialProducts = [{ id: 'p1', name: 'Product 1' }];
  const mockNewProducts = [{ id: 'p2', name: 'Product 2' }];

  beforeEach(() => {
    jest.clearAllMocks();
    (fetchBusinessProductsPaginated as jest.Mock).mockResolvedValue({
      success: true,
      data: mockNewProducts,
      hasMore: false,
    });
  });

  it('should initialize with initial products', () => {
    const { result } = renderHook(() => useProductsPagination(mockBusinessId, mockInitialProducts));
    expect(result.current.allProducts).toEqual(mockInitialProducts);
    expect(result.current.loadingMoreProducts).toBe(false);
    expect(result.current.hasMoreProducts).toBe(true); // Default hasMore is true
  });

  it('should load more products', async () => {
    (fetchBusinessProductsPaginated as jest.Mock).mockResolvedValueOnce({
      success: true,
      data: mockNewProducts,
      hasMore: true,
    }).mockResolvedValueOnce({
      success: true,
      data: [{ id: 'p3', name: 'Product 3' }],
      hasMore: false,
    });

    const { result } = renderHook(() => useProductsPagination(mockBusinessId, mockInitialProducts));

    await act(async () => {
      await result.current.loadMoreProducts();
    });

    expect(result.current.allProducts).toEqual([...mockInitialProducts, ...mockNewProducts]);
    expect(result.current.loadingMoreProducts).toBe(false);
    expect(result.current.hasMoreProducts).toBe(true);
    expect(fetchBusinessProductsPaginated).toHaveBeenCalledWith(mockBusinessId, 2, 10, undefined, 'newest');

    await act(async () => {
      await result.current.loadMoreProducts();
    });

    expect(result.current.allProducts).toEqual([...mockInitialProducts, ...mockNewProducts, { id: 'p3', name: 'Product 3' }]);
    expect(result.current.hasMoreProducts).toBe(false);
  });

  it('should search products', async () => {
    const { result } = renderHook(() => useProductsPagination(mockBusinessId, mockInitialProducts));

    await act(async () => {
      await result.current.searchProducts('query');
    });

    expect(result.current.searchQuery).toBe('query');
    expect(result.current.allProducts).toEqual(mockNewProducts);
    expect(fetchBusinessProductsPaginated).toHaveBeenCalledWith(mockBusinessId, 1, 10, 'query', 'newest');
  });

  it('should sort products', async () => {
    const { result } = renderHook(() => useProductsPagination(mockBusinessId, mockInitialProducts));

    await act(async () => {
      await result.current.sortProducts('price_asc');
    });

    expect(result.current.sortBy).toBe('price_asc');
    expect(result.current.allProducts).toEqual(mockNewProducts);
    expect(fetchBusinessProductsPaginated).toHaveBeenCalledWith(mockBusinessId, 1, 10, '', 'price_asc');
  });
});

describe('useReviewsPagination', () => {
  const mockBusinessId = 'biz123';
  const mockInitialReviews = [{ id: 'r1', rating: 5 }];
  const mockNewReviews = [{ id: 'r2', rating: 4 }];

  beforeEach(() => {
    jest.clearAllMocks();
    (fetchBusinessReviewsPaginated as jest.Mock).mockResolvedValue({
      success: true,
      data: mockNewReviews,
      hasMore: false,
    });
  });

  it('should initialize with initial reviews', () => {
    const { result } = renderHook(() => useReviewsPagination(mockBusinessId, mockInitialReviews));
    expect(result.current.allReviews).toEqual(mockInitialReviews);
    expect(result.current.loadingMoreReviews).toBe(false);
    expect(result.current.hasMoreReviews).toBe(true); // Default hasMore is true
  });

  it('should load more reviews', async () => {
    (fetchBusinessReviewsPaginated as jest.Mock).mockResolvedValueOnce({
      success: true,
      data: mockNewReviews,
      hasMore: true,
    }).mockResolvedValueOnce({
      success: true,
      data: [{ id: 'r3', rating: 3 }],
      hasMore: false,
    });

    const { result } = renderHook(() => useReviewsPagination(mockBusinessId, mockInitialReviews));

    await act(async () => {
      await result.current.loadMoreReviews();
    });

    expect(result.current.allReviews).toEqual([...mockInitialReviews, ...mockNewReviews]);
    expect(result.current.loadingMoreReviews).toBe(false);
    expect(result.current.hasMoreReviews).toBe(true);
    expect(fetchBusinessReviewsPaginated).toHaveBeenCalledWith(mockBusinessId, 2, 10, 'newest');

    await act(async () => {
      await result.current.loadMoreReviews();
    });

    expect(result.current.allReviews).toEqual([...mockInitialReviews, ...mockNewReviews, { id: 'r3', rating: 3 }]);
    expect(result.current.hasMoreReviews).toBe(false);
  });

  it('should sort reviews', async () => {
    const { result } = renderHook(() => useReviewsPagination(mockBusinessId, mockInitialReviews));

    await act(async () => {
      await result.current.sortReviews('highest_rated');
    });

    expect(result.current.sortBy).toBe('highest_rated');
    expect(result.current.allReviews).toEqual(mockNewReviews);
    expect(fetchBusinessReviewsPaginated).toHaveBeenCalledWith(mockBusinessId, 1, 10, 'highest_rated');
  });
});