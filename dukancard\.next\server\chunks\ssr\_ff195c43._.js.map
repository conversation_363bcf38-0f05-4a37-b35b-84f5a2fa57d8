{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/addressValidation.ts"], "sourcesContent": ["/**\r\n * Customer address validation utility\r\n * Checks if customer has complete address information\r\n */\r\n\r\nexport interface CustomerAddressData {\r\n  pincode?: string | null;\r\n  state?: string | null;\r\n  city?: string | null;\r\n  locality?: string | null;\r\n  // address is optional as per requirements\r\n  address?: string | null;\r\n}\r\n\r\nexport interface CustomerProfileData extends CustomerAddressData {\r\n  name?: string | null;\r\n}\r\n\r\n/**\r\n * Validates if customer address is complete\r\n * Address field is optional, but pincode, state, city, and locality are required\r\n */\r\nexport function isCustomerAddressComplete(addressData: CustomerAddressData): boolean {\r\n  const { pincode, state, city, locality } = addressData;\r\n\r\n  // Check if required fields are present and not empty\r\n  return !!(\r\n    pincode && pincode.trim() !== '' &&\r\n    state && state.trim() !== '' &&\r\n    city && city.trim() !== '' &&\r\n    locality && locality.trim() !== ''\r\n  );\r\n}\r\n\r\n/**\r\n * Gets missing address fields for customer\r\n */\r\nexport function getMissingAddressFields(addressData: CustomerAddressData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  if (!addressData.pincode || addressData.pincode.trim() === '') {\r\n    missing.push('pincode');\r\n  }\r\n  if (!addressData.state || addressData.state.trim() === '') {\r\n    missing.push('state');\r\n  }\r\n  if (!addressData.city || addressData.city.trim() === '') {\r\n    missing.push('city');\r\n  }\r\n  if (!addressData.locality || addressData.locality.trim() === '') {\r\n    missing.push('locality');\r\n  }\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing address fields\r\n */\r\nexport function getAddressValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n\r\n/**\r\n * Validates if customer name is complete\r\n */\r\nexport function isCustomerNameComplete(name?: string | null): boolean {\r\n  return !!(name && name.trim() !== '');\r\n}\r\n\r\n/**\r\n * Validates if customer profile is complete (both name and address)\r\n */\r\nexport function isCustomerProfileComplete(profileData: CustomerProfileData): boolean {\r\n  return isCustomerNameComplete(profileData.name) && isCustomerAddressComplete(profileData);\r\n}\r\n\r\n/**\r\n * Gets missing profile fields for customer (name + address)\r\n */\r\nexport function getMissingProfileFields(profileData: CustomerProfileData): string[] {\r\n  const missing: string[] = [];\r\n\r\n  // Check name\r\n  if (!isCustomerNameComplete(profileData.name)) {\r\n    missing.push('name');\r\n  }\r\n\r\n  // Check address fields\r\n  const missingAddressFields = getMissingAddressFields(profileData);\r\n  missing.push(...missingAddressFields);\r\n\r\n  return missing;\r\n}\r\n\r\n/**\r\n * Generates a user-friendly message for missing profile fields (name + address)\r\n */\r\nexport function getProfileValidationMessage(missingFields: string[]): string {\r\n  if (missingFields.length === 0) {\r\n    return '';\r\n  }\r\n\r\n  const fieldNames = missingFields.map(field => {\r\n    switch (field) {\r\n      case 'name': return 'Name';\r\n      case 'pincode': return 'Pincode';\r\n      case 'state': return 'State';\r\n      case 'city': return 'City';\r\n      case 'locality': return 'Locality';\r\n      default: return field;\r\n    }\r\n  });\r\n\r\n  if (fieldNames.length === 1) {\r\n    return `Please update your ${fieldNames[0]} in your profile.`;\r\n  } else if (fieldNames.length === 2) {\r\n    return `Please update your ${fieldNames.join(' and ')} in your profile.`;\r\n  } else {\r\n    const lastField = fieldNames.pop();\r\n    return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAmBM,SAAS,0BAA0B,WAAgC;IACxE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;IAE3C,qDAAqD;IACrD,OAAO,CAAC,CAAC,CACP,WAAW,QAAQ,IAAI,OAAO,MAC9B,SAAS,MAAM,IAAI,OAAO,MAC1B,QAAQ,KAAK,IAAI,OAAO,MACxB,YAAY,SAAS,IAAI,OAAO,EAClC;AACF;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,IAAI,CAAC,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,IAAI,OAAO,IAAI;QAC7D,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,KAAK,IAAI,YAAY,KAAK,CAAC,IAAI,OAAO,IAAI;QACzD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,OAAO,IAAI;QACvD,QAAQ,IAAI,CAAC;IACf;IACA,IAAI,CAAC,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,IAAI,OAAO,IAAI;QAC/D,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF;AAKO,SAAS,uBAAuB,IAAoB;IACzD,OAAO,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,OAAO,EAAE;AACtC;AAKO,SAAS,0BAA0B,WAAgC;IACxE,OAAO,uBAAuB,YAAY,IAAI,KAAK,0BAA0B;AAC/E;AAKO,SAAS,wBAAwB,WAAgC;IACtE,MAAM,UAAoB,EAAE;IAE5B,aAAa;IACb,IAAI,CAAC,uBAAuB,YAAY,IAAI,GAAG;QAC7C,QAAQ,IAAI,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,uBAAuB,wBAAwB;IACrD,QAAQ,IAAI,IAAI;IAEhB,OAAO;AACT;AAKO,SAAS,4BAA4B,aAAuB;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,MAAM,aAAa,cAAc,GAAG,CAAC,CAAA;QACnC,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE,CAAC,iBAAiB,CAAC;IAC/D,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;QAClC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,SAAS,iBAAiB,CAAC;IAC1E,OAAO;QACL,MAAM,YAAY,WAAW,GAAG;QAChC,OAAO,CAAC,mBAAmB,EAAE,WAAW,IAAI,CAAC,MAAM,MAAM,EAAE,UAAU,iBAAiB,CAAC;IACzF;AACF", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/customerProfiles/addressValidation.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { isCustomerAddressComplete, getMissingAddressFields, getAddressValidationMessage, type CustomerAddressData } from \"@/lib/utils/addressValidation\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\n/**\r\n * Checks if customer has complete address information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerAddress(userId: string): Promise<{\r\n  isValid: boolean;\r\n  missingFields?: string[];\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    // Fetch customer address data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer profile for address validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your address information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n      };\r\n    }\r\n    \r\n    const addressData: CustomerAddressData = {\r\n      pincode: profile?.pincode,\r\n      state: profile?.state,\r\n      city: profile?.city,\r\n      locality: profile?.locality,\r\n      address: profile?.address\r\n    };\r\n    \r\n    const isValid = isCustomerAddressComplete(addressData);\r\n    \r\n    if (!isValid) {\r\n      const missingFields = getMissingAddressFields(addressData);\r\n      const message = getAddressValidationMessage(missingFields);\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n      \r\n      return {\r\n        isValid: false,\r\n        missingFields,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n    \r\n    return { isValid: true };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error during address validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your address. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your address information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check address and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteAddress(userId: string): Promise<void> {\r\n  const validation = await validateCustomerAddress(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Checks if customer has complete name information\r\n * Returns validation result and redirect URL if needed\r\n */\r\nexport async function validateCustomerName(userId: string): Promise<{\r\n  isValid: boolean;\r\n  message?: string;\r\n  redirectUrl?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    // Fetch customer name data\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('name')\r\n      .eq('id', userId)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error('Error fetching customer profile for name validation:', error);\r\n      // If we can't fetch the profile, assume invalid and redirect\r\n      return {\r\n        isValid: false,\r\n        message: 'Unable to verify your profile information. Please update your profile.',\r\n        redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n      };\r\n    }\r\n\r\n    // Check if name is present and not empty\r\n    const isValid = !!(profile?.name && profile.name.trim() !== '');\r\n\r\n    if (!isValid) {\r\n      const message = 'Please complete your name in your profile to access the dashboard.';\r\n      const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;\r\n\r\n      return {\r\n        isValid: false,\r\n        message,\r\n        redirectUrl\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n\r\n  } catch (error) {\r\n    console.error('Unexpected error during name validation:', error);\r\n    return {\r\n      isValid: false,\r\n      message: 'An error occurred while validating your profile. Please update your profile.',\r\n      redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check name and redirect if incomplete\r\n * Use this in customer dashboard pages\r\n */\r\nexport async function requireCompleteName(userId: string): Promise<void> {\r\n  const validation = await validateCustomerName(userId);\r\n\r\n  if (!validation.isValid && validation.redirectUrl) {\r\n    redirect(validation.redirectUrl);\r\n  }\r\n}\r\n\r\n/**\r\n * Middleware function to check both address and name, redirect if incomplete\r\n * Use this in customer dashboard pages (except settings page)\r\n * Settings page is exempt from address validation\r\n */\r\nexport async function requireCompleteProfile(userId: string, exemptFromAddressValidation: boolean = false): Promise<void> {\r\n  // Always check name (required for all dashboard access)\r\n  await requireCompleteName(userId);\r\n\r\n  // Only check address if not exempt (settings page is exempt)\r\n  if (!exemptFromAddressValidation) {\r\n    await requireCompleteAddress(userId);\r\n  }\r\n}\r\n\r\n/**\r\n * Get customer address data for forms\r\n */\r\nexport async function getCustomerAddressData(userId: string): Promise<{\r\n  data?: CustomerAddressData;\r\n  error?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n  \r\n  try {\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('pincode, state, city, locality, address')\r\n      .eq('id', userId)\r\n      .single();\r\n    \r\n    if (error) {\r\n      console.error('Error fetching customer address data:', error);\r\n      return { error: 'Failed to fetch address data' };\r\n    }\r\n    \r\n    return {\r\n      data: {\r\n        pincode: profile?.pincode,\r\n        state: profile?.state,\r\n        city: profile?.city,\r\n        locality: profile?.locality,\r\n        address: profile?.address\r\n      }\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error fetching address data:', error);\r\n    return { error: 'An unexpected error occurred' };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAAA;;;;;;;AAMO,eAAe,wBAAwB,MAAc;IAM1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,8BAA8B;QAC9B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2DAA2D;YACzE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,MAAM,cAAmC;YACvC,SAAS,SAAS;YAClB,OAAO,SAAS;YAChB,MAAM,SAAS;YACf,UAAU,SAAS;YACnB,SAAS,SAAS;QACpB;QAEA,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAAE;QAE1C,IAAI,CAAC,SAAS;YACZ,MAAM,gBAAgB,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE;YAC9C,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD,EAAE;YAC5C,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,uBAAuB,MAAc;IACzD,MAAM,aAAa,MAAM,wBAAwB;IAEjD,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAMO,eAAe,qBAAqB,MAAc;IAKvD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,2BAA2B;QAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wDAAwD;YACtE,6DAA6D;YAC7D,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;QACF;QAEA,yCAAyC;QACzC,MAAM,UAAU,CAAC,CAAC,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,OAAO,EAAE;QAE9D,IAAI,CAAC,SAAS;YACZ,MAAM,UAAU;YAChB,MAAM,cAAc,CAAC,oCAAoC,EAAE,mBAAmB,UAAU;YAExF,OAAO;gBACL,SAAS;gBACT;gBACA;YACF;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;IACF;AACF;AAMO,eAAe,oBAAoB,MAAc;IACtD,MAAM,aAAa,MAAM,qBAAqB;IAE9C,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,WAAW,EAAE;QACjD,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,WAAW;IACjC;AACF;AAOO,eAAe,uBAAuB,MAAc,EAAE,8BAAuC,KAAK;IACvG,wDAAwD;IACxD,MAAM,oBAAoB;IAE1B,6DAA6D;IAC7D,IAAI,CAAC,6BAA6B;QAChC,MAAM,uBAAuB;IAC/B;AACF;AAKO,eAAe,uBAAuB,MAAc;IAIzD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;gBAAE,OAAO;YAA+B;QACjD;QAEA,OAAO;YACL,MAAM;gBACJ,SAAS,SAAS;gBAClB,OAAO,SAAS;gBAChB,MAAM,SAAS;gBACf,UAAU,SAAS;gBACnB,SAAS,SAAS;YACpB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;;;IA9LsB;IAiEA;IAYA;IAuDA;IAaA;IAaA;;AA9JA,+OAAA;AAiEA,+OAAA;AAYA,+OAAA;AAuDA,+OAAA;AAaA,+OAAA;AAaA,+OAAA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/admin.ts"], "sourcesContent": ["import { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * Creates a Supabase admin client with the service role key.\r\n * This client has admin privileges and should only be used on the server.\r\n * Never expose your service_role key in the browser.\r\n */\r\nexport function createAdminClient() {\r\n  return createSupabaseClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,gFAExB,QAAQ,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/products/fetchProductsByIds.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { ProductsServices } from \"../../../../dukancard-app/src/types/database/products-services\";\r\n\r\ntype FetchedProduct = Pick<\r\n  ProductsServices,\r\n  \"id\" | \"name\" | \"base_price\" | \"discounted_price\" | \"image_url\" | \"slug\"\r\n>;\r\n\r\n/**\r\n * Fetch products by their IDs using admin client to bypass RLS\r\n * This is used for displaying linked products in feed posts\r\n */\r\nexport async function fetchProductsByIds(productIds: string[]): Promise<{\r\n  success: boolean;\r\n  data?: FetchedProduct[];\r\n  error?: string;\r\n}> {\r\n  if (!productIds || productIds.length === 0) {\r\n    return {\r\n      success: true,\r\n      data: [],\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    const { data, error } = await supabaseAdmin\r\n      .from(\"products_services\")\r\n      .select(\"id, name, base_price, discounted_price, image_url, slug\")\r\n      .in(\"id\", productIds)\r\n      .eq(\"is_available\", true);\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching products by IDs:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in fetchProductsByIds:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;;;AAYO,eAAe,mBAAmB,UAAoB;IAK3D,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;QAC1C,OAAO;YACL,SAAS;YACT,MAAM,EAAE;QACV;IACF;IAEA,IAAI;QACF,0CAA0C;QAC1C,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,2DACP,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,gBAAgB;QAEtB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,QAAQ,EAAE;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IAzCsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/storage-paths.ts"], "sourcesContent": ["/**\r\n * Scalable Storage Path Utilities\r\n *\r\n * This module provides utilities for generating scalable storage paths\r\n * that can handle billions of users efficiently using hash-based distribution.\r\n */\r\n\r\n/**\r\n * Generate scalable user path using hash-based distribution\r\n *\r\n * @param userId - The user's UUID\r\n * @returns Scalable path: users/{prefix}/{midfix}/{userId}\r\n *\r\n * Example:\r\n * - Input: \"a1b2c3d4-e5f6-7890-abcd-ef1234567890\"\r\n * - Output: \"users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890\"\r\n */\r\nexport function getScalableUserPath(userId: string): string {\r\n  if (!userId || typeof userId !== 'string') {\r\n    throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);\r\n  }\r\n\r\n  if (userId.length < 4) {\r\n    throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);\r\n  }\r\n\r\n  const prefix = userId.substring(0, 2).toLowerCase();\r\n  const midfix = userId.substring(2, 4).toLowerCase();\r\n\r\n  return `users/${prefix}/${midfix}/${userId}`;\r\n}\r\n\r\n/**\r\n * Generate profile image path\r\n */\r\nexport function getProfileImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/profile/logo_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate product image path (legacy - for backward compatibility)\r\n * @deprecated Use getProductBaseImagePath or getProductVariantImagePath instead\r\n */\r\nexport function getProductImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate base product image path\r\n */\r\nexport function getProductBaseImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate product variant image path\r\n */\r\nexport function getProductVariantImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  variantId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate gallery image path\r\n */\r\nexport function getGalleryImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/gallery/gallery_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate post image path\r\n */\r\nexport function getPostImagePath(\r\n  userId: string,\r\n  postId: string,\r\n  imageIndex: number,\r\n  timestamp: number,\r\n  createdAt?: string\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n\r\n  // Use post creation date if provided, otherwise use current date (for backward compatibility)\r\n  const dateToUse = createdAt ? new Date(createdAt) : new Date();\r\n  const year = dateToUse.getFullYear();\r\n  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate post folder path for deletion\r\n */\r\nexport function getPostFolderPath(userId: string, postId: string, createdAt: string): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  const postDate = new Date(createdAt);\r\n  const year = postDate.getFullYear();\r\n  const month = String(postDate.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}`;\r\n}\r\n\r\n/**\r\n * Generate customer avatar image path\r\n */\r\nexport function getCustomerAvatarPath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/avatar/avatar_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate customer post image path\r\n */\r\nexport function getCustomerPostImagePath(\r\n  userId: string,\r\n  postId: string,\r\n  imageIndex: number,\r\n  timestamp: number,\r\n  createdAt?: string\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n\r\n  // Use post creation date if provided, otherwise use current date (for backward compatibility)\r\n  const dateToUse = createdAt ? new Date(createdAt) : new Date();\r\n  const year = dateToUse.getFullYear();\r\n  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate custom ad image path\r\n */\r\nexport function getCustomAdImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/ads/custom_ad_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate custom header image path\r\n */\r\nexport function getCustomHeaderImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/branding/header_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate theme-specific custom header image path\r\n */\r\nexport function getThemeSpecificHeaderImagePath(\r\n  userId: string,\r\n  timestamp: number,\r\n  theme: 'light' | 'dark'\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/branding/header_${theme}_${timestamp}.webp`;\r\n}\r\n\r\n// Legacy utilities removed since migration is complete\r\n\r\n/**\r\n * Path validation utilities\r\n */\r\nexport class PathValidator {\r\n  /**\r\n   * Validate if a path follows the new scalable structure\r\n   */\r\n  static isScalablePath(path: string): boolean {\r\n    return path.startsWith('users/') && path.split('/').length >= 4;\r\n  }\r\n\r\n  /**\r\n   * Extract user ID from scalable path\r\n   */\r\n  static extractUserIdFromPath(path: string): string | null {\r\n    if (!this.isScalablePath(path)) {\r\n      return null;\r\n    }\r\n\r\n    const parts = path.split('/');\r\n    return parts[3]; // users/{prefix}/{midfix}/{userId}/...\r\n  }\r\n\r\n  /**\r\n   * Validate path structure integrity\r\n   */\r\n  static validatePathStructure(userId: string, path: string): boolean {\r\n    const expectedUserPath = getScalableUserPath(userId);\r\n    return path.startsWith(expectedUserPath);\r\n  }\r\n}\r\n\r\n/**\r\n * Storage analytics utilities\r\n */\r\nexport class StorageAnalytics {\r\n  /**\r\n   * Get storage distribution info for monitoring\r\n   */\r\n  static getDistributionInfo(userId: string): {\r\n    prefix: string;\r\n    midfix: string;\r\n    bucket: string;\r\n    estimatedPeers: number;\r\n  } {\r\n    const prefix = userId.substring(0, 2).toLowerCase();\r\n    const midfix = userId.substring(2, 4).toLowerCase();\r\n\r\n    // Estimate number of users in same bucket (assuming even distribution)\r\n    const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets\r\n    const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users\r\n\r\n    return {\r\n      prefix,\r\n      midfix,\r\n      bucket: `${prefix}/${midfix}`,\r\n      estimatedPeers\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;;;;;;;;CASC;;;;;;;;;;;;;;;;;AACM,SAAS,oBAAoB,MAAc;IAChD,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,OAAO,OAAO,SAAS,EAAE,QAAQ;IAC3F;IAEA,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,MAAM,IAAI,MAAM,CAAC,yDAAyD,EAAE,QAAQ;IACtF;IAEA,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;IACjD,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;IAEjD,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ;AAC9C;AAKO,SAAS,oBAAoB,MAAc,EAAE,SAAiB;IACnE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,cAAc,EAAE,UAAU,KAAK,CAAC;AACrD;AAMO,SAAS,oBACd,MAAc,EACd,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAClF;AAKO,SAAS,wBACd,MAAc,EACd,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,YAAY,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AACvF;AAKO,SAAS,2BACd,MAAc,EACd,SAAiB,EACjB,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,CAAC,EAAE,UAAU,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC/F;AAKO,SAAS,oBAAoB,MAAc,EAAE,SAAiB;IACnE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,UAAU,KAAK,CAAC;AACxD;AAKO,SAAS,iBACd,MAAc,EACd,MAAc,EACd,UAAkB,EAClB,SAAiB,EACjB,SAAkB;IAElB,MAAM,WAAW,oBAAoB;IAErC,8FAA8F;IAC9F,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,IAAI;IACxD,MAAM,OAAO,UAAU,WAAW;IAClC,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE3D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC7F;AAKO,SAAS,kBAAkB,MAAc,EAAE,MAAc,EAAE,SAAiB;IACjF,MAAM,WAAW,oBAAoB;IACrC,MAAM,WAAW,IAAI,KAAK;IAC1B,MAAM,OAAO,SAAS,WAAW;IACjC,MAAM,QAAQ,OAAO,SAAS,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE1D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ;AACvD;AAKO,SAAS,sBAAsB,MAAc,EAAE,SAAiB;IACrE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,eAAe,EAAE,UAAU,KAAK,CAAC;AACtD;AAKO,SAAS,yBACd,MAAc,EACd,MAAc,EACd,UAAkB,EAClB,SAAiB,EACjB,SAAkB;IAElB,MAAM,WAAW,oBAAoB;IAErC,8FAA8F;IAC9F,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,IAAI;IACxD,MAAM,OAAO,UAAU,WAAW;IAClC,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE3D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC7F;AAKO,SAAS,qBAAqB,MAAc,EAAE,SAAiB;IACpE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,eAAe,EAAE,UAAU,KAAK,CAAC;AACtD;AAKO,SAAS,yBAAyB,MAAc,EAAE,SAAiB;IACxE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,UAAU,KAAK,CAAC;AACxD;AAKO,SAAS,gCACd,MAAc,EACd,SAAiB,EACjB,KAAuB;IAEvB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,MAAM,CAAC,EAAE,UAAU,KAAK,CAAC;AACjE;AAOO,MAAM;IACX;;GAEC,GACD,OAAO,eAAe,IAAY,EAAW;QAC3C,OAAO,KAAK,UAAU,CAAC,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,IAAI;IAChE;IAEA;;GAEC,GACD,OAAO,sBAAsB,IAAY,EAAiB;QACxD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO;YAC9B,OAAO;QACT;QAEA,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,OAAO,KAAK,CAAC,EAAE,EAAE,uCAAuC;IAC1D;IAEA;;GAEC,GACD,OAAO,sBAAsB,MAAc,EAAE,IAAY,EAAW;QAClE,MAAM,mBAAmB,oBAAoB;QAC7C,OAAO,KAAK,UAAU,CAAC;IACzB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,oBAAoB,MAAc,EAKvC;QACA,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;QACjD,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;QAEjD,uEAAuE;QACvE,MAAM,eAAe,KAAK,KAAK,KAAK,IAAI,iBAAiB;QACzD,MAAM,iBAAiB,KAAK,KAAK,CAAC,UAAU,eAAe,wBAAwB;QAEnF,OAAO;YACL;YACA;YACA,QAAQ,GAAG,OAAO,CAAC,EAAE,QAAQ;YAC7B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/upload-post-media.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { getPostImagePath, getPostFolderPath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface PostMediaUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload and process image for business post\r\n * Future-proof structure: {userId}/posts/{year}/{month}/{postId}/image_0_{timestamp}.webp\r\n */\r\nexport async function uploadPostImage(\r\n  formData: FormData,\r\n  postId: string,\r\n  postCreatedAt?: string\r\n): Promise<PostMediaUploadResult> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const userId = user.id;\r\n  const imageFile = formData.get(\"imageFile\") as File | null;\r\n\r\n  if (!imageFile) {\r\n    return { success: false, error: \"No image file provided.\" };\r\n  }\r\n\r\n  // Use the provided post creation date for consistent folder structure\r\n\r\n  // Validate file type (strict backend validation)\r\n  const allowedTypes = [\"image/jpeg\", \"image/png\", \"image/gif\", \"image/webp\"];\r\n  const allowedExtensions = [\".jpg\", \".jpeg\", \".png\", \".gif\", \".webp\"];\r\n\r\n  if (!allowedTypes.includes(imageFile.type)) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.\"\r\n    };\r\n  }\r\n\r\n  // Validate file extension as additional security measure\r\n  const fileName = imageFile.name.toLowerCase();\r\n  const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));\r\n  if (!hasValidExtension) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid file extension. Please upload files with .jpg, .jpeg, .png, .gif, or .webp extensions.\"\r\n    };\r\n  }\r\n\r\n  // Backend size validation (critical security check)\r\n  const maxSize = 15 * 1024 * 1024; // 15MB - matches industry standards\r\n  if (imageFile.size > maxSize) {\r\n    const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(2);\r\n    return {\r\n      success: false,\r\n      error: `File size (${fileSizeMB}MB) exceeds the 15MB limit. Please choose a smaller image.`\r\n    };\r\n  }\r\n\r\n  // Additional security: Check for minimum file size (avoid empty files)\r\n  if (imageFile.size < 100) { // 100 bytes minimum\r\n    return {\r\n      success: false,\r\n      error: \"File appears to be empty or corrupted. Please try a different image.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Create scalable path structure for billions of users\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const bucketName = \"business\";\r\n    const imagePath = getPostImagePath(userId, postId, 0, timestamp, postCreatedAt);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Post Image Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL using admin client\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Error processing post image:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Failed to process image. Please try a different image.\"\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Delete entire post folder and all its contents\r\n * This removes all files in the post folder, effectively deleting the folder\r\n */\r\nexport async function deletePostMedia(\r\n  userId: string,\r\n  postId: string,\r\n  createdAt: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // Use admin client for storage operations to bypass RLS\r\n  const adminSupabase = createAdminClient();\r\n\r\n  try {\r\n    const bucketName = \"businesses\";\r\n    const postFolderPath = getPostFolderPath(userId, postId, createdAt);\r\n\r\n    // List all files in the post folder\r\n    const { data: files, error: listError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .list(postFolderPath, {\r\n        limit: 1000, // Set a reasonable limit for safety\r\n        sortBy: { column: 'name', order: 'asc' }\r\n      });\r\n\r\n    if (listError) {\r\n      // If folder doesn't exist, consider it successful (already deleted)\r\n      if (listError.message?.includes('not found') ||\r\n          listError.message?.includes('does not exist') ||\r\n          listError.message?.includes('The resource was not found')) {\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error listing post media files:\", listError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to list media files: ${listError.message}`,\r\n      };\r\n    }\r\n\r\n    if (!files || files.length === 0) {\r\n      // No files to delete, folder is already empty or doesn't exist\r\n      return { success: true };\r\n    }\r\n\r\n    // Create full paths for all files in the folder\r\n    const filePaths = files.map(file => `${postFolderPath}/${file.name}`);\r\n\r\n    // Delete all files in the post folder using admin client\r\n    // In object storage, deleting all files effectively removes the folder\r\n    const { error: deleteError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .remove(filePaths);\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting post folder contents:\", deleteError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to delete post folder: ${deleteError.message}`,\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Error in deletePostMedia:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred while deleting post folder.\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;;;;;AAYO,eAAe,gBACpB,QAAkB,EAClB,MAAc,EACd,aAAsB;IAEtB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,MAAM,SAAS,KAAK,EAAE;IACtB,MAAM,YAAY,SAAS,GAAG,CAAC;IAE/B,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,sEAAsE;IAEtE,iDAAiD;IACjD,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;KAAa;IAC3E,MAAM,oBAAoB;QAAC;QAAQ;QAAS;QAAQ;QAAQ;KAAQ;IAEpE,IAAI,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,GAAG;QAC1C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,yDAAyD;IACzD,MAAM,WAAW,UAAU,IAAI,CAAC,WAAW;IAC3C,MAAM,oBAAoB,kBAAkB,IAAI,CAAC,CAAA,MAAO,SAAS,QAAQ,CAAC;IAC1E,IAAI,CAAC,mBAAmB;QACtB,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,oDAAoD;IACpD,MAAM,UAAU,KAAK,OAAO,MAAM,oCAAoC;IACtE,IAAI,UAAU,IAAI,GAAG,SAAS;QAC5B,MAAM,aAAa,CAAC,UAAU,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;QAC5D,OAAO;YACL,SAAS;YACT,OAAO,CAAC,WAAW,EAAE,WAAW,0DAA0D,CAAC;QAC7F;IACF;IAEA,uEAAuE;IACvE,IAAI,UAAU,IAAI,GAAG,KAAK;QACxB,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI;QACF,uDAAuD;QACvD,MAAM,YAAY,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC1D,MAAM,aAAa;QACnB,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,QAAQ,GAAG,WAAW;QAEjE,4DAA4D;QAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,UAAU,WAAW;QAE1D,wDAAwD;QACxD,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEtC,gDAAgD;QAChD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,YACL,MAAM,CAAC,WAAW,YAAY;YAC7B,aAAa,UAAU,IAAI;YAC3B,QAAQ;QACV;QAEF,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,wBAAwB,EAAE,YAAY,OAAO,EAAE;YACzD;QACF;QAEA,wCAAwC;QACxC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,cAAc,OAAO,CAC5C,IAAI,CAAC,YACL,YAAY,CAAC;QAEhB,IAAI,CAAC,SAAS,WAAW;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,KAAK,QAAQ,SAAS;QACxB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAMO,eAAe,gBACpB,MAAc,EACd,MAAc,EACd,SAAiB;IAEjB,wDAAwD;IACxD,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;IAEtC,IAAI;QACF,MAAM,aAAa;QACnB,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,QAAQ;QAEzD,oCAAoC;QACpC,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAAc,OAAO,CAClE,IAAI,CAAC,YACL,IAAI,CAAC,gBAAgB;YACpB,OAAO;YACP,QAAQ;gBAAE,QAAQ;gBAAQ,OAAO;YAAM;QACzC;QAEF,IAAI,WAAW;YACb,oEAAoE;YACpE,IAAI,UAAU,OAAO,EAAE,SAAS,gBAC5B,UAAU,OAAO,EAAE,SAAS,qBAC5B,UAAU,OAAO,EAAE,SAAS,+BAA+B;gBAC7D,OAAO;oBAAE,SAAS;gBAAK;YACzB;YACA,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,4BAA4B,EAAE,UAAU,OAAO,EAAE;YAC3D;QACF;QAEA,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAChC,+DAA+D;YAC/D,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,gDAAgD;QAChD,MAAM,YAAY,MAAM,GAAG,CAAC,CAAA,OAAQ,GAAG,eAAe,CAAC,EAAE,KAAK,IAAI,EAAE;QAEpE,yDAAyD;QACzD,uEAAuE;QACvE,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,YACL,MAAM,CAAC;QAEV,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,8BAA8B,EAAE,YAAY,OAAO,EAAE;YAC/D;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IA5LsB;IA2HA;;AA3HA,+OAAA;AA2HA,+OAAA", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/crud.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { PostFormData } from '@/lib/types/posts';\r\nimport { ActionResponse } from '@/lib/types/api';\r\nimport { deletePostMedia } from '@/lib/actions/shared/upload-post-media';\r\n\r\n/**\r\n * Create a new post\r\n */\r\nexport async function createPost(formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to create a post'\r\n    };\r\n  }\r\n\r\n  // Get the user's business profile\r\n  const { data: businessProfile, error: profileError } = await supabase\r\n    .from('business_profiles')\r\n    .select('id, city_slug, state_slug, locality_slug, pincode, logo_url')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError || !businessProfile) {\r\n    return {\r\n      success: false,\r\n      message: 'Business profile not found',\r\n      error: 'You must have a business profile to create a post'\r\n    };\r\n  }\r\n\r\n  // Prepare post data\r\n  const postData = {\r\n    business_id: user.id,\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    city_slug: businessProfile.city_slug,\r\n    state_slug: businessProfile.state_slug,\r\n    locality_slug: businessProfile.locality_slug,\r\n    pincode: businessProfile.pincode,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    author_avatar: businessProfile.logo_url\r\n  };\r\n\r\n  // Insert the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .insert(postData)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error creating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to create post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post created successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the content of an existing post (for inline editing)\r\n */\r\nexport async function updatePostContent(postId: string, content: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the content and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      content: content.trim(),\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post content:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the product_ids of an existing post (for inline editing)\r\n */\r\nexport async function updatePostProducts(postId: string, productIds: string[]): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the product_ids and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      product_ids: productIds,\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post products:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post products',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post products updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update an existing post (full update for form submissions)\r\n */\r\nexport async function updatePost(postId: string, formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Prepare update data\r\n  const updateData = {\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    updated_at: new Date().toISOString()\r\n  };\r\n\r\n  // Update the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update(updateData)\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Delete a post\r\n */\r\nexport async function deletePost(postId: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to delete a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user, get creation date for media deletion\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id, created_at, image_url')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to delete it'\r\n    };\r\n  }\r\n\r\n  // First, attempt to delete the post folder from storage\r\n  // This ensures we clean up any files that might exist, regardless of image_url status\r\n  try {\r\n    const mediaDeleteResult = await deletePostMedia(user.id, postId, existingPost.created_at);\r\n    if (!mediaDeleteResult.success && mediaDeleteResult.error) {\r\n      console.error('Error deleting business post media:', mediaDeleteResult.error);\r\n      return {\r\n        success: false,\r\n        message: 'Failed to delete post images',\r\n        error: `Cannot delete post: ${mediaDeleteResult.error}`\r\n      };\r\n    }\r\n  } catch (mediaError) {\r\n    console.error('Error deleting business post media:', mediaError);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to delete post images',\r\n      error: 'Cannot delete post: Failed to clean up associated images'\r\n    };\r\n  }\r\n\r\n  // Only delete the post after successful media deletion\r\n  const { error } = await supabase\r\n    .from('business_posts')\r\n    .delete()\r\n    .eq('id', postId);\r\n\r\n  if (error) {\r\n    console.error('Error deleting post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to delete post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post deleted successfully'\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAGA;;;;;;;AAKO,eAAe,WAAW,QAAsB;IACrD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,qBACL,MAAM,CAAC,+DACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,gBAAgB,CAAC,iBAAiB;QACpC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,WAAW;QACf,aAAa,KAAK,EAAE;QACpB,SAAS,SAAS,OAAO;QACzB,WAAW,SAAS,SAAS,IAAI;QACjC,WAAW,gBAAgB,SAAS;QACpC,YAAY,gBAAgB,UAAU;QACtC,eAAe,gBAAgB,aAAa;QAC5C,SAAS,gBAAgB,OAAO;QAChC,aAAa,SAAS,WAAW,IAAI,EAAE;QACvC,wBAAwB,SAAS,sBAAsB,IAAI,EAAE;QAC7D,eAAe,gBAAgB,QAAQ;IACzC;IAEA,kBAAkB;IAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,kBAAkB,MAAc,EAAE,OAAe;IACrE,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,wCAAwC;IACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;QACN,SAAS,QAAQ,IAAI;QACrB,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,mBAAmB,MAAc,EAAE,UAAoB;IAC3E,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,4CAA4C;IAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC;QACN,aAAa;QACb,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,WAAW,MAAc,EAAE,QAAsB;IACrE,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,sBAAsB;IACtB,MAAM,aAAa;QACjB,SAAS,SAAS,OAAO;QACzB,WAAW,SAAS,SAAS,IAAI;QACjC,aAAa,SAAS,WAAW,IAAI,EAAE;QACvC,wBAAwB,SAAS,sBAAsB,IAAI,EAAE;QAC7D,YAAY,IAAI,OAAO,WAAW;IACpC;IAEA,kBAAkB;IAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,WAAW,MAAc;IAC7C,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,yFAAyF;IACzF,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,6BACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,wDAAwD;IACxD,sFAAsF;IACtF,IAAI;QACF,MAAM,oBAAoB,MAAM,CAAA,GAAA,mJAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE,EAAE,QAAQ,aAAa,UAAU;QACxF,IAAI,CAAC,kBAAkB,OAAO,IAAI,kBAAkB,KAAK,EAAE;YACzD,QAAQ,KAAK,CAAC,uCAAuC,kBAAkB,KAAK;YAC5E,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,CAAC,oBAAoB,EAAE,kBAAkB,KAAK,EAAE;YACzD;QACF;IACF,EAAE,OAAO,YAAY;QACnB,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,uDAAuD;IACvD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IAEf,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF;;;IA7VsB;IAyEA;IAiEA;IAiEA;IAuEA;;AAlRA,+OAAA;AAyEA,+OAAA;AAiEA,+OAAA;AAiEA,+OAAA;AAuEA,+OAAA", "debugId": null}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/productActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { ProductsServicesRow } from \"@/types/database\";\r\n\r\n// Search business products for the current user\r\nexport async function searchBusinessProducts(query: string): Promise<{\r\n  success: boolean;\r\n  data?: ProductsServicesRow[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!query || query.trim().length < 2) {\r\n      return {\r\n        success: false,\r\n        error: \"Search query must be at least 2 characters long\",\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const {\r\n      data: { user },\r\n      error: authError,\r\n    } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Search products for the current user's business only\r\n    const { data, error } = await supabaseAdmin\r\n      .from(\"products_services\")\r\n      .select(\"*\")\r\n      .eq(\"business_id\", user.id) // Ensure user can only see their own products\r\n      .eq(\"is_available\", true)\r\n      .ilike(\"name\", `%${query.trim()}%`)\r\n      .order(\"name\", { ascending: true })\r\n      .limit(10); // Limit search results to 10 items\r\n\r\n    if (error) {\r\n      console.error(\"Error searching products:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to search products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in searchBusinessProducts:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get selected products by IDs for the current user\r\nexport async function getSelectedProducts(productIds: string[]): Promise<{\r\n  success: boolean;\r\n  data?: ProductsServicesRow[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!productIds || productIds.length === 0) {\r\n      return {\r\n        success: true,\r\n        data: [],\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const {\r\n      data: { user },\r\n      error: authError,\r\n    } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Use regular client - accessing user's own products with authentication\r\n    // Get products by IDs, but only for the current user's business\r\n    const { data, error } = await supabase\r\n      .from(\"products_services\")\r\n      .select(\"*\")\r\n      .in(\"id\", productIds)\r\n      .eq(\"business_id\", user.id) // Ensure user can only access their own products\r\n      .order(\"name\", { ascending: true });\r\n\r\n    if (error) {\r\n      console.error(\"Error getting selected products:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to get selected products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in getSelectedProducts:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;;;;AAIO,eAAe,uBAAuB,KAAa;IAKxD,IAAI;QACF,iBAAiB;QACjB,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,yBAAyB;QACzB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,0CAA0C;QAC1C,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEtC,uDAAuD;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,cAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,KAAK,EAAE,EAAE,8CAA8C;SACzE,EAAE,CAAC,gBAAgB,MACnB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC,EACjC,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK,GAChC,KAAK,CAAC,KAAK,mCAAmC;QAEjD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,QAAQ,EAAE;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAGO,eAAe,oBAAoB,UAAoB;IAK5D,IAAI;QACF,iBAAiB;QACjB,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;YAC1C,OAAO;gBACL,SAAS;gBACT,MAAM,EAAE;YACV;QACF;QAEA,yBAAyB;QACzB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE/B,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,yEAAyE;QACzE,gEAAgE;QAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,eAAe,KAAK,EAAE,EAAE,iDAAiD;SAC5E,KAAK,CAAC,QAAQ;YAAE,WAAW;QAAK;QAEnC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,QAAQ,EAAE;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IAvHsB;IA+DA;;AA/DA,+OAAA;AA+DA,+OAAA", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/upload-customer-post-media.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { getCustomerPostImagePath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface CustomerPostMediaUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload and process image for customer post\r\n * Future-proof structure: {userId}/posts/{year}/{month}/{postId}/image_0_{timestamp}.webp\r\n */\r\nexport async function uploadCustomerPostImage(\r\n  formData: FormData,\r\n  postId: string,\r\n  postCreatedAt?: string\r\n): Promise<CustomerPostMediaUploadResult> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const userId = user.id;\r\n  const imageFile = formData.get(\"imageFile\") as File | null;\r\n\r\n  if (!imageFile) {\r\n    return { success: false, error: \"No image file provided.\" };\r\n  }\r\n\r\n  // Validate file type\r\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];\r\n  if (!allowedTypes.includes(imageFile.type)) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed.\"\r\n    };\r\n  }\r\n\r\n  // Validate file size (15MB limit)\r\n  const maxSize = 15 * 1024 * 1024; // 15MB\r\n  if (imageFile.size > maxSize) {\r\n    return {\r\n      success: false,\r\n      error: \"File size exceeds 15MB limit.\"\r\n    };\r\n  }\r\n\r\n  // Validate that the post belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('customer_posts')\r\n    .select('id, customer_id')\r\n    .eq('id', postId)\r\n    .eq('customer_id', userId)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      error: \"Post not found or you don't have permission to upload images for this post.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Create scalable path structure for billions of users\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const bucketName = \"customers\";\r\n    const imagePath = getCustomerPostImagePath(userId, postId, 0, timestamp, postCreatedAt);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Customer Post Image Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL using admin client\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Error processing customer post image:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Failed to process image. Please try a different image.\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;;;;;AAYO,eAAe,wBACpB,QAAkB,EAClB,MAAc,EACd,aAAsB;IAEtB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,MAAM,SAAS,KAAK,EAAE;IACtB,MAAM,YAAY,SAAS,GAAG,CAAC;IAE/B,IAAI,CAAC,WAAW;QACd,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,qBAAqB;IACrB,MAAM,eAAe;QAAC;QAAc;QAAa;QAAa;QAAc;KAAY;IACxF,IAAI,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,GAAG;QAC1C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;IACzC,IAAI,UAAU,IAAI,GAAG,SAAS;QAC5B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,6CAA6C;IAC7C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,mBACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,QAClB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI;QACF,uDAAuD;QACvD,MAAM,YAAY,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC1D,MAAM,aAAa;QACnB,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ,QAAQ,GAAG,WAAW;QAEzE,4DAA4D;QAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,UAAU,WAAW;QAE1D,wDAAwD;QACxD,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEtC,gDAAgD;QAChD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,YACL,MAAM,CAAC,WAAW,YAAY;YAC7B,aAAa,UAAU,IAAI;YAC3B,QAAQ;QACV;QAEF,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,wBAAwB,EAAE,YAAY,OAAO,EAAE;YACzD;QACF;QAEA,wCAAwC;QACxC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,cAAc,OAAO,CAC5C,IAAI,CAAC,YACL,YAAY,CAAC;QAEhB,IAAI,CAAC,SAAS,WAAW;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,KAAK,QAAQ,SAAS;QACxB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IA7GsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/customer/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '0014cdea15b4f7fa389d50710dd262d0d658a87646'} from 'ACTIONS_MODULE0'\nexport {getCustomerAddressData as '40abe89cc2ddf077bf8b7fc05dc06a196326614d43'} from 'ACTIONS_MODULE1'\nexport {validateCustomerName as '40cdc3a5d35bdcb3d5224345c243cb1e3b7e5035ee'} from 'ACTIONS_MODULE1'\nexport {requireCompleteName as '40d422d48fae88a9b060cc8ee338fcbc74794d66e3'} from 'ACTIONS_MODULE1'\nexport {validateCustomerAddress as '40e253170fdda8e169f04eef4ce49a73190e792fd9'} from 'ACTIONS_MODULE1'\nexport {requireCompleteAddress as '40e78b100e981c3d45e4d0de5c0c1b0e3bbb48a311'} from 'ACTIONS_MODULE1'\nexport {requireCompleteProfile as '60bf355f0670bed9eb38a8dfaf1f52e4a417ae1b9e'} from 'ACTIONS_MODULE1'\nexport {fetchProductsByIds as '405dc89aa7a0e52212e7328847f0162c7a60616b08'} from 'ACTIONS_MODULE2'\nexport {updatePost as '60c0539d120bf3389a34f6a69beb2b195e8eb9664f'} from 'ACTIONS_MODULE3'\nexport {deletePost as '4084747f2f4b5dfcf217e98c0edf55dc61b90bafe7'} from 'ACTIONS_MODULE3'\nexport {searchBusinessProducts as '4075605054c51df52c1b8c952ae615d84766a3a78b'} from 'ACTIONS_MODULE4'\nexport {getSelectedProducts as '4045e58f0cd2826d0512b416e126181a3c17bd4b82'} from 'ACTIONS_MODULE4'\nexport {uploadCustomerPostImage as '707741fdfa91e5a22c2e21351f641ca64f07589208'} from 'ACTIONS_MODULE5'\n"], "names": [], "mappings": ";AAAA;AACA;AAMA;AACA;AAEA;AAEA", "debugId": null}}, {"offset": {"line": 1366, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/ModernCustomerFeedList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/feed/ModernCustomerFeedList.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/feed/ModernCustomerFeedList.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/ModernCustomerFeedList.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/feed/ModernCustomerFeedList.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/feed/ModernCustomerFeedList.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from \"@supabase/ssr\";\r\n\r\nexport function createClient() {\r\n  return createBrowserClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 1419, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/planPrioritizer.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Plan Prioritizer - Handles business plan-based post prioritization\r\n * Higher tier businesses get better visibility while maintaining fairness\r\n */\r\n\r\nexport const PLAN_PRIORITY: Record<string, number> = {\r\n  'enterprise': 5,\r\n  'pro': 4,\r\n  'growth': 3,\r\n  'basic': 2,\r\n  'free': 1\r\n};\r\n\r\nexport interface BusinessGroup {\r\n  authorId: string;\r\n  priority: number;\r\n  latestPostTime: number;\r\n  posts: UnifiedPost[];\r\n}\r\n\r\n/**\r\n * Create business priority groups based on subscription plans\r\n * Similar to how LinkedIn prioritizes premium content\r\n */\r\nexport function createBusinessPriorityGroups(businessPosts: UnifiedPost[]): BusinessGroup[] {\r\n  // Group posts by business author\r\n  const businessPostsByAuthor = new Map<string, UnifiedPost[]>();\r\n  businessPosts.forEach(post => {\r\n    if (!businessPostsByAuthor.has(post.author_id)) {\r\n      businessPostsByAuthor.set(post.author_id, []);\r\n    }\r\n    businessPostsByAuthor.get(post.author_id)!.push(post);\r\n  });\r\n\r\n  // Sort posts within each business group chronologically (latest first)\r\n  businessPostsByAuthor.forEach((posts, authorId) => {\r\n    businessPostsByAuthor.set(authorId, posts.sort((a, b) =>\r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n\r\n  // Create priority groups\r\n  return Array.from(businessPostsByAuthor.entries())\r\n    .map(([authorId, authorPosts]) => {\r\n      const latestPost = authorPosts[0];\r\n      const priority = PLAN_PRIORITY[latestPost.business_plan || 'free'] || 1;\r\n      return {\r\n        authorId,\r\n        priority,\r\n        latestPostTime: new Date(latestPost.created_at).getTime(),\r\n        posts: authorPosts\r\n      };\r\n    })\r\n    .sort((a, b) => {\r\n      // Sort by plan priority first\r\n      if (a.priority !== b.priority) {\r\n        return b.priority - a.priority; // Higher priority first\r\n      }\r\n      // If same plan, sort by latest post timestamp\r\n      return b.latestPostTime - a.latestPostTime;\r\n    });\r\n}\r\n\r\n/**\r\n * Distribute business posts with plan-based prioritization\r\n * Uses tier-based round-robin to ensure diversity within each plan level\r\n */\r\nexport function distributePrioritizedBusinessPosts(businessGroups: BusinessGroup[]): UnifiedPost[] {\r\n  const result: UnifiedPost[] = [];\r\n\r\n  // Group businesses by plan priority\r\n  const businessesByPlan = new Map<number, BusinessGroup[]>();\r\n  businessGroups.forEach(business => {\r\n    if (!businessesByPlan.has(business.priority)) {\r\n      businessesByPlan.set(business.priority, []);\r\n    }\r\n    businessesByPlan.get(business.priority)!.push(business);\r\n  });\r\n\r\n  // Sort plan priorities (highest first)\r\n  const sortedPlanPriorities = Array.from(businessesByPlan.keys()).sort((a, b) => b - a);\r\n\r\n  // Distribute posts: round-robin within each plan tier\r\n  for (const planPriority of sortedPlanPriorities) {\r\n    const businessesInPlan = businessesByPlan.get(planPriority)!;\r\n\r\n    // Create queues for round-robin distribution\r\n    const businessPostQueues = businessesInPlan.map(business => ({\r\n      ...business,\r\n      remainingPosts: [...business.posts]\r\n    }));\r\n\r\n    // Round-robin within this plan tier until all posts are distributed\r\n    while (businessPostQueues.some(queue => queue.remainingPosts.length > 0)) {\r\n      businessPostQueues.forEach(business => {\r\n        if (business.remainingPosts.length > 0) {\r\n          const post = business.remainingPosts.shift()!;\r\n          result.push(post);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Get plan display name for UI purposes\r\n */\r\nexport function getPlanDisplayName(planId: string): string {\r\n  const planNames: Record<string, string> = {\r\n    'enterprise': 'Enterprise',\r\n    'pro': 'Pro',\r\n    'growth': 'Growth',\r\n    'basic': 'Basic',\r\n    'free': 'Free'\r\n  };\r\n  \r\n  return planNames[planId] || 'Free';\r\n}\r\n\r\n/**\r\n * Check if a business has premium features based on plan\r\n */\r\nexport function hasPremiumFeatures(planId: string): boolean {\r\n  const priority = PLAN_PRIORITY[planId] || 1;\r\n  return priority >= PLAN_PRIORITY.growth; // Growth and above\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAOO,MAAM,gBAAwC;IACnD,cAAc;IACd,OAAO;IACP,UAAU;IACV,SAAS;IACT,QAAQ;AACV;AAaO,SAAS,6BAA6B,aAA4B;IACvE,iCAAiC;IACjC,MAAM,wBAAwB,IAAI;IAClC,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,CAAC,sBAAsB,GAAG,CAAC,KAAK,SAAS,GAAG;YAC9C,sBAAsB,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QAC9C;QACA,sBAAsB,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IAClD;IAEA,uEAAuE;IACvE,sBAAsB,OAAO,CAAC,CAAC,OAAO;QACpC,sBAAsB,GAAG,CAAC,UAAU,MAAM,IAAI,CAAC,CAAC,GAAG,IACjD,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,yBAAyB;IACzB,OAAO,MAAM,IAAI,CAAC,sBAAsB,OAAO,IAC5C,GAAG,CAAC,CAAC,CAAC,UAAU,YAAY;QAC3B,MAAM,aAAa,WAAW,CAAC,EAAE;QACjC,MAAM,WAAW,aAAa,CAAC,WAAW,aAAa,IAAI,OAAO,IAAI;QACtE,OAAO;YACL;YACA;YACA,gBAAgB,IAAI,KAAK,WAAW,UAAU,EAAE,OAAO;YACvD,OAAO;QACT;IACF,GACC,IAAI,CAAC,CAAC,GAAG;QACR,8BAA8B;QAC9B,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;YAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ,EAAE,wBAAwB;QAC1D;QACA,8CAA8C;QAC9C,OAAO,EAAE,cAAc,GAAG,EAAE,cAAc;IAC5C;AACJ;AAMO,SAAS,mCAAmC,cAA+B;IAChF,MAAM,SAAwB,EAAE;IAEhC,oCAAoC;IACpC,MAAM,mBAAmB,IAAI;IAC7B,eAAe,OAAO,CAAC,CAAA;QACrB,IAAI,CAAC,iBAAiB,GAAG,CAAC,SAAS,QAAQ,GAAG;YAC5C,iBAAiB,GAAG,CAAC,SAAS,QAAQ,EAAE,EAAE;QAC5C;QACA,iBAAiB,GAAG,CAAC,SAAS,QAAQ,EAAG,IAAI,CAAC;IAChD;IAEA,uCAAuC;IACvC,MAAM,uBAAuB,MAAM,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAEpF,sDAAsD;IACtD,KAAK,MAAM,gBAAgB,qBAAsB;QAC/C,MAAM,mBAAmB,iBAAiB,GAAG,CAAC;QAE9C,6CAA6C;QAC7C,MAAM,qBAAqB,iBAAiB,GAAG,CAAC,CAAA,WAAY,CAAC;gBAC3D,GAAG,QAAQ;gBACX,gBAAgB;uBAAI,SAAS,KAAK;iBAAC;YACrC,CAAC;QAED,oEAAoE;QACpE,MAAO,mBAAmB,IAAI,CAAC,CAAA,QAAS,MAAM,cAAc,CAAC,MAAM,GAAG,GAAI;YACxE,mBAAmB,OAAO,CAAC,CAAA;gBACzB,IAAI,SAAS,cAAc,CAAC,MAAM,GAAG,GAAG;oBACtC,MAAM,OAAO,SAAS,cAAc,CAAC,KAAK;oBAC1C,OAAO,IAAI,CAAC;gBACd;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAoC;QACxC,cAAc;QACd,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,WAAW,aAAa,CAAC,OAAO,IAAI;IAC1C,OAAO,YAAY,cAAc,MAAM,EAAE,mBAAmB;AAC9D", "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/diversityEngine.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Diversity Engine - Ensures no consecutive posts from the same author\r\n * Inspired by Facebook/Instagram feed algorithms that maintain user engagement\r\n * through content diversity and prevent feed monotony.\r\n */\r\n\r\nexport interface DiversityOptions {\r\n  maxConsecutiveFromSameAuthor?: number;\r\n  prioritizeRecency?: boolean;\r\n}\r\n\r\n/**\r\n * Apply diversity rules to prevent consecutive posts from the same author\r\n * Uses a sliding window approach similar to major social media platforms\r\n */\r\nexport function applyDiversityRules(\r\n  posts: UnifiedPost[], \r\n  options: DiversityOptions = {}\r\n): UnifiedPost[] {\r\n  const { maxConsecutiveFromSameAuthor = 1 } = options;\r\n  \r\n  if (posts.length <= 1) return posts;\r\n\r\n  const diversifiedPosts: UnifiedPost[] = [];\r\n  const remainingPosts = [...posts];\r\n  let lastAuthorId: string | null = null;\r\n  let consecutiveCount = 0;\r\n\r\n  while (remainingPosts.length > 0) {\r\n    let selectedIndex = -1;\r\n    \r\n    // First, try to find a post from a different author\r\n    for (let i = 0; i < remainingPosts.length; i++) {\r\n      const post = remainingPosts[i];\r\n      \r\n      if (post.author_id !== lastAuthorId) {\r\n        selectedIndex = i;\r\n        break;\r\n      }\r\n    }\r\n    \r\n    // If no different author found, or we haven't exceeded consecutive limit\r\n    if (selectedIndex === -1 && consecutiveCount < maxConsecutiveFromSameAuthor) {\r\n      selectedIndex = 0; // Take the first available post\r\n    }\r\n    \r\n    // If still no selection, force diversity by taking first different author\r\n    if (selectedIndex === -1) {\r\n      for (let i = 0; i < remainingPosts.length; i++) {\r\n        if (remainingPosts[i].author_id !== lastAuthorId) {\r\n          selectedIndex = i;\r\n          break;\r\n        }\r\n      }\r\n      // If still no different author, take first available (edge case)\r\n      if (selectedIndex === -1) selectedIndex = 0;\r\n    }\r\n\r\n    const selectedPost = remainingPosts.splice(selectedIndex, 1)[0];\r\n    diversifiedPosts.push(selectedPost);\r\n    \r\n    // Update tracking variables\r\n    if (selectedPost.author_id === lastAuthorId) {\r\n      consecutiveCount++;\r\n    } else {\r\n      consecutiveCount = 1;\r\n      lastAuthorId = selectedPost.author_id;\r\n    }\r\n  }\r\n\r\n  return diversifiedPosts;\r\n}\r\n\r\n/**\r\n * Group posts by author while maintaining chronological order within groups\r\n */\r\nexport function groupPostsByAuthor(posts: UnifiedPost[]): Map<string, UnifiedPost[]> {\r\n  const grouped = new Map<string, UnifiedPost[]>();\r\n  \r\n  posts.forEach(post => {\r\n    if (!grouped.has(post.author_id)) {\r\n      grouped.set(post.author_id, []);\r\n    }\r\n    grouped.get(post.author_id)!.push(post);\r\n  });\r\n  \r\n  // Sort posts within each group chronologically (latest first)\r\n  grouped.forEach((authorPosts, authorId) => {\r\n    grouped.set(authorId, authorPosts.sort((a, b) => \r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n  \r\n  return grouped;\r\n}\r\n\r\n/**\r\n * Round-robin distribution to ensure fair representation\r\n * Similar to how Instagram distributes stories from different accounts\r\n */\r\nexport function roundRobinDistribution(groupedPosts: Map<string, UnifiedPost[]>): UnifiedPost[] {\r\n  const result: UnifiedPost[] = [];\r\n  const queues = Array.from(groupedPosts.entries()).map(([authorId, posts]) => ({\r\n    authorId,\r\n    posts: [...posts]\r\n  }));\r\n\r\n  // Continue until all queues are empty\r\n  while (queues.some(queue => queue.posts.length > 0)) {\r\n    queues.forEach(queue => {\r\n      if (queue.posts.length > 0) {\r\n        const post = queue.posts.shift()!;\r\n        result.push(post);\r\n      }\r\n    });\r\n  }\r\n\r\n  return result;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAiBO,SAAS,oBACd,KAAoB,EACpB,UAA4B,CAAC,CAAC;IAE9B,MAAM,EAAE,+BAA+B,CAAC,EAAE,GAAG;IAE7C,IAAI,MAAM,MAAM,IAAI,GAAG,OAAO;IAE9B,MAAM,mBAAkC,EAAE;IAC1C,MAAM,iBAAiB;WAAI;KAAM;IACjC,IAAI,eAA8B;IAClC,IAAI,mBAAmB;IAEvB,MAAO,eAAe,MAAM,GAAG,EAAG;QAChC,IAAI,gBAAgB,CAAC;QAErB,oDAAoD;QACpD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,MAAM,OAAO,cAAc,CAAC,EAAE;YAE9B,IAAI,KAAK,SAAS,KAAK,cAAc;gBACnC,gBAAgB;gBAChB;YACF;QACF;QAEA,yEAAyE;QACzE,IAAI,kBAAkB,CAAC,KAAK,mBAAmB,8BAA8B;YAC3E,gBAAgB,GAAG,gCAAgC;QACrD;QAEA,0EAA0E;QAC1E,IAAI,kBAAkB,CAAC,GAAG;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;gBAC9C,IAAI,cAAc,CAAC,EAAE,CAAC,SAAS,KAAK,cAAc;oBAChD,gBAAgB;oBAChB;gBACF;YACF;YACA,iEAAiE;YACjE,IAAI,kBAAkB,CAAC,GAAG,gBAAgB;QAC5C;QAEA,MAAM,eAAe,eAAe,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE;QAC/D,iBAAiB,IAAI,CAAC;QAEtB,4BAA4B;QAC5B,IAAI,aAAa,SAAS,KAAK,cAAc;YAC3C;QACF,OAAO;YACL,mBAAmB;YACnB,eAAe,aAAa,SAAS;QACvC;IACF;IAEA,OAAO;AACT;AAKO,SAAS,mBAAmB,KAAoB;IACrD,MAAM,UAAU,IAAI;IAEpB,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,SAAS,GAAG;YAChC,QAAQ,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QAChC;QACA,QAAQ,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IACpC;IAEA,8DAA8D;IAC9D,QAAQ,OAAO,CAAC,CAAC,aAAa;QAC5B,QAAQ,GAAG,CAAC,UAAU,YAAY,IAAI,CAAC,CAAC,GAAG,IACzC,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,OAAO;AACT;AAMO,SAAS,uBAAuB,YAAwC;IAC7E,MAAM,SAAwB,EAAE;IAChC,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM,GAAK,CAAC;YAC5E;YACA,OAAO;mBAAI;aAAM;QACnB,CAAC;IAED,sCAAsC;IACtC,MAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,KAAK,CAAC,MAAM,GAAG,GAAI;QACnD,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG;gBAC1B,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK;gBAC9B,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1609, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/optimizedHybridAlgorithm.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\nimport { PLAN_PRIORITY } from './planPrioritizer';\r\nimport { applyDiversityRules } from './diversityEngine';\r\n\r\n/**\r\n * Optimized Hybrid Algorithm for Exact Post Count\r\n * \r\n * Works with exactly the fetched posts (e.g., 10 posts) without losing any content\r\n * Strategy:\r\n * 1. Separate customer and business posts\r\n * 2. Apply plan prioritization to business posts\r\n * 3. Merge customer + business posts by timestamp\r\n * 4. Apply diversity rules\r\n * 5. Return all posts (no loss)\r\n */\r\n\r\nexport interface OptimizedHybridOptions {\r\n  enableDiversity?: boolean;\r\n  maintainChronologicalFlow?: boolean;\r\n}\r\n\r\n/**\r\n * Main optimized hybrid algorithm - processes exactly the fetched posts\r\n */\r\nexport function processOptimizedHybrid(\r\n  posts: UnifiedPost[],\r\n  options: OptimizedHybridOptions = {}\r\n): UnifiedPost[] {\r\n  const {\r\n    enableDiversity = true,\r\n    maintainChronologicalFlow = true\r\n  } = options;\r\n\r\n  if (posts.length === 0) return [];\r\n\r\n  // Separate customer and business posts\r\n  const customerPosts = posts.filter(post => post.post_source === 'customer');\r\n  const businessPosts = posts.filter(post => post.post_source === 'business');\r\n\r\n  // Process customer posts (maintain chronological order)\r\n  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);\r\n\r\n  // Process business posts (apply plan prioritization)\r\n  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);\r\n\r\n  // Merge both types\r\n  const mergedPosts = mergeOptimizedPosts(\r\n    processedCustomerPosts,\r\n    processedBusinessPosts,\r\n    maintainChronologicalFlow\r\n  );\r\n\r\n  // Apply diversity rules if enabled\r\n  return enableDiversity ? applyDiversityRules(mergedPosts) : mergedPosts;\r\n}\r\n\r\n/**\r\n * Process customer posts - simple chronological sort\r\n */\r\nfunction processCustomerPostsOptimized(customerPosts: UnifiedPost[]): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return [];\r\n\r\n  // Sort chronologically (latest first)\r\n  return customerPosts.sort((a, b) => \r\n    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n  );\r\n}\r\n\r\n/**\r\n * Process business posts - apply plan prioritization ONLY to latest post per business\r\n * Other posts from same business compete purely on timestamp\r\n */\r\nfunction processBusinessPostsOptimized(businessPosts: UnifiedPost[]): UnifiedPost[] {\r\n  if (businessPosts.length === 0) return [];\r\n\r\n  // Group posts by business (author_id)\r\n  const postsByBusiness = new Map<string, UnifiedPost[]>();\r\n  businessPosts.forEach(post => {\r\n    if (!postsByBusiness.has(post.author_id)) {\r\n      postsByBusiness.set(post.author_id, []);\r\n    }\r\n    postsByBusiness.get(post.author_id)!.push(post);\r\n  });\r\n\r\n  // Sort posts within each business by timestamp (latest first)\r\n  postsByBusiness.forEach((posts, businessId) => {\r\n    postsByBusiness.set(businessId, posts.sort((a, b) =>\r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n\r\n  // Separate latest posts (get plan priority) from other posts (time-based only)\r\n  const latestPostsPerBusiness: UnifiedPost[] = [];\r\n  const otherPostsFromBusinesses: UnifiedPost[] = [];\r\n\r\n  postsByBusiness.forEach((posts, _businessId) => {\r\n    if (posts.length > 0) {\r\n      // First post is latest (already sorted)\r\n      latestPostsPerBusiness.push(posts[0]);\r\n\r\n      // Rest are other posts from same business\r\n      if (posts.length > 1) {\r\n        otherPostsFromBusinesses.push(...posts.slice(1));\r\n      }\r\n    }\r\n  });\r\n\r\n  // Sort latest posts by plan priority + timestamp\r\n  const prioritizedLatestPosts = latestPostsPerBusiness.sort((a, b) => {\r\n    const planA = a.business_plan || 'free';\r\n    const planB = b.business_plan || 'free';\r\n\r\n    const priorityA = PLAN_PRIORITY[planA] || 1;\r\n    const priorityB = PLAN_PRIORITY[planB] || 1;\r\n\r\n    // Sort by plan priority first\r\n    if (priorityA !== priorityB) {\r\n      return priorityB - priorityA; // Higher priority first\r\n    }\r\n\r\n    // If same plan, sort by timestamp (latest first)\r\n    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n  });\r\n\r\n  // Sort other posts purely by timestamp (no plan priority)\r\n  const timeBasedOtherPosts = otherPostsFromBusinesses.sort((a, b) =>\r\n    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n  );\r\n\r\n  // Return prioritized latest posts first, then time-based other posts\r\n  return [...prioritizedLatestPosts, ...timeBasedOtherPosts];\r\n}\r\n\r\n/**\r\n * Merge customer and business posts with equal treatment\r\n * No priority between customer vs business - only plan priority within business posts\r\n */\r\nfunction mergeOptimizedPosts(\r\n  customerPosts: UnifiedPost[],\r\n  businessPosts: UnifiedPost[],\r\n  maintainChronologicalFlow: boolean\r\n): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return businessPosts;\r\n  if (businessPosts.length === 0) return customerPosts;\r\n\r\n  if (maintainChronologicalFlow) {\r\n    // Merge all posts by timestamp - equal treatment\r\n    return [...customerPosts, ...businessPosts]\r\n      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());\r\n  } else {\r\n    // Business posts first (due to plan prioritization), then customer posts\r\n    return [...businessPosts, ...customerPosts];\r\n  }\r\n}\r\n\r\n/**\r\n * Alternative approach: Intelligent interleaving\r\n * Ensures both customer and business posts get representation\r\n */\r\nexport function processOptimizedHybridWithInterleaving(\r\n  posts: UnifiedPost[],\r\n  options: OptimizedHybridOptions = {}\r\n): UnifiedPost[] {\r\n  const {\r\n    enableDiversity = true,\r\n    maintainChronologicalFlow = true\r\n  } = options;\r\n\r\n  if (posts.length === 0) return [];\r\n\r\n  // Separate and process posts\r\n  const customerPosts = posts.filter(post => post.post_source === 'customer');\r\n  const businessPosts = posts.filter(post => post.post_source === 'business');\r\n\r\n  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);\r\n  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);\r\n\r\n  // Intelligent interleaving\r\n  const interleavedPosts = intelligentInterleave(\r\n    processedCustomerPosts,\r\n    processedBusinessPosts,\r\n    maintainChronologicalFlow\r\n  );\r\n\r\n  // Apply diversity rules if enabled\r\n  return enableDiversity ? applyDiversityRules(interleavedPosts) : interleavedPosts;\r\n}\r\n\r\n/**\r\n * Intelligent interleaving of customer and business posts\r\n */\r\nfunction intelligentInterleave(\r\n  customerPosts: UnifiedPost[],\r\n  businessPosts: UnifiedPost[],\r\n  respectTimestamp: boolean\r\n): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return businessPosts;\r\n  if (businessPosts.length === 0) return customerPosts;\r\n\r\n  const result: UnifiedPost[] = [];\r\n  let customerIndex = 0;\r\n  let businessIndex = 0;\r\n\r\n  // Interleave posts while respecting timestamps if enabled\r\n  while (customerIndex < customerPosts.length || businessIndex < businessPosts.length) {\r\n    const customerPost = customerPosts[customerIndex];\r\n    const businessPost = businessPosts[businessIndex];\r\n\r\n    if (!customerPost && businessPost) {\r\n      // Only business posts left\r\n      result.push(businessPost);\r\n      businessIndex++;\r\n    } else if (customerPost && !businessPost) {\r\n      // Only customer posts left\r\n      result.push(customerPost);\r\n      customerIndex++;\r\n    } else if (customerPost && businessPost) {\r\n      // Both available - decide based on timestamp or alternating pattern\r\n      if (respectTimestamp) {\r\n        const customerTime = new Date(customerPost.created_at).getTime();\r\n        const businessTime = new Date(businessPost.created_at).getTime();\r\n        \r\n        if (businessTime >= customerTime) {\r\n          result.push(businessPost);\r\n          businessIndex++;\r\n        } else {\r\n          result.push(customerPost);\r\n          customerIndex++;\r\n        }\r\n      } else {\r\n        // Alternating pattern - business posts get slight preference due to plan prioritization\r\n        if (result.length % 2 === 0) {\r\n          result.push(businessPost);\r\n          businessIndex++;\r\n        } else {\r\n          result.push(customerPost);\r\n          customerIndex++;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Get statistics for the optimized algorithm\r\n */\r\nexport function getOptimizedAlgorithmStats(\r\n  originalPosts: UnifiedPost[],\r\n  processedPosts: UnifiedPost[]\r\n): {\r\n  originalCount: number;\r\n  processedCount: number;\r\n  customerPosts: number;\r\n  businessPosts: number;\r\n  planDistribution: Record<string, number>;\r\n  postsLost: number;\r\n  efficiency: number;\r\n} {\r\n  const planDistribution: Record<string, number> = {};\r\n  \r\n  const businessPosts = processedPosts.filter(p => p.post_source === 'business');\r\n  const customerPosts = processedPosts.filter(p => p.post_source === 'customer');\r\n  \r\n  businessPosts.forEach(post => {\r\n    const plan = post.business_plan || 'free';\r\n    planDistribution[plan] = (planDistribution[plan] || 0) + 1;\r\n  });\r\n\r\n  const postsLost = originalPosts.length - processedPosts.length;\r\n  const efficiency = processedPosts.length / originalPosts.length;\r\n\r\n  return {\r\n    originalCount: originalPosts.length,\r\n    processedCount: processedPosts.length,\r\n    customerPosts: customerPosts.length,\r\n    businessPosts: businessPosts.length,\r\n    planDistribution,\r\n    postsLost,\r\n    efficiency\r\n  };\r\n}\r\n\r\n/**\r\n * Validate that no posts are lost (should always be 100% with optimized algorithm)\r\n */\r\nexport function validateOptimizedAlgorithm(\r\n  originalPosts: UnifiedPost[],\r\n  processedPosts: UnifiedPost[]\r\n): {\r\n  isValid: boolean;\r\n  issues: string[];\r\n  efficiency: number;\r\n} {\r\n  const issues: string[] = [];\r\n  \r\n  if (originalPosts.length !== processedPosts.length) {\r\n    issues.push(`Post count mismatch: ${originalPosts.length} → ${processedPosts.length}`);\r\n  }\r\n\r\n  const originalIds = new Set(originalPosts.map(p => p.id));\r\n  const processedIds = new Set(processedPosts.map(p => p.id));\r\n  \r\n  const lostPosts: string[] = [];\r\n  originalIds.forEach(id => {\r\n    if (!processedIds.has(id)) {\r\n      lostPosts.push(id);\r\n    }\r\n  });\r\n\r\n  if (lostPosts.length > 0) {\r\n    issues.push(`Lost posts: ${lostPosts.join(', ')}`);\r\n  }\r\n\r\n  const efficiency = processedPosts.length / originalPosts.length;\r\n\r\n  return {\r\n    isValid: issues.length === 0,\r\n    issues,\r\n    efficiency\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAsBO,SAAS,uBACd,KAAoB,EACpB,UAAkC,CAAC,CAAC;IAEpC,MAAM,EACJ,kBAAkB,IAAI,EACtB,4BAA4B,IAAI,EACjC,GAAG;IAEJ,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;IAEjC,uCAAuC;IACvC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAChE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAEhE,wDAAwD;IACxD,MAAM,yBAAyB,8BAA8B;IAE7D,qDAAqD;IACrD,MAAM,yBAAyB,8BAA8B;IAE7D,mBAAmB;IACnB,MAAM,cAAc,oBAClB,wBACA,wBACA;IAGF,mCAAmC;IACnC,OAAO,kBAAkB,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;AAC9D;AAEA;;CAEC,GACD,SAAS,8BAA8B,aAA4B;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;IAEzC,sCAAsC;IACtC,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAC5B,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;AAErE;AAEA;;;CAGC,GACD,SAAS,8BAA8B,aAA4B;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;IAEzC,sCAAsC;IACtC,MAAM,kBAAkB,IAAI;IAC5B,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,SAAS,GAAG;YACxC,gBAAgB,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QACxC;QACA,gBAAgB,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IAC5C;IAEA,8DAA8D;IAC9D,gBAAgB,OAAO,CAAC,CAAC,OAAO;QAC9B,gBAAgB,GAAG,CAAC,YAAY,MAAM,IAAI,CAAC,CAAC,GAAG,IAC7C,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,+EAA+E;IAC/E,MAAM,yBAAwC,EAAE;IAChD,MAAM,2BAA0C,EAAE;IAElD,gBAAgB,OAAO,CAAC,CAAC,OAAO;QAC9B,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,wCAAwC;YACxC,uBAAuB,IAAI,CAAC,KAAK,CAAC,EAAE;YAEpC,0CAA0C;YAC1C,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,yBAAyB,IAAI,IAAI,MAAM,KAAK,CAAC;YAC/C;QACF;IACF;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,uBAAuB,IAAI,CAAC,CAAC,GAAG;QAC7D,MAAM,QAAQ,EAAE,aAAa,IAAI;QACjC,MAAM,QAAQ,EAAE,aAAa,IAAI;QAEjC,MAAM,YAAY,uIAAA,CAAA,gBAAa,CAAC,MAAM,IAAI;QAC1C,MAAM,YAAY,uIAAA,CAAA,gBAAa,CAAC,MAAM,IAAI;QAE1C,8BAA8B;QAC9B,IAAI,cAAc,WAAW;YAC3B,OAAO,YAAY,WAAW,wBAAwB;QACxD;QAEA,iDAAiD;QACjD,OAAO,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAC1E;IAEA,0DAA0D;IAC1D,MAAM,sBAAsB,yBAAyB,IAAI,CAAC,CAAC,GAAG,IAC5D,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAGnE,qEAAqE;IACrE,OAAO;WAAI;WAA2B;KAAoB;AAC5D;AAEA;;;CAGC,GACD,SAAS,oBACP,aAA4B,EAC5B,aAA4B,EAC5B,yBAAkC;IAElC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,IAAI,2BAA2B;QAC7B,iDAAiD;QACjD,OAAO;eAAI;eAAkB;SAAc,CACxC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IACrF,OAAO;QACL,yEAAyE;QACzE,OAAO;eAAI;eAAkB;SAAc;IAC7C;AACF;AAMO,SAAS,uCACd,KAAoB,EACpB,UAAkC,CAAC,CAAC;IAEpC,MAAM,EACJ,kBAAkB,IAAI,EACtB,4BAA4B,IAAI,EACjC,GAAG;IAEJ,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;IAEjC,6BAA6B;IAC7B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAChE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAEhE,MAAM,yBAAyB,8BAA8B;IAC7D,MAAM,yBAAyB,8BAA8B;IAE7D,2BAA2B;IAC3B,MAAM,mBAAmB,sBACvB,wBACA,wBACA;IAGF,mCAAmC;IACnC,OAAO,kBAAkB,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,oBAAoB;AACnE;AAEA;;CAEC,GACD,SAAS,sBACP,aAA4B,EAC5B,aAA4B,EAC5B,gBAAyB;IAEzB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,MAAM,SAAwB,EAAE;IAChC,IAAI,gBAAgB;IACpB,IAAI,gBAAgB;IAEpB,0DAA0D;IAC1D,MAAO,gBAAgB,cAAc,MAAM,IAAI,gBAAgB,cAAc,MAAM,CAAE;QACnF,MAAM,eAAe,aAAa,CAAC,cAAc;QACjD,MAAM,eAAe,aAAa,CAAC,cAAc;QAEjD,IAAI,CAAC,gBAAgB,cAAc;YACjC,2BAA2B;YAC3B,OAAO,IAAI,CAAC;YACZ;QACF,OAAO,IAAI,gBAAgB,CAAC,cAAc;YACxC,2BAA2B;YAC3B,OAAO,IAAI,CAAC;YACZ;QACF,OAAO,IAAI,gBAAgB,cAAc;YACvC,oEAAoE;YACpE,IAAI,kBAAkB;gBACpB,MAAM,eAAe,IAAI,KAAK,aAAa,UAAU,EAAE,OAAO;gBAC9D,MAAM,eAAe,IAAI,KAAK,aAAa,UAAU,EAAE,OAAO;gBAE9D,IAAI,gBAAgB,cAAc;oBAChC,OAAO,IAAI,CAAC;oBACZ;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF,OAAO;gBACL,wFAAwF;gBACxF,IAAI,OAAO,MAAM,GAAG,MAAM,GAAG;oBAC3B,OAAO,IAAI,CAAC;oBACZ;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,2BACd,aAA4B,EAC5B,cAA6B;IAU7B,MAAM,mBAA2C,CAAC;IAElD,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK;IACnE,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK;IAEnE,cAAc,OAAO,CAAC,CAAA;QACpB,MAAM,OAAO,KAAK,aAAa,IAAI;QACnC,gBAAgB,CAAC,KAAK,GAAG,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI;IAC3D;IAEA,MAAM,YAAY,cAAc,MAAM,GAAG,eAAe,MAAM;IAC9D,MAAM,aAAa,eAAe,MAAM,GAAG,cAAc,MAAM;IAE/D,OAAO;QACL,eAAe,cAAc,MAAM;QACnC,gBAAgB,eAAe,MAAM;QACrC,eAAe,cAAc,MAAM;QACnC,eAAe,cAAc,MAAM;QACnC;QACA;QACA;IACF;AACF;AAKO,SAAS,2BACd,aAA4B,EAC5B,cAA6B;IAM7B,MAAM,SAAmB,EAAE;IAE3B,IAAI,cAAc,MAAM,KAAK,eAAe,MAAM,EAAE;QAClD,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,cAAc,MAAM,CAAC,GAAG,EAAE,eAAe,MAAM,EAAE;IACvF;IAEA,MAAM,cAAc,IAAI,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IACvD,MAAM,eAAe,IAAI,IAAI,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAEzD,MAAM,YAAsB,EAAE;IAC9B,YAAY,OAAO,CAAC,CAAA;QAClB,IAAI,CAAC,aAAa,GAAG,CAAC,KAAK;YACzB,UAAU,IAAI,CAAC;QACjB;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,OAAO;IACnD;IAEA,MAAM,aAAa,eAAe,MAAM,GAAG,cAAc,MAAM;IAE/D,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/postCreationHandler.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Post Creation Handler - Manages immediate post visibility after creation\r\n * \r\n * Behavior:\r\n * 1. When user creates a post -> Show at top immediately (instant feedback)\r\n * 2. When user refreshes -> Apply normal algorithm (proper positioning)\r\n * \r\n * This provides excellent UX while maintaining algorithmic integrity\r\n */\r\n\r\nexport interface PostCreationState {\r\n  justCreatedPostId?: string;\r\n  sessionId?: string;\r\n  createdAt?: string;\r\n}\r\n\r\nexport interface FeedWithCreationState {\r\n  posts: UnifiedPost[];\r\n  hasJustCreatedPost: boolean;\r\n  justCreatedPost?: UnifiedPost;\r\n}\r\n\r\n/**\r\n * Handle feed display when user just created a post\r\n * Shows new post at top for immediate feedback\r\n */\r\nexport function handlePostCreationFeed(\r\n  algorithmicPosts: UnifiedPost[],\r\n  creationState: PostCreationState\r\n): FeedWithCreationState {\r\n  \r\n  if (!creationState.justCreatedPostId) {\r\n    // No recent post creation, return normal algorithmic feed\r\n    return {\r\n      posts: algorithmicPosts,\r\n      hasJustCreatedPost: false\r\n    };\r\n  }\r\n\r\n  // Find the just-created post in the algorithmic results\r\n  const justCreatedPost = algorithmicPosts.find(\r\n    post => post.id === creationState.justCreatedPostId\r\n  );\r\n\r\n  if (!justCreatedPost) {\r\n    // Post not found in current page, return normal feed\r\n    // (Post might be on a different page due to algorithm)\r\n    return {\r\n      posts: algorithmicPosts,\r\n      hasJustCreatedPost: false\r\n    };\r\n  }\r\n\r\n  // Remove the post from its algorithmic position\r\n  const otherPosts = algorithmicPosts.filter(\r\n    post => post.id !== creationState.justCreatedPostId\r\n  );\r\n\r\n  // Show just-created post at the top\r\n  return {\r\n    posts: [justCreatedPost, ...otherPosts],\r\n    hasJustCreatedPost: true,\r\n    justCreatedPost\r\n  };\r\n}\r\n\r\n/**\r\n * Create post creation state after successful post creation\r\n */\r\nexport function createPostCreationState(\r\n  postId: string,\r\n  sessionId?: string\r\n): PostCreationState {\r\n  return {\r\n    justCreatedPostId: postId,\r\n    sessionId: sessionId || generateSessionId(),\r\n    createdAt: new Date().toISOString()\r\n  };\r\n}\r\n\r\n/**\r\n * Check if post creation state is still valid (within session)\r\n */\r\nexport function isPostCreationStateValid(\r\n  creationState: PostCreationState,\r\n  currentSessionId?: string\r\n): boolean {\r\n  if (!creationState.justCreatedPostId) return false;\r\n  \r\n  // Check if it's the same session\r\n  if (creationState.sessionId && currentSessionId) {\r\n    return creationState.sessionId === currentSessionId;\r\n  }\r\n\r\n  // Check if creation was recent (within last 5 minutes as fallback)\r\n  if (creationState.createdAt) {\r\n    const createdTime = new Date(creationState.createdAt).getTime();\r\n    const now = new Date().getTime();\r\n    const fiveMinutes = 5 * 60 * 1000;\r\n    \r\n    return (now - createdTime) < fiveMinutes;\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\n/**\r\n * Clear post creation state (call on refresh or navigation)\r\n */\r\nexport function clearPostCreationState(): PostCreationState {\r\n  return {};\r\n}\r\n\r\n/**\r\n * Generate a simple session ID for tracking\r\n */\r\nfunction generateSessionId(): string {\r\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n}\r\n\r\n/**\r\n * Enhanced feed response that includes creation state information\r\n */\r\nexport interface EnhancedFeedResponse {\r\n  success: boolean;\r\n  message: string;\r\n  error?: string;\r\n  data?: {\r\n    items: UnifiedPost[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    hasJustCreatedPost: boolean;\r\n    justCreatedPost?: UnifiedPost;\r\n    creationState?: PostCreationState;\r\n  };\r\n}\r\n\r\n/**\r\n * Process feed with post creation handling\r\n */\r\nexport function processFeedWithCreationHandling(\r\n  algorithmicPosts: UnifiedPost[],\r\n  totalCount: number,\r\n  hasMore: boolean,\r\n  creationState?: PostCreationState\r\n): EnhancedFeedResponse {\r\n\r\n  if (!creationState || !creationState.justCreatedPostId) {\r\n    // No post creation state, return normal feed\r\n    return {\r\n      success: true,\r\n      message: 'Posts fetched successfully',\r\n      data: {\r\n        items: algorithmicPosts,\r\n        totalCount,\r\n        hasMore,\r\n        hasJustCreatedPost: false\r\n      }\r\n    };\r\n  }\r\n\r\n  // Handle post creation display\r\n  const feedWithCreation = handlePostCreationFeed(algorithmicPosts, creationState);\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Posts fetched successfully',\r\n    data: {\r\n      items: feedWithCreation.posts,\r\n      totalCount,\r\n      hasMore,\r\n      hasJustCreatedPost: feedWithCreation.hasJustCreatedPost,\r\n      justCreatedPost: feedWithCreation.justCreatedPost,\r\n      creationState\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Client-side helper to manage post creation state in localStorage/sessionStorage\r\n */\r\nexport const PostCreationStateManager = {\r\n  \r\n  /**\r\n   * Save post creation state to session storage\r\n   */\r\n  save(state: PostCreationState): void {\r\n    if (typeof window !== 'undefined') {\r\n      sessionStorage.setItem('post_creation_state', JSON.stringify(state));\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Load post creation state from session storage\r\n   */\r\n  load(): PostCreationState {\r\n    if (typeof window !== 'undefined') {\r\n      const stored = sessionStorage.getItem('post_creation_state');\r\n      if (stored) {\r\n        try {\r\n          return JSON.parse(stored);\r\n        } catch (e) {\r\n          console.warn('Failed to parse post creation state:', e);\r\n        }\r\n      }\r\n    }\r\n    return {};\r\n  },\r\n\r\n  /**\r\n   * Clear post creation state from session storage\r\n   */\r\n  clear(): void {\r\n    if (typeof window !== 'undefined') {\r\n      sessionStorage.removeItem('post_creation_state');\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Check if current state is valid and clear if not\r\n   */\r\n  validateAndClean(): PostCreationState {\r\n    const state = this.load();\r\n    const currentSessionId = this.getCurrentSessionId();\r\n    \r\n    if (!isPostCreationStateValid(state, currentSessionId)) {\r\n      this.clear();\r\n      return {};\r\n    }\r\n    \r\n    return state;\r\n  },\r\n\r\n  /**\r\n   * Get or create current session ID\r\n   */\r\n  getCurrentSessionId(): string {\r\n    if (typeof window !== 'undefined') {\r\n      let sessionId = sessionStorage.getItem('current_session_id');\r\n      if (!sessionId) {\r\n        sessionId = generateSessionId();\r\n        sessionStorage.setItem('current_session_id', sessionId);\r\n      }\r\n      return sessionId;\r\n    }\r\n    return generateSessionId();\r\n  }\r\n};\r\n\r\n/**\r\n * Hook-like function for React components to manage post creation state\r\n */\r\nexport function usePostCreationState() {\r\n  const load = () => PostCreationStateManager.validateAndClean();\r\n  const save = (state: PostCreationState) => PostCreationStateManager.save(state);\r\n  const clear = () => PostCreationStateManager.clear();\r\n  \r\n  return { load, save, clear };\r\n}\r\n\r\n/**\r\n * Utility to mark a post as just created (call after successful post creation)\r\n */\r\nexport function markPostAsJustCreated(postId: string): void {\r\n  const state = createPostCreationState(\r\n    postId, \r\n    PostCreationStateManager.getCurrentSessionId()\r\n  );\r\n  PostCreationStateManager.save(state);\r\n}\r\n\r\n/**\r\n * Utility to check if we should show the \"just posted\" indicator\r\n */\r\nexport function shouldShowJustPostedIndicator(\r\n  post: UnifiedPost, \r\n  creationState?: PostCreationState\r\n): boolean {\r\n  if (!creationState || !creationState.justCreatedPostId) return false;\r\n  return post.id === creationState.justCreatedPostId;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AA4BO,SAAS,uBACd,gBAA+B,EAC/B,aAAgC;IAGhC,IAAI,CAAC,cAAc,iBAAiB,EAAE;QACpC,0DAA0D;QAC1D,OAAO;YACL,OAAO;YACP,oBAAoB;QACtB;IACF;IAEA,wDAAwD;IACxD,MAAM,kBAAkB,iBAAiB,IAAI,CAC3C,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,iBAAiB;IAGrD,IAAI,CAAC,iBAAiB;QACpB,qDAAqD;QACrD,uDAAuD;QACvD,OAAO;YACL,OAAO;YACP,oBAAoB;QACtB;IACF;IAEA,gDAAgD;IAChD,MAAM,aAAa,iBAAiB,MAAM,CACxC,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,iBAAiB;IAGrD,oCAAoC;IACpC,OAAO;QACL,OAAO;YAAC;eAAoB;SAAW;QACvC,oBAAoB;QACpB;IACF;AACF;AAKO,SAAS,wBACd,MAAc,EACd,SAAkB;IAElB,OAAO;QACL,mBAAmB;QACnB,WAAW,aAAa;QACxB,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAKO,SAAS,yBACd,aAAgC,EAChC,gBAAyB;IAEzB,IAAI,CAAC,cAAc,iBAAiB,EAAE,OAAO;IAE7C,iCAAiC;IACjC,IAAI,cAAc,SAAS,IAAI,kBAAkB;QAC/C,OAAO,cAAc,SAAS,KAAK;IACrC;IAEA,mEAAmE;IACnE,IAAI,cAAc,SAAS,EAAE;QAC3B,MAAM,cAAc,IAAI,KAAK,cAAc,SAAS,EAAE,OAAO;QAC7D,MAAM,MAAM,IAAI,OAAO,OAAO;QAC9B,MAAM,cAAc,IAAI,KAAK;QAE7B,OAAO,AAAC,MAAM,cAAe;IAC/B;IAEA,OAAO;AACT;AAKO,SAAS;IACd,OAAO,CAAC;AACV;AAEA;;CAEC,GACD,SAAS;IACP,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAsBO,SAAS,gCACd,gBAA+B,EAC/B,UAAkB,EAClB,OAAgB,EAChB,aAAiC;IAGjC,IAAI,CAAC,iBAAiB,CAAC,cAAc,iBAAiB,EAAE;QACtD,6CAA6C;QAC7C,OAAO;YACL,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP;gBACA;gBACA,oBAAoB;YACtB;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,uBAAuB,kBAAkB;IAElE,OAAO;QACL,SAAS;QACT,SAAS;QACT,MAAM;YACJ,OAAO,iBAAiB,KAAK;YAC7B;YACA;YACA,oBAAoB,iBAAiB,kBAAkB;YACvD,iBAAiB,iBAAiB,eAAe;YACjD;QACF;IACF;AACF;AAKO,MAAM,2BAA2B;IAEtC;;GAEC,GACD,MAAK,KAAwB;QAC3B,uCAAmC;;QAEnC;IACF;IAEA;;GAEC,GACD;QACE,uCAAmC;;QASnC;QACA,OAAO,CAAC;IACV;IAEA;;GAEC,GACD;QACE,uCAAmC;;QAEnC;IACF;IAEA;;GAEC,GACD;QACE,MAAM,QAAQ,IAAI,CAAC,IAAI;QACvB,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;QAEjD,IAAI,CAAC,yBAAyB,OAAO,mBAAmB;YACtD,IAAI,CAAC,KAAK;YACV,OAAO,CAAC;QACV;QAEA,OAAO;IACT;IAEA;;GAEC,GACD;QACE,uCAAmC;;QAOnC;QACA,OAAO;IACT;AACF;AAKO,SAAS;IACd,MAAM,OAAO,IAAM,yBAAyB,gBAAgB;IAC5D,MAAM,OAAO,CAAC,QAA6B,yBAAyB,IAAI,CAAC;IACzE,MAAM,QAAQ,IAAM,yBAAyB,KAAK;IAElD,OAAO;QAAE;QAAM;QAAM;IAAM;AAC7B;AAKO,SAAS,sBAAsB,MAAc;IAClD,MAAM,QAAQ,wBACZ,QACA,yBAAyB,mBAAmB;IAE9C,yBAAyB,IAAI,CAAC;AAChC;AAKO,SAAS,8BACd,IAAiB,EACjB,aAAiC;IAEjC,IAAI,CAAC,iBAAiB,CAAC,cAAc,iBAAiB,EAAE,OAAO;IAC/D,OAAO,KAAK,EAAE,KAAK,cAAc,iBAAiB;AACpD", "debugId": null}}, {"offset": {"line": 1987, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/unifiedFeed.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/client';\r\nimport { FeedQueryParams } from '@/lib/types/posts';\r\nimport { processOptimizedHybrid } from '@/lib/utils/feed/optimizedHybridAlgorithm';\r\nimport {\r\n  processFeedWithCreationHandling,\r\n  PostCreationState,\r\n  EnhancedFeedResponse\r\n} from '@/lib/utils/feed/postCreationHandler';\r\n\r\nexport interface UnifiedPost {\r\n  id: string;\r\n  post_source: 'business' | 'customer';\r\n  author_id: string;\r\n  content: string;\r\n  image_url: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  city_slug: string | null;\r\n  state_slug: string | null;\r\n  locality_slug: string | null;\r\n  pincode: string | null;\r\n  product_ids: string[];\r\n  mentioned_business_ids: string[];\r\n  author_name: string | null;\r\n  author_avatar: string | null;\r\n  business_slug: string | null; // Business slug for business posts, null for customer posts\r\n  phone: string | null; // Phone number for business posts, null for customer posts\r\n  whatsapp_number: string | null; // WhatsApp number for business posts, null for customer posts\r\n  business_plan: string | null; // Plan for business posts, null for customer posts\r\n}\r\n\r\nexport interface UnifiedFeedResponse {\r\n  success: boolean;\r\n  message: string;\r\n  error?: string;\r\n  data?: {\r\n    items: UnifiedPost[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Get unified feed posts (business + customer posts) with proper pagination\r\n * Supports post creation state for immediate feedback when user creates a post\r\n */\r\nexport async function getUnifiedFeedPosts(\r\n  params: FeedQueryParams,\r\n  creationState?: PostCreationState\r\n): Promise<EnhancedFeedResponse> {\r\n  const supabase = createClient();\r\n  const {\r\n    filter = 'smart',\r\n    page = 1,\r\n    limit = 10,\r\n    city_slug,\r\n    state_slug,\r\n    locality_slug,\r\n    pincode\r\n  } = params;\r\n\r\n  try {\r\n    // Get current user for smart and subscribed filters\r\n    const { data: { user } } = await supabase.auth.getUser();\r\n\r\n    // Build base query\r\n    let query = supabase\r\n      .from('unified_posts')\r\n      .select('*', { count: 'exact' });\r\n\r\n    // Apply filters based on filter type\r\n    switch (filter) {\r\n      case 'smart':\r\n        if (user) {\r\n          // Get user's subscribed businesses for smart feed\r\n          const { data: subscriptions } = await supabase\r\n            .from('subscriptions')\r\n            .select('business_profile_id')\r\n            .eq('user_id', user.id);\r\n\r\n          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];\r\n\r\n          // Try to get user's location from both customer and business profiles\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase\r\n              .from('customer_profiles')\r\n              .select('city_slug, state_slug, locality_slug, pincode')\r\n              .eq('id', user.id)\r\n              .single(),\r\n            supabase\r\n              .from('business_profiles')\r\n              .select('city_slug, state_slug, locality_slug, pincode')\r\n              .eq('id', user.id)\r\n              .single()\r\n          ]);\r\n\r\n          // Use whichever profile exists\r\n          const userProfile = customerProfile.data || businessProfile.data;\r\n\r\n          // Build smart feed conditions\r\n          const conditions = [];\r\n\r\n          // Subscribed businesses\r\n          if (subscribedBusinessIds.length > 0) {\r\n            conditions.push(`and(post_source.eq.business,author_id.in.(${subscribedBusinessIds.join(',')}))`);\r\n          }\r\n\r\n          // User's own posts (check both customer and business posts)\r\n          conditions.push(`and(post_source.eq.customer,author_id.eq.${user.id})`);\r\n          conditions.push(`and(post_source.eq.business,author_id.eq.${user.id})`);\r\n\r\n          // Local posts based on user location\r\n          if (userProfile?.locality_slug) {\r\n            conditions.push(`locality_slug.eq.${userProfile.locality_slug}`);\r\n          }\r\n          if (userProfile?.pincode) {\r\n            conditions.push(`pincode.eq.${userProfile.pincode}`);\r\n          }\r\n          if (userProfile?.city_slug) {\r\n            conditions.push(`city_slug.eq.${userProfile.city_slug}`);\r\n          }\r\n\r\n          if (conditions.length > 0) {\r\n            query = query.or(conditions.join(','));\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'subscribed':\r\n        if (user) {\r\n          const { data: subscriptions } = await supabase\r\n            .from('subscriptions')\r\n            .select('business_profile_id')\r\n            .eq('user_id', user.id);\r\n\r\n          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];\r\n\r\n          if (subscribedBusinessIds.length > 0) {\r\n            query = query\r\n              .eq('post_source', 'business')\r\n              .in('author_id', subscribedBusinessIds);\r\n          } else {\r\n            // No subscriptions, return empty result\r\n            return {\r\n              success: true,\r\n              message: 'No subscribed businesses found',\r\n              data: {\r\n                items: [],\r\n                totalCount: 0,\r\n                hasMore: false,\r\n                hasJustCreatedPost: false\r\n              }\r\n            };\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'locality':\r\n        if (locality_slug) {\r\n          query = query.eq('locality_slug', locality_slug);\r\n        } else if (user) {\r\n          // If no locality_slug provided, get user's locality from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('locality_slug').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('locality_slug').eq('id', user.id).single()\r\n          ]);\r\n          const userLocality = customerProfile.data?.locality_slug || businessProfile.data?.locality_slug;\r\n          if (userLocality) {\r\n            query = query.eq('locality_slug', userLocality);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'pincode':\r\n        if (pincode) {\r\n          query = query.eq('pincode', pincode);\r\n        } else if (user) {\r\n          // If no pincode provided, get user's pincode from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('pincode').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('pincode').eq('id', user.id).single()\r\n          ]);\r\n          const userPincode = customerProfile.data?.pincode || businessProfile.data?.pincode;\r\n          if (userPincode) {\r\n            query = query.eq('pincode', userPincode);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'city':\r\n        if (city_slug) {\r\n          query = query.eq('city_slug', city_slug);\r\n        } else if (user) {\r\n          // If no city_slug provided, get user's city from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('city_slug').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('city_slug').eq('id', user.id).single()\r\n          ]);\r\n          const userCity = customerProfile.data?.city_slug || businessProfile.data?.city_slug;\r\n          if (userCity) {\r\n            query = query.eq('city_slug', userCity);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'state':\r\n        if (state_slug) {\r\n          query = query.eq('state_slug', state_slug);\r\n        } else if (user) {\r\n          // If no state_slug provided, get user's state from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('state_slug').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('state_slug').eq('id', user.id).single()\r\n          ]);\r\n          const userState = customerProfile.data?.state_slug || businessProfile.data?.state_slug;\r\n          if (userState) {\r\n            query = query.eq('state_slug', userState);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'all':\r\n        // No additional filters for 'all'\r\n        break;\r\n    }\r\n\r\n    // Fetch exactly the target number of posts to prevent post loss\r\n    // Algorithm will arrange these posts optimally without losing any content\r\n    const from = (page - 1) * limit; // Standard pagination\r\n    const to = from + limit - 1;\r\n\r\n    // Execute query with chronological ordering (prioritization applied client-side)\r\n    const { data, error, count } = await query\r\n      .order('created_at', { ascending: false })\r\n      .range(from, to);\r\n\r\n    if (error) {\r\n      console.error('Error fetching unified feed posts:', error);\r\n      return {\r\n        success: false,\r\n        message: 'Failed to fetch posts',\r\n        error: error.message\r\n      };\r\n    }\r\n\r\n    // Apply optimized hybrid algorithm to ALL feed types\r\n    // Processes exactly the fetched posts without losing any content\r\n    // Business posts get plan prioritization, customer posts maintain chronological order\r\n    // Works with location filters (locality, pincode, city, state, all)\r\n    const prioritizedData = data ? processOptimizedHybrid(data, {\r\n      enableDiversity: true,\r\n      maintainChronologicalFlow: true\r\n    }) : [];\r\n\r\n    const totalCount = count || 0;\r\n    // Standard pagination logic - no posts lost\r\n    const hasMore = prioritizedData.length === limit && (from + limit) < totalCount;\r\n\r\n    // Handle post creation state for immediate feedback\r\n    return processFeedWithCreationHandling(\r\n      prioritizedData,\r\n      totalCount,\r\n      hasMore,\r\n      creationState\r\n    );\r\n\r\n  } catch (error) {\r\n    console.error('Unexpected error in getUnifiedFeedPosts:', error);\r\n    return {\r\n      success: false,\r\n      message: 'An unexpected error occurred',\r\n      error: error instanceof Error ? error.message : 'Unknown error'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get unified feed posts with author information populated\r\n * Author information is now included directly in the unified_posts view\r\n */\r\nexport async function getUnifiedFeedPostsWithAuthors(\r\n  params: FeedQueryParams,\r\n  creationState?: PostCreationState\r\n): Promise<EnhancedFeedResponse> {\r\n  // Since author information is now included in the unified_posts view,\r\n  // we can just return the result from getUnifiedFeedPosts directly\r\n  return await getUnifiedFeedPosts(params, creationState);\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;AA2CO,eAAe,oBACpB,MAAuB,EACvB,aAAiC;IAEjC,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EACJ,SAAS,OAAO,EAChB,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,SAAS,EACT,UAAU,EACV,aAAa,EACb,OAAO,EACR,GAAG;IAEJ,IAAI;QACF,oDAAoD;QACpD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEtD,mBAAmB;QACnB,IAAI,QAAQ,SACT,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ;QAEhC,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,IAAI,MAAM;oBACR,kDAAkD;oBAClD,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,iBACL,MAAM,CAAC,uBACP,EAAE,CAAC,WAAW,KAAK,EAAE;oBAExB,MAAM,wBAAwB,eAAe,IAAI,CAAA,IAAK,EAAE,mBAAmB,KAAK,EAAE;oBAElF,sEAAsE;oBACtE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,iDACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBACT,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,iDACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;qBACV;oBAED,+BAA+B;oBAC/B,MAAM,cAAc,gBAAgB,IAAI,IAAI,gBAAgB,IAAI;oBAEhE,8BAA8B;oBAC9B,MAAM,aAAa,EAAE;oBAErB,wBAAwB;oBACxB,IAAI,sBAAsB,MAAM,GAAG,GAAG;wBACpC,WAAW,IAAI,CAAC,CAAC,0CAA0C,EAAE,sBAAsB,IAAI,CAAC,KAAK,EAAE,CAAC;oBAClG;oBAEA,4DAA4D;oBAC5D,WAAW,IAAI,CAAC,CAAC,yCAAyC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBACtE,WAAW,IAAI,CAAC,CAAC,yCAAyC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBAEtE,qCAAqC;oBACrC,IAAI,aAAa,eAAe;wBAC9B,WAAW,IAAI,CAAC,CAAC,iBAAiB,EAAE,YAAY,aAAa,EAAE;oBACjE;oBACA,IAAI,aAAa,SAAS;wBACxB,WAAW,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY,OAAO,EAAE;oBACrD;oBACA,IAAI,aAAa,WAAW;wBAC1B,WAAW,IAAI,CAAC,CAAC,aAAa,EAAE,YAAY,SAAS,EAAE;oBACzD;oBAEA,IAAI,WAAW,MAAM,GAAG,GAAG;wBACzB,QAAQ,MAAM,EAAE,CAAC,WAAW,IAAI,CAAC;oBACnC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,MAAM;oBACR,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,iBACL,MAAM,CAAC,uBACP,EAAE,CAAC,WAAW,KAAK,EAAE;oBAExB,MAAM,wBAAwB,eAAe,IAAI,CAAA,IAAK,EAAE,mBAAmB,KAAK,EAAE;oBAElF,IAAI,sBAAsB,MAAM,GAAG,GAAG;wBACpC,QAAQ,MACL,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,aAAa;oBACrB,OAAO;wBACL,wCAAwC;wBACxC,OAAO;4BACL,SAAS;4BACT,SAAS;4BACT,MAAM;gCACJ,OAAO,EAAE;gCACT,YAAY;gCACZ,SAAS;gCACT,oBAAoB;4BACtB;wBACF;oBACF;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,eAAe;oBACjB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;gBACpC,OAAO,IAAI,MAAM;oBACf,uEAAuE;oBACvE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBACnF,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBACpF;oBACD,MAAM,eAAe,gBAAgB,IAAI,EAAE,iBAAiB,gBAAgB,IAAI,EAAE;oBAClF,IAAI,cAAc;wBAChB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;oBACpC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS;oBACX,QAAQ,MAAM,EAAE,CAAC,WAAW;gBAC9B,OAAO,IAAI,MAAM;oBACf,gEAAgE;oBAChE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,WAAW,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBAC7E,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,WAAW,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBAC9E;oBACD,MAAM,cAAc,gBAAgB,IAAI,EAAE,WAAW,gBAAgB,IAAI,EAAE;oBAC3E,IAAI,aAAa;wBACf,QAAQ,MAAM,EAAE,CAAC,WAAW;oBAC9B;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,WAAW;oBACb,QAAQ,MAAM,EAAE,CAAC,aAAa;gBAChC,OAAO,IAAI,MAAM;oBACf,+DAA+D;oBAC/D,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBAC/E,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBAChF;oBACD,MAAM,WAAW,gBAAgB,IAAI,EAAE,aAAa,gBAAgB,IAAI,EAAE;oBAC1E,IAAI,UAAU;wBACZ,QAAQ,MAAM,EAAE,CAAC,aAAa;oBAChC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,YAAY;oBACd,QAAQ,MAAM,EAAE,CAAC,cAAc;gBACjC,OAAO,IAAI,MAAM;oBACf,iEAAiE;oBACjE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBAChF,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBACjF;oBACD,MAAM,YAAY,gBAAgB,IAAI,EAAE,cAAc,gBAAgB,IAAI,EAAE;oBAC5E,IAAI,WAAW;wBACb,QAAQ,MAAM,EAAE,CAAC,cAAc;oBACjC;gBACF;gBACA;YAEF,KAAK;gBAEH;QACJ;QAEA,gEAAgE;QAChE,0EAA0E;QAC1E,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,sBAAsB;QACvD,MAAM,KAAK,OAAO,QAAQ;QAE1B,iFAAiF;QACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,MAClC,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,MAAM;QAEf,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,MAAM,OAAO;YACtB;QACF;QAEA,qDAAqD;QACrD,iEAAiE;QACjE,sFAAsF;QACtF,oEAAoE;QACpE,MAAM,kBAAkB,OAAO,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;YAC1D,iBAAiB;YACjB,2BAA2B;QAC7B,KAAK,EAAE;QAEP,MAAM,aAAa,SAAS;QAC5B,4CAA4C;QAC5C,MAAM,UAAU,gBAAgB,MAAM,KAAK,SAAS,AAAC,OAAO,QAAS;QAErE,oDAAoD;QACpD,OAAO,CAAA,GAAA,2IAAA,CAAA,kCAA+B,AAAD,EACnC,iBACA,YACA,SACA;IAGJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAMO,eAAe,+BACpB,MAAuB,EACvB,aAAiC;IAEjC,sEAAsE;IACtE,kEAAkE;IAClE,OAAO,MAAM,oBAAoB,QAAQ;AAC3C", "debugId": null}}, {"offset": {"line": 2178, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/page.tsx"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { redirect } from 'next/navigation';\r\nimport { Metadata } from 'next';\r\nimport ModernCustomerFeedList from '@/components/feed/ModernCustomerFeedList';\r\nimport { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';\r\nimport { getUnifiedFeedPostsWithAuthors } from '@/lib/actions/posts/unifiedFeed';\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Feed\",\r\n  description: \"View your personalized feed and stay updated\",\r\n};\r\n\r\nexport default async function CustomerDashboardPage() {\r\n  const supabase = await createClient();\r\n\r\n  // Check if user is authenticated\r\n  const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    redirect('/login?message=Please log in to view your dashboard');\r\n  }\r\n\r\n  // Check if customer has complete address\r\n  await requireCompleteProfile(user.id);\r\n\r\n  // Get the user's customer profile\r\n  const { data: customerProfile, error: profileError } = await supabase\r\n    .from('customer_profiles')\r\n    .select('name')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError) {\r\n    console.error('Error fetching customer profile:', profileError);\r\n  }\r\n\r\n  // Get the user's city from subscribed businesses\r\n  let citySlug: string | undefined;\r\n\r\n  const { data: subscriptions } = await supabase\r\n    .from('subscriptions')\r\n    .select(`\r\n      business_profiles (\r\n        city_slug\r\n      )\r\n    `)\r\n    .eq('user_id', user.id)\r\n    .limit(1);\r\n\r\n  if (subscriptions &&\r\n      subscriptions.length > 0 &&\r\n      subscriptions[0].business_profiles) {\r\n    const profile = subscriptions[0].business_profiles;\r\n    citySlug = typeof profile === 'object' && profile !== null && 'city_slug' in profile\r\n      ? (profile.city_slug as string) || undefined\r\n      : undefined;\r\n  }\r\n\r\n  // Use smart feed as the default filter\r\n  const initialFilter = 'smart';\r\n\r\n  // Get initial posts using the unified smart feed algorithm\r\n  const initialFeedResult = await getUnifiedFeedPostsWithAuthors({\r\n    filter: initialFilter,\r\n    page: 1,\r\n    limit: 10\r\n  });\r\n\r\n  const posts = initialFeedResult.success ? initialFeedResult.data?.items || [] : [];\r\n  const hasMore = initialFeedResult.success ? initialFeedResult.data?.hasMore || false : false;\r\n\r\n  if (!initialFeedResult.success) {\r\n    console.error('Error fetching initial posts:', initialFeedResult.error);\r\n  }\r\n\r\n  const customerName = customerProfile?.name || \"Valued Customer\";\r\n\r\n  return (\r\n    <ModernCustomerFeedList\r\n      initialPosts={posts}\r\n      initialTotalCount={0} // Not needed for infinite scroll\r\n      initialHasMore={hasMore}\r\n      initialFilter={initialFilter}\r\n      citySlug={citySlug}\r\n      userName={customerName}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAEA;AACA;AACA;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,iCAAiC;IACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,yCAAyC;IACzC,MAAM,CAAA,GAAA,uJAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,EAAE;IAEpC,kCAAkC;IAClC,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,qBACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,cAAc;QAChB,QAAQ,KAAK,CAAC,oCAAoC;IACpD;IAEA,iDAAiD;IACjD,IAAI;IAEJ,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,iBACL,MAAM,CAAC,CAAC;;;;IAIT,CAAC,EACA,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC;IAET,IAAI,iBACA,cAAc,MAAM,GAAG,KACvB,aAAa,CAAC,EAAE,CAAC,iBAAiB,EAAE;QACtC,MAAM,UAAU,aAAa,CAAC,EAAE,CAAC,iBAAiB;QAClD,WAAW,OAAO,YAAY,YAAY,YAAY,QAAQ,eAAe,UACzE,AAAC,QAAQ,SAAS,IAAe,YACjC;IACN;IAEA,uCAAuC;IACvC,MAAM,gBAAgB;IAEtB,2DAA2D;IAC3D,MAAM,oBAAoB,MAAM,CAAA,GAAA,sIAAA,CAAA,iCAA8B,AAAD,EAAE;QAC7D,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IAEA,MAAM,QAAQ,kBAAkB,OAAO,GAAG,kBAAkB,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE;IAClF,MAAM,UAAU,kBAAkB,OAAO,GAAG,kBAAkB,IAAI,EAAE,WAAW,QAAQ;IAEvF,IAAI,CAAC,kBAAkB,OAAO,EAAE;QAC9B,QAAQ,KAAK,CAAC,iCAAiC,kBAAkB,KAAK;IACxE;IAEA,MAAM,eAAe,iBAAiB,QAAQ;IAE9C,qBACE,8OAAC,6IAAA,CAAA,UAAsB;QACrB,cAAc;QACd,mBAAmB;QACnB,gBAAgB;QAChB,eAAe;QACf,UAAU;QACV,UAAU;;;;;;AAGhB", "debugId": null}}]}