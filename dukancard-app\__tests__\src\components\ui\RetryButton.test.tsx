import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import { RetryButton } from '@/src/components/ui/RetryButton';
import { RefreshCw } from 'lucide-react-native';

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  RefreshCw: jest.fn(() => null),
}));

describe('RetryButton', () => {
  const mockOnRetry = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (RefreshCw as jest.Mock).mockImplementation(() => <Text testID="refresh-icon">RefreshIcon</Text>);
  });

  it('renders correctly with default props', () => {
    render(<RetryButton onRetry={mockOnRetry} />);
    expect(screen.getByText('Retry')).toBeOnTheScreen();
    expect(screen.getByTestId('refresh-icon')).toBeOnTheScreen();
  });

  it('calls onRetry when button is pressed', async () => {
    render(<RetryButton onRetry={mockOnRetry} />);
    fireEvent.press(screen.getByText('Retry'));
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it('shows loading indicator when retrying', async () => {
    mockOnRetry.mockImplementationOnce(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(<RetryButton onRetry={mockOnRetry} />);
    fireEvent.press(screen.getByText('Retry'));

    expect(screen.getByText('Retrying...')).toBeOnTheScreen();
    expect(screen.queryByTestId('refresh-icon')).toBeNull();

    await waitFor(() => {
      expect(screen.queryByText('Retrying...')).toBeNull();
      expect(screen.getByText('Retry')).toBeOnTheScreen();
    });
  });

  it('is disabled when disabled prop is true', () => {
    render(<RetryButton onRetry={mockOnRetry} disabled={true} />);
    fireEvent.press(screen.getByText('Retry'));
    expect(mockOnRetry).not.toHaveBeenCalled();
  });

  it('applies secondary variant styles', () => {
    render(<RetryButton onRetry={mockOnRetry} variant="secondary" />);
    expect(screen.getByText('Retry').props.style).toContainEqual({
      backgroundColor: '#FFFFFF',
    });
  });

  it('applies ghost variant styles', () => {
    render(<RetryButton onRetry={mockOnRetry} variant="ghost" />);
    expect(screen.getByText('Retry').props.style).toContainEqual({
      backgroundColor: 'transparent',
    });
  });

  it('applies small size styles', () => {
    render(<RetryButton onRetry={mockOnRetry} size="small" />);
    expect(screen.getByText('Retry').props.style).toContainEqual({
      paddingHorizontal: 12,
      paddingVertical: 8,
    });
  });

  it('applies large size styles', () => {
    render(<RetryButton onRetry={mockOnRetry} size="large" />);
    expect(screen.getByText('Retry').props.style).toContainEqual({
      paddingHorizontal: 20,
      paddingVertical: 16,
    });
  });

  it('does not show icon when showIcon is false', () => {
    render(<RetryButton onRetry={mockOnRetry} showIcon={false} />);
    expect(screen.queryByTestId('refresh-icon')).toBeNull();
  });
});