import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import ImageCarousel from '../../../../src/components/product/ImageCarousel';
import { Dimensions } from 'react-native';

// Mock Dimensions to control screen width for consistent testing
jest.mock('react-native/Libraries/Utilities/Dimensions', () => ({
  get: jest.fn(() => ({
    width: 375, // Standard iPhone width for testing
    height: 667,
  })),
}));

// Mock react-native-reanimated-carousel
jest.mock('react-native-reanimated-carousel', () => {
  const React = require('react');
  const MockCarousel = React.forwardRef(({ children, onSnapToItem, defaultIndex, ...props }, ref) => {
    React.useImperativeHandle(ref, () => ({
      scrollTo: ({ index, animated }) => {
        if (onSnapToItem) {
          onSnapToItem(index);
        }
      },
    }));
    return <React.Fragment>{children}</React.Fragment>;
  });
  return { __esModule: true, default: MockCarousel };
});

// Mock FullScreenImageViewer
jest.mock('../../../../src/components/business/FullScreenImageViewer', () => 'FullScreenImageViewer');

describe('ImageCarousel', () => {
  const mockImages = [
    'https://example.com/image1.jpg',
    'https://example.com/image2.jpg',
    'https://example.com/image3.jpg',
  ];

  it('renders correctly with no images', () => {
    const { getByText } = render(<ImageCarousel images={[]} />);
    expect(getByText('No images available')).toBeTruthy();
  });

  it('renders a single image correctly', () => {
    const { getByTestId, queryByText } = render(<ImageCarousel images={[mockImages[0]]} />);
    expect(getByTestId('image-carousel-main-image')).toBeTruthy();
    expect(queryByText('1 / 1')).toBeNull(); // No counter for single image
    expect(queryByText('Show More')).toBeNull(); // No thumbnails for single image
  });

  it('renders multiple images and displays the counter', () => {
    const { getByText, getAllByTestId } = render(<ImageCarousel images={mockImages} />);
    expect(getAllByTestId('image-carousel-main-image').length).toBeGreaterThan(0);
    expect(getByText('1 / 3')).toBeTruthy();
  });

  it('changes main image and updates counter on thumbnail press', async () => {
    const { getByText, getAllByTestId } = render(<ImageCarousel images={mockImages} />);

    // Initial state
    expect(getByText('1 / 3')).toBeTruthy();

    // Press second thumbnail (index 1)
    fireEvent.press(getAllByTestId('image-carousel-thumbnail')[1]);

    await waitFor(() => {
      expect(getByText('2 / 3')).toBeTruthy();
    });
  });

  it('opens FullScreenImageViewer on main image press when onImagePress is not provided', () => {
    const { getByTestId, getByText } = render(<ImageCarousel images={mockImages} />);
    fireEvent.press(getByTestId('image-carousel-main-image'));
    expect(getByText('FullScreenImageViewer')).toBeTruthy();
  });

  it('calls onImagePress when provided', () => {
    const mockOnImagePress = jest.fn();
    const { getByTestId } = render(
      <ImageCarousel images={mockImages} onImagePress={mockOnImagePress} />
    );
    fireEvent.press(getByTestId('image-carousel-main-image'));
    expect(mockOnImagePress).toHaveBeenCalledWith(mockImages[0], 0);
  });

  it('handles image loading and error states', async () => {
    const badImage = 'invalid-url';
    const { getByTestId, getByText, queryByText } = render(
      <ImageCarousel images={[badImage]} />
    );

    // Simulate image load start
    fireEvent(getByTestId('image-carousel-main-image'), 'onLoadStart');
    expect(queryByText('ActivityIndicator')).toBeTruthy(); // Assuming ActivityIndicator is rendered

    // Simulate image error
    fireEvent(getByTestId('image-carousel-main-image'), 'onError');
    await waitFor(() => {
      expect(getByText('Image not available')).toBeTruthy();
      expect(queryByText('ActivityIndicator')).toBeNull();
    });
  });
});
