import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { ErrorBoundary } from '../../../src/components/ErrorBoundary';
import { Text, View } from 'react-native';

// Mock console.error to prevent it from polluting test output
const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

describe('ErrorBoundary', () => {
  afterAll(() => {
    consoleErrorSpy.mockRestore(); // Restore console.error after all tests
  });

  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary>
        <Text>Test Child</Text>
      </ErrorBoundary>
    );
    expect(screen.getByText('Test Child')).toBeTruthy();
    expect(screen.queryByText('Oops! Something went wrong')).toBeNull();
  });

  it('renders error message when a child component throws an error', () => {
    const ProblemChild = () => {
      throw new Error('Test Error');
    };

    render(
      <ErrorBoundary>
        <ProblemChild />
      </ErrorBoundary>
    );

    expect(screen.getByText('Oops! Something went wrong')).toBeTruthy();
    expect(screen.queryByText('Test Child')).toBeNull();
    expect(consoleErrorSpy).toHaveBeenCalled();
  });

  it('logs the error to console', () => {
    const ProblemChild = () => {
      throw new Error('Another Test Error');
    };

    render(
      <ErrorBoundary>
        <ProblemChild />
      </ErrorBoundary>
    );

    // The component logs errors through its error tracking system
    expect(consoleErrorSpy).toHaveBeenCalled();
  });

  it('resets error state when resetError is called', () => {
    const ProblemChild = () => {
      throw new Error('Error to be reset');
    };

    const { rerender } = render(
      <ErrorBoundary>
        <ProblemChild />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong.')).toBeTruthy();

    // Simulate a re-render that would typically happen after an error is resolved
    // In a real app, this might be triggered by a button click or prop change
    rerender(
      <ErrorBoundary>
        <Text>New Child Content</Text>
      </ErrorBoundary>
    );

    expect(screen.getByText('New Child Content')).toBeTruthy();
    expect(screen.queryByText('Something went wrong.')).toBeNull();
  });
});
