import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react-native';
import { ThemeProvider, useThemeContext, useColorScheme } from '@/src/contexts/ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme as useSystemColorScheme } from 'react-native';

// Mock necessary modules
jest.mock('@react-native-async-storage/async-storage');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  useColorScheme: jest.fn(),
}));

describe('ThemeContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null); // Default to no saved theme
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(null);
    (useSystemColorScheme as jest.Mock).mockReturnValue('light'); // Default system theme
  });

  const TestComponent = () => {
    const { themeMode, colorScheme, setThemeMode, isDark, isLight } = useThemeContext();
    const currentColorScheme = useColorScheme();
    return (
      <>
        <Text testID="theme-mode">{themeMode}</Text>
        <Text testID="color-scheme">{colorScheme}</Text>
        <Text testID="is-dark">{isDark.toString()}</Text>
        <Text testID="is-light">{isLight.toString()}</Text>
        <Text testID="current-color-scheme">{currentColorScheme}</Text>
        <button onPress={() => setThemeMode('dark')}>Set Dark</button>
        <button onPress={() => setThemeMode('light')}>Set Light</button>
        <button onPress={() => setThemeMode('system')}>Set System</button>
      </>
    );
  };

  it('initializes with system theme if no saved preference', async () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('theme-mode').props.children).toBe('system');
      expect(screen.getByTestId('color-scheme').props.children).toBe('light');
      expect(screen.getByTestId('is-dark').props.children).toBe('false');
      expect(screen.getByTestId('is-light').props.children).toBe('true');
      expect(screen.getByTestId('current-color-scheme').props.children).toBe('light');
    });
  });

  it('loads saved theme preference', async () => {
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue('dark');

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('theme-mode').props.children).toBe('dark');
      expect(screen.getByTestId('color-scheme').props.children).toBe('dark');
      expect(screen.getByTestId('is-dark').props.children).toBe('true');
      expect(screen.getByTestId('is-light').props.children).toBe('false');
      expect(screen.getByTestId('current-color-scheme').props.children).toBe('dark');
    });
  });

  it('updates theme mode and saves preference', async () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    await waitFor(() => expect(screen.getByTestId('theme-mode').props.children).toBe('system'));

    fireEvent.press(screen.getByText('Set Dark'));

    await waitFor(() => {
      expect(screen.getByTestId('theme-mode').props.children).toBe('dark');
      expect(screen.getByTestId('color-scheme').props.children).toBe('dark');
      expect(screen.getByTestId('is-dark').props.children).toBe('true');
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('@dukancard_theme_mode', 'dark');
    });

    fireEvent.press(screen.getByText('Set Light'));

    await waitFor(() => {
      expect(screen.getByTestId('theme-mode').props.children).toBe('light');
      expect(screen.getByTestId('color-scheme').props.children).toBe('light');
      expect(screen.getByTestId('is-light').props.children).toBe('true');
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('@dukancard_theme_mode', 'light');
    });

    fireEvent.press(screen.getByText('Set System'));

    await waitFor(() => {
      expect(screen.getByTestId('theme-mode').props.children).toBe('system');
      expect(screen.getByTestId('color-scheme').props.children).toBe('light'); // Assuming system is light
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('@dukancard_theme_mode', 'system');
    });
  });

  it('useColorScheme hook returns system theme if outside provider', () => {
    (useSystemColorScheme as jest.Mock).mockReturnValue('dark');
    const result = renderHook(() => useColorScheme());
    expect(result.current).toBe('dark');
  });

  it('throws error if useThemeContext is used outside ThemeProvider', () => {
    expect(() => render(<useThemeContext />)).toThrow(
      'useThemeContext must be used within a ThemeProvider'
    );
  });
});