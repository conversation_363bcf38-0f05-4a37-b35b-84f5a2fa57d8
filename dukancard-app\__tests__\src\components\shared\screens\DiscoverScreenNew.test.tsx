import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import DiscoverScreen from '../../../../src/components/shared/screens/DiscoverScreenNew';
import { DiscoverProvider, useDiscoverContext } from '@/src/contexts/DiscoveryContext';
import { useTheme } from '@/src/hooks/useTheme';
import { Alert } from 'react-native';

// Mock all external dependencies
jest.mock('@/src/contexts/DiscoveryContext', () => ({
  DiscoverProvider: ({ children }: any) => <>{children}</>,
  useDiscoverContext: jest.fn(() => ({
    viewType: 'cards',
    handleViewChange: jest.fn(),
    isSearching: false,
    isSorting: false,
    isFilteringByCategory: false,
    searchTerm: '',
    handleSearch: jest.fn(),
    handleClearSearch: jest.fn(),
    searchError: null,
    businesses: [],
    products: [],
    isLoadingMore: false,
    hasMore: false,
    loadMore: jest.fn(),
    totalCount: 0,
    userLocation: null,
    updateLocation: jest.fn(),
    isLocationLoading: false,
    refresh: jest.fn(),
    productSortBy: 'newest',
    sortBy: 'created_desc',
    handleProductSortChange: jest.fn(),
    handleBusinessSortChange: jest.fn(),
    selectedCategory: null,
    handleCategoryChange: jest.fn(),
  })),
}));

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      textPrimary: '#000',
      textSecondary: '#666',
      primary: '#D4AF37',
      border: '#eee',
      iconMuted: '#999',
    },
  }),
}));

jest.mock('@/src/components/discovery/CompactLocationPicker', () => jest.fn(() => <Text>MockCompactLocationPicker</Text>));
jest.mock('@/src/components/discovery/SearchSection', () => jest.fn(() => <Text>MockSearchSection</Text>));
jest.mock('@/src/components/discovery/ViewToggle', () => jest.fn(() => <Text>MockViewToggle</Text>));
jest.mock('@/src/components/discovery/ErrorComponents', () => ({
  ErrorSection: jest.fn(() => <Text>MockErrorSection</Text>),
}));
jest.mock('@/src/components/discovery/BusinessCard', () => jest.fn(({ business, onPress }) => (
  <TouchableOpacity testID={`business-card-${business.id}`} onPress={onPress}>
    <Text>{business.name}</Text>
  </TouchableOpacity>
)));
jest.mock('@/src/components/shared/ui/ProductCard', () => jest.fn(({ product, onPress }) => (
  <TouchableOpacity testID={`product-card-${product.id}`} onPress={onPress}>
    <Text>{product.name}</Text>
  </TouchableOpacity>
)));
jest.mock('@/src/components/discovery/NavigationHandlers', () => ({
  useDiscoveryNavigation: () => ({
    navigateToBusinessProfile: jest.fn(),
  }),
}));
jest.mock('@/src/components/ErrorBoundary', () => ({ ErrorBoundary: ({ children }: any) => <>{children}</> }));
jest.mock('@/src/components/discovery/styles/DiscoverScreenStyles', () => ({
  createDiscoverScreenStyles: () => ({
    contentContainer: {},
    clearSearchContainer: {},
    clearSearchButton: {},
    clearSearchText: {},
  }),
}));
jest.mock('@/src/utils/sortMappings', () => ({
  getSortDisplayName: jest.fn(() => 'Mock Sort'),
}));
jest.mock('@/src/components/discovery/SortBottomSheet', () => {
  const React = require('react');
  const MockSortBottomSheet = React.forwardRef(() => <Text>MockSortBottomSheet</Text>);
  return MockSortBottomSheet;
});
jest.mock('@/src/components/discovery/CategorySelector', () => jest.fn(() => <Text>MockCategorySelector</Text>));
jest.mock('@/src/components/pickers/CategoryBottomSheetPicker', () => {
  const React = require('react');
  const MockCategoryBottomSheetPicker = React.forwardRef(() => <Text>MockCategoryBottomSheetPicker</Text>);
  return MockCategoryBottomSheetPicker;
});
jest.mock('@/src/components/discovery/DiscoverySkeletons', () => ({
  BusinessCardSkeleton: jest.fn(() => <Text>MockBusinessCardSkeleton</Text>),
}));
jest.mock('@/src/components/ui/ProductSkeleton', () => ({
  ProductCardSkeleton: jest.fn(() => <Text>MockProductCardSkeleton</Text>),
}));
jest.mock('@/lib/utils', () => ({
  formatIndianNumberShort: jest.fn((num) => num.toString()),
}));

describe('DiscoverScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all main sections', () => {
    render(<DiscoverScreen />);
    expect(screen.getByText('MockCompactLocationPicker')).toBeTruthy();
    expect(screen.getByText('MockViewToggle')).toBeTruthy();
    expect(screen.getByText('MockCategorySelector')).toBeTruthy();
    expect(screen.getByText('MockSearchSection')).toBeTruthy();
    expect(screen.getByText('MockSortBottomSheet')).toBeTruthy();
    expect(screen.getByText('MockCategoryBottomSheetPicker')).toBeTruthy();
  });

  it('displays businesses when viewType is cards', () => {
    (useDiscoverContext as jest.Mock).mockReturnValue({
      viewType: 'cards',
      businesses: [{ id: 'b1', name: 'Business 1' }],
      products: [],
      totalCount: 1,
    });
    render(<DiscoverScreen />);
    expect(screen.getByText('Business 1')).toBeTruthy();
    expect(screen.queryByText('Product 1')).toBeNull();
  });

  it('displays products when viewType is products', () => {
    (useDiscoverContext as jest.Mock).mockReturnValue({
      viewType: 'products',
      businesses: [],
      products: [{ id: 'p1', name: 'Product 1' }],
      totalCount: 1,
    });
    render(<DiscoverScreen />);
    expect(screen.getByText('Product 1')).toBeTruthy();
    expect(screen.queryByText('Business 1')).toBeNull();
  });

  it('shows loading skeletons when loading', () => {
    (useDiscoverContext as jest.Mock).mockReturnValue({
      isSearching: true,
      viewType: 'cards',
      businesses: [],
      products: [],
      totalCount: 0,
    });
    render(<DiscoverScreen />);
    expect(screen.getAllByText('MockBusinessCardSkeleton').length).toBeGreaterThan(0);
  });

  it('shows no results found message when no data and not loading', () => {
    (useDiscoverContext as jest.Mock).mockReturnValue({
      isSearching: false,
      viewType: 'cards',
      businesses: [],
      products: [],
      totalCount: 0,
    });
    render(<DiscoverScreen />);
    expect(screen.getByText('No Business Cards Found')).toBeTruthy();
  });

  it('calls loadMore when onEndReached is triggered and hasMore is true', async () => {
    const mockLoadMore = jest.fn();
    (useDiscoverContext as jest.Mock).mockReturnValue({
      viewType: 'cards',
      businesses: Array(10).fill({ id: 'b', name: 'Business' }).map((b, i) => ({ ...b, id: `b${i}` })),
      products: [],
      isLoadingMore: false,
      hasMore: true,
      loadMore: mockLoadMore,
      totalCount: 10,
    });

    const { getByTestId } = render(<DiscoverScreen />);
    const flatList = getByTestId('flat-list'); // Assuming FlatList has a testID

    fireEvent.scroll(flatList, {
      nativeEvent: { contentOffset: { y: 100 }, contentSize: { height: 200 }, layoutMeasurement: { height: 100 } },
    });

    // Manually trigger onEndReached
    fireEvent(flatList, 'onEndReached');

    await waitFor(() => {
      expect(mockLoadMore).toHaveBeenCalledTimes(1);
    });
  });

  it('displays clear search button when searchTerm is present', () => {
    (useDiscoverContext as jest.Mock).mockReturnValue({
      searchTerm: 'test',
      businesses: [],
      products: [],
      totalCount: 0,
    });
    render(<DiscoverScreen />);
    expect(screen.getByText('Clear Search')).toBeTruthy();
  });

  it('calls handleClearSearch when clear search button is pressed', () => {
    const mockHandleClearSearch = jest.fn();
    (useDiscoverContext as jest.Mock).mockReturnValue({
      searchTerm: 'test',
      handleClearSearch: mockHandleClearSearch,
      businesses: [],
      products: [],
      totalCount: 0,
    });
    render(<DiscoverScreen />);
    fireEvent.press(screen.getByText('Clear Search'));
    expect(mockHandleClearSearch).toHaveBeenCalledTimes(1);
  });
});
