import { renderHook, act } from '@testing-library/react-hooks';
import { useLoadingState, useGoogleSignInLoading, useFormSubmissionLoading } from '@/src/hooks/useLoadingState';

describe('useLoadingState', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('should initialize with isLoading as false', () => {
    const { result } = renderHook(() => useLoadingState());
    expect(result.current.isLoading).toBe(false);
  });

  it('startLoading should set isLoading to true', () => {
    const { result } = renderHook(() => useLoadingState());
    act(() => {
      result.current.startLoading();
    });
    expect(result.current.isLoading).toBe(true);
  });

  it('stopLoading should set isLoading to false after minDuration and resetDelay', () => {
    const { result } = renderHook(() => useLoadingState({ minDuration: 200, resetDelay: 50 }));

    act(() => {
      result.current.startLoading();
    });
    expect(result.current.isLoading).toBe(true);

    act(() => {
      result.current.stopLoading();
    });
    expect(result.current.isLoading).toBe(true); // Still loading due to minDuration

    jest.advanceTimersByTime(199);
    expect(result.current.isLoading).toBe(true);

    jest.advanceTimersByTime(1);
    expect(result.current.isLoading).toBe(true); // Still loading due to resetDelay

    jest.advanceTimersByTime(50);
    expect(result.current.isLoading).toBe(false);
  });

  it('stopLoading with immediate=true should set isLoading to false immediately', () => {
    const { result } = renderHook(() => useLoadingState({ minDuration: 200, resetDelay: 50 }));

    act(() => {
      result.current.startLoading();
    });
    expect(result.current.isLoading).toBe(true);

    act(() => {
      result.current.stopLoading(true);
    });
    expect(result.current.isLoading).toBe(false);
  });

  it('stopLoadingWithDelay should set isLoading to false after specified delay', () => {
    const { result } = renderHook(() => useLoadingState({ minDuration: 0, resetDelay: 0 }));

    act(() => {
      result.current.startLoading();
    });
    expect(result.current.isLoading).toBe(true);

    act(() => {
      result.current.stopLoadingWithDelay(300);
    });
    expect(result.current.isLoading).toBe(true);

    jest.advanceTimersByTime(299);
    expect(result.current.isLoading).toBe(true);

    jest.advanceTimersByTime(1);
    expect(result.current.isLoading).toBe(false);
  });

  it('clearTimeouts should clear all pending timeouts', () => {
    const { result } = renderHook(() => useLoadingState({ minDuration: 1000, resetDelay: 1000 }));

    act(() => {
      result.current.startLoading();
      result.current.stopLoading();
    });

    expect(result.current.isLoading).toBe(true);

    act(() => {
      result.current.clearTimeouts();
    });

    jest.runAllTimers();
    expect(result.current.isLoading).toBe(true); // Should remain true as timeouts are cleared before they could set it to false
  });

  it('should reset on unmount by default', () => {
    const { result, unmount } = renderHook(() => useLoadingState());

    act(() => {
      result.current.startLoading();
    });
    expect(result.current.isLoading).toBe(true);

    unmount();
    expect(result.current.isLoading).toBe(false);
  });

  it('should not reset on unmount if resetOnUnmount is false', () => {
    const { result, unmount } = renderHook(() => useLoadingState({ resetOnUnmount: false }));

    act(() => {
      result.current.startLoading();
    });
    expect(result.current.isLoading).toBe(true);

    unmount();
    expect(result.current.isLoading).toBe(true);
  });
});

describe('useGoogleSignInLoading', () => {
  it('should return useLoadingState with specific Google Sign-In options', () => {
    const { result } = renderHook(() => useGoogleSignInLoading());
    // We can't directly assert the internal options passed to useLoadingState
    // but we can check the behavior based on those options.
    // For now, we'll just ensure it returns the loading state object.
    expect(result.current).toHaveProperty('isLoading');
    expect(result.current).toHaveProperty('startLoading');
    expect(result.current).toHaveProperty('stopLoading');
  });
});

describe('useFormSubmissionLoading', () => {
  it('should return useLoadingState with specific form submission options', () => {
    const { result } = renderHook(() => useFormSubmissionLoading());
    // Similar to useGoogleSignInLoading, check for the returned object structure.
    expect(result.current).toHaveProperty('isLoading');
    expect(result.current).toHaveProperty('startLoading');
    expect(result.current).toHaveProperty('stopLoading');
  });
});