import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import UnifiedBottomNavigation from '../../../../src/components/shared/navigation/UnifiedBottomNavigation';

// Mock necessary modules
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({
    profileStatus: {
      roleStatus: { role: 'customer' },
    },
  }),
}));

jest.mock('@/src/components/shared/navigation/BottomNavigation', () => ({
  Home: 'HomeIcon',
  Search: 'SearchIcon',
  Store: 'StoreIcon',
  User: 'UserIcon',
  DukancardBottomTabs: jest.fn(({ tabs, onQRScanPress, colorScheme }) => (
    <mock-DukancardBottomTabs
      testID="bottom-tabs"
      tabs={tabs}
      onQRScanPress={onQRScanPress}
      colorScheme={colorScheme}
    />
  )),
}));

jest.mock('../../qr/QRScannerModal', () => jest.fn(({ visible, onClose, onScanSuccess }) => {
  if (!visible) return null;
  return (
    <mock-QRScannerModal
      testID="qr-scanner-modal"
      visible={visible}
      onClose={onClose}
      onScanSuccess={onScanSuccess}
    />
  );
}));

describe('UnifiedBottomNavigation', () => {
  const mockRouterPush = jest.fn();
  const mockOnTabChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    require('expo-router').useRouter().push = mockRouterPush;
  });

  it('renders correctly with default props', () => {
    const { getByTestId } = render(<UnifiedBottomNavigation />);
    expect(getByTestId('bottom-tabs')).toBeTruthy();
  });

  it('handles onTabChange when provided', () => {
    const { getByTestId } = render(
      <UnifiedBottomNavigation onTabChange={mockOnTabChange} activeTab="home" />
    );
    const bottomTabs = getByTestId('bottom-tabs');
    const homeTab = bottomTabs.props.tabs.find((tab: any) => tab.key === 'home');
    fireEvent.press(homeTab.onPress);
    expect(mockOnTabChange).toHaveBeenCalledWith('home');
  });

  it('navigates correctly for customer user when onTabChange is not provided', () => {
    const { getByTestId } = render(<UnifiedBottomNavigation />);
    const bottomTabs = getByTestId('bottom-tabs');
    const homeTab = bottomTabs.props.tabs.find((tab: any) => tab.key === 'home');
    fireEvent.press(homeTab.onPress);
    expect(mockRouterPush).toHaveBeenCalledWith('/(dashboard)/customer');
  });

  it('navigates correctly for business user when onTabChange is not provided', () => {
    require('@/src/contexts/AuthContext').useAuth.mockReturnValue({
      profileStatus: { roleStatus: { role: 'business' } },
    });
    const { getByTestId } = render(<UnifiedBottomNavigation />);
    const bottomTabs = getByTestId('bottom-tabs');
    const homeTab = bottomTabs.props.tabs.find((tab: any) => tab.key === 'home');
    fireEvent.press(homeTab.onPress);
    expect(mockRouterPush).toHaveBeenCalledWith('/(dashboard)/business');
  });

  it('opens QRScannerModal when QR scan button is pressed', async () => {
    const { getByTestId, queryByTestId } = render(<UnifiedBottomNavigation showQRScanner={true} />);
    const bottomTabs = getByTestId('bottom-tabs');
    fireEvent.press(bottomTabs.props.onQRScanPress);

    await waitFor(() => {
      expect(queryByTestId('qr-scanner-modal')).toBeTruthy();
    });
  });

  it('closes QRScannerModal when onClose is called', async () => {
    const { getByTestId, queryByTestId } = render(<UnifiedBottomNavigation showQRScanner={true} />);
    const bottomTabs = getByTestId('bottom-tabs');
    fireEvent.press(bottomTabs.props.onQRScanPress);

    await waitFor(() => {
      expect(queryByTestId('qr-scanner-modal')).toBeTruthy();
    });

    const qrScannerModal = getByTestId('qr-scanner-modal');
    fireEvent(qrScannerModal, 'onClose');

    await waitFor(() => {
      expect(queryByTestId('qr-scanner-modal')).toBeNull();
    });
  });

  it('calls handleQRScanSuccess and navigates to business slug', async () => {
    const { getByTestId } = render(<UnifiedBottomNavigation showQRScanner={true} />);
    const bottomTabs = getByTestId('bottom-tabs');
    fireEvent.press(bottomTabs.props.onQRScanPress);

    await waitFor(() => {
      expect(getByTestId('qr-scanner-modal')).toBeTruthy();
    });

    const qrScannerModal = getByTestId('qr-scanner-modal');
    fireEvent(qrScannerModal, 'onScanSuccess', 'test-business-slug');

    await waitFor(() => {
      expect(mockRouterPush).toHaveBeenCalledWith('/business/test-business-slug');
      expect(qrScannerModal.props.visible).toBe(false); // Modal should close
    });
  });

  it('does not show QR scanner if showQRScanner is false', () => {
    const { queryByTestId } = render(<UnifiedBottomNavigation showQRScanner={false} />);
    const bottomTabs = queryByTestId('bottom-tabs');
    expect(bottomTabs.props.onQRScanPress).toBeUndefined();
  });
});
