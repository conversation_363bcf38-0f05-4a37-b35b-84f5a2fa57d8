import React from 'react';
import { render, screen, waitFor } from '@testing-library/react-native';
import { SplashScreen } from '@/src/components/ui/SplashScreen';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { DukancardLogo } from '@/src/components/ui/DukancardLogo';
import { Animated } from 'react-native';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme');
jest.mock('@/src/components/ui/DukancardLogo', () => ({
  DukancardLogo: jest.fn(() => null), // Mock <PERSON>cardLogo to return null
}));
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Animated: {
    Value: jest.fn(() => ({
      interpolate: jest.fn(() => '0deg'),
      setValue: jest.fn(),
      stopAnimation: jest.fn(),
    })),
    timing: jest.fn(() => ({
      start: jest.fn(),
    })),
    spring: jest.fn(() => ({
      start: jest.fn(),
    })),
    parallel: jest.fn((animations) => ({
      start: jest.fn((callback) => {
        animations.forEach((anim: any) => anim.start());
        if (callback) callback();
      }),
    })),
  },
}));

describe('SplashScreen', () => {
  const mockOnAnimationComplete = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (DukancardLogo as jest.Mock).mockImplementation((props) => <Text testID="dukancard-logo" {...props}>DukancardLogo</Text>);
  });

  it('renders DukancardLogo', () => {
    render(<SplashScreen />);
    expect(screen.getByTestId('dukancard-logo')).toBeOnTheScreen();
  });

  it('calls onAnimationComplete after duration', async () => {
    render(<SplashScreen onAnimationComplete={mockOnAnimationComplete} duration={100} />);

    await waitFor(() => {
      expect(mockOnAnimationComplete).toHaveBeenCalledTimes(1);
    }, { timeout: 150 }); // Give a little extra time for the timeout
  });

  it('applies dark mode background color', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    render(<SplashScreen />);
    // Check for dark mode specific styles (e.g., background color)
    // This is a simplified check, a snapshot test would be more comprehensive for styles.
    expect(screen.getByTestId('splash-screen-container').props.style).toContainEqual({
      backgroundColor: '#000000',
    });
  });

  it('starts parallel animations on mount', () => {
    render(<SplashScreen />);
    expect(Animated.parallel).toHaveBeenCalledTimes(1);
    expect(Animated.timing).toHaveBeenCalledTimes(1);
    expect(Animated.spring).toHaveBeenCalledTimes(1);
  });
});