import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { AnimatedLoader } from '@/src/components/ui/AnimatedLoader';
import { Loader2 } from 'lucide-react-native';

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  Loader2: jest.fn(() => null),
}));

// Mock Animated API
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Animated: {
    Value: jest.fn(() => ({
      interpolate: jest.fn(() => '0deg'),
      setValue: jest.fn(),
      stopAnimation: jest.fn(),
    })),
    timing: jest.fn(() => ({
      start: jest.fn(),
    })),
    loop: jest.fn((animation) => animation),
  },
}));

describe('AnimatedLoader', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (Loader2 as jest.Mock).mockImplementation(({ size, color }) => <Text testID="loader-icon" style={{ fontSize: size, color }}>Loader</Text>);
  });

  it('renders Loader2 icon with default size and color', () => {
    render(<AnimatedLoader />);
    expect(screen.getByTestId('loader-icon')).toBeOnTheScreen();
    expect(screen.getByTestId('loader-icon').props.style).toContainEqual({
      fontSize: 24,
      color: '#D4AF37',
    });
  });

  it('renders Loader2 icon with custom size and color', () => {
    render(<AnimatedLoader size={30} color="red" />);
    expect(screen.getByTestId('loader-icon')).toBeOnTheScreen();
    expect(screen.getByTestId('loader-icon').props.style).toContainEqual({
      fontSize: 30,
      color: 'red',
    });
  });

  it('applies custom style prop', () => {
    render(<AnimatedLoader style={{ opacity: 0.5 }} />);
    expect(screen.getByTestId('loader-icon').parent.props.style).toContainEqual({
      opacity: 0.5,
    });
  });

  it('starts and stops animation on mount and unmount', () => {
    const mockSetValue = jest.fn();
    const mockStart = jest.fn();
    const mockStopAnimation = jest.fn();

    (require('react-native').Animated.Value as jest.Mock).mockImplementation(() => ({
      interpolate: jest.fn(() => '0deg'),
      setValue: mockSetValue,
      stopAnimation: mockStopAnimation,
    }));
    (require('react-native').Animated.timing as jest.Mock).mockImplementation(() => ({
      start: mockStart,
    }));

    const { unmount } = render(<AnimatedLoader />);

    expect(mockSetValue).toHaveBeenCalledWith(0);
    expect(mockStart).toHaveBeenCalled();

    unmount();
    expect(mockStopAnimation).toHaveBeenCalled();
  });
});