import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import AvatarUploadSection from '@/src/components/profile/AvatarUploadSection';
import { ImagePickerBottomSheetRef } from '@/src/components/pickers/ImagePickerBottomSheet';

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  X: 'X',
  Camera: 'Camera',
  Loader2: 'Loader2',
}));

// Mock useToast hook
jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    showToast: jest.fn(),
  }),
}));

// Mock useAlertDialog hook
jest.mock('@/src/components/providers/AlertProvider', () => ({
  useAlertDialog: () => ({
    showAlert: jest.fn(),
  }),
}));

// Mock deleteCustomerAvatar service
jest.mock('@/backend/supabase/services/storage/avatarUploadService', () => ({
  deleteCustomerAvatar: jest.fn(),
}));

// Mock react-native's Animated module
jest.mock('react-native', () => {
  const ReactNative = jest.requireActual('react-native');
  return {
    ...ReactNative,
    Animated: {
      ...ReactNative.Animated,
      Value: jest.fn(() => ({
        setValue: jest.fn(),
        addListener: jest.fn(),
        removeListener: jest.fn(),
        removeAllListeners: jest.fn(),
        interpolate: jest.fn(),
      })),
      loop: jest.fn(() => ({
        start: jest.fn(),
        stop: jest.fn(),
      })),
      timing: jest.fn(() => ({
        start: jest.fn(),
        stop: jest.fn(),
      })),
      parallel: jest.fn(() => ({
        start: jest.fn(),
        stop: jest.fn(),
      })),
    },
  };
});

// Mock useTheme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    isDark: false,
    colors: {
      textPrimary: '#000000',
      textSecondary: '#666666',
      border: '#E5E7EB',
      error: '#EF4444',
      background: '#FFFFFF',
    },
    brandColors: {
      gold: '#D4AF37',
    },
    spacing: {
      sm: 8,
    },
  }),
}));

// Mock react-hook-form's useFormContext
jest.mock('react-hook-form', () => ({
  useFormContext: () => ({
    control: {
      _formValues: {
        avatarUri: '',
      },
    },
    setValue: jest.fn(),
  }),
}));

describe('AvatarUploadSection', () => {
  const mockImagePickerRef = React.createRef<ImagePickerBottomSheetRef>();
  mockImagePickerRef.current = {
    present: jest.fn(),
    dismiss: jest.fn(),
  };

  const mockTheme = {
    colors: {
      background: '#FFFFFF',
      textPrimary: '#000000',
    },
    brandColors: {
      gold: '#D4AF37',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    const { getByTestId, queryByTestId } = render(
      <AvatarUploadSection
        avatarUri=""
        isLoading={false}
        theme={mockTheme}
        imagePickerRef={mockImagePickerRef}
      />
    );
    expect(getByTestId('avatar-placeholder')).toBeTruthy();
    expect(queryByTestId('avatar-image')).toBeNull();
    expect(queryByTestId('loading-indicator')).toBeNull();
  });

  it('renders avatar image when avatarUri is provided', () => {
    const mockTheme = {
      colors: {
        background: '#FFFFFF',
        textPrimary: '#000000',
      },
      brandColors: {
        gold: '#D4AF37',
      },
    };
    const { getByTestId, queryByTestId } = render(
      <AvatarUploadSection
        avatarUri="https://example.com/avatar.jpg"
        isLoading={false}
        theme={mockTheme}
        imagePickerRef={mockImagePickerRef}
      />
    );
    expect(getByTestId('avatar-image')).toBeTruthy();
    expect(queryByTestId('avatar-placeholder')).toBeNull();
  });

  it('renders loading indicator when isLoading is true', () => {
    const { getByTestId, queryByTestId } = render(
      <AvatarUploadSection
        avatarUri=""
        isLoading={true}
        theme={mockTheme}
        imagePickerRef={mockImagePickerRef}
      />
    );
    expect(getByTestId('loading-indicator')).toBeTruthy();
    expect(queryByTestId('avatar-placeholder')).toBeNull();
    expect(queryByTestId('avatar-image')).toBeNull();
  });

  it('calls imagePickerRef.current.present when pressed', () => {
    const { getByTestId } = render(
      <AvatarUploadSection
        avatarUri=""
        isLoading={false}
        theme={mockTheme}
        imagePickerRef={mockImagePickerRef}
      />
    );
    fireEvent.press(getByTestId('avatar-touchable'));
    expect(mockImagePickerRef.current?.present).toHaveBeenCalledTimes(1);
  });

  it('disables touchable when loading', () => {
    const { getByTestId } = render(
      <AvatarUploadSection
        avatarUri=""
        isLoading={true}
        theme={mockTheme}
        imagePickerRef={mockImagePickerRef}
      />
    );
    const touchable = getByTestId('avatar-touchable');
    expect(touchable.props.disabled).toBe(true);
  });
});
