import { createAppError, handleNetworkError, handleSupabaseError, logError, retryWithBackoff } from '@/src/utils/errorHandling';

describe('errorHandling', () => {
  describe('createAppError', () => {
    it('should create an AppError object', () => {
      const error = createAppError('network', 'Test Title', 'Test Message', { code: 'NET_001', statusCode: 500, originalError: new Error('Original') });
      expect(error).toEqual({
        type: 'network',
        title: 'Test Title',
        message: 'Test Message',
        code: 'NET_001',
        statusCode: 500,
        originalError: expect.any(Error),
      });
    });

    it('should create an AppError with minimal details', () => {
      const error = createAppError('generic', 'Generic Title', 'Generic Message');
      expect(error).toEqual({
        type: 'generic',
        title: 'Generic Title',
        message: 'Generic Message',
        code: undefined,
        statusCode: undefined,
        originalError: undefined,
      });
    });
  });

  describe('handleNetworkError', () => {
    it('should handle no internet connection', () => {
      const error = { request: true, response: undefined };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('network');
      expect(appError.title).toBe('No Internet Connection');
    });

    it('should handle 400 status code', () => {
      const error = { response: { status: 400 } };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('validation');
      expect(appError.title).toBe('Invalid Request');
    });

    it('should handle 401 status code', () => {
      const error = { response: { status: 401 } };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('unauthorized');
      expect(appError.title).toBe('Authentication Required');
    });

    it('should handle 403 status code', () => {
      const error = { response: { status: 403 } };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('unauthorized');
      expect(appError.title).toBe('Access Denied');
    });

    it('should handle 404 status code', () => {
      const error = { response: { status: 404 } };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('notFound');
      expect(appError.title).toBe('Not Found');
    });

    it('should handle 429 status code', () => {
      const error = { response: { status: 429 } };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('server');
      expect(appError.title).toBe('Too Many Requests');
    });

    it('should handle 5xx status codes', () => {
      const error = { response: { status: 500 } };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('server');
      expect(appError.title).toBe('Server Error');
    });

    it('should handle timeout message', () => {
      const error = { message: 'Request timed out' };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('network');
      expect(appError.title).toBe('Request Timeout');
    });

    it('should handle generic network error message', () => {
      const error = { message: 'Network Error' };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('network');
      expect(appError.title).toBe('Network Error');
    });

    it('should handle unknown errors as generic', () => {
      const error = { someProp: 'value' };
      const appError = handleNetworkError(error);
      expect(appError.type).toBe('generic');
      expect(appError.title).toBe('Something went wrong');
    });
  });

  describe('handleSupabaseError', () => {
    it('should handle null error', () => {
      const appError = handleSupabaseError(null);
      expect(appError.type).toBe('generic');
      expect(appError.title).toBe('Unknown Error');
    });

    it('should handle email rate limit error', () => {
      const error = { message: 'too many emails have been sent' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('server');
      expect(appError.title).toBe('Email Rate Limit Exceeded');
    });

    it('should handle request rate limit error', () => {
      const error = { message: 'too many requests have been sent by this client' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('server');
      expect(appError.title).toBe('Request Rate Limit Exceeded');
    });

    it('should handle OTP expired error', () => {
      const error = { message: 'otp code for this sign-in has expired' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('validation');
      expect(appError.title).toBe('OTP Expired');
    });

    it('should handle invalid login credentials error', () => {
      const error = { message: 'invalid login credentials' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('unauthorized');
      expect(appError.title).toBe('Invalid Credentials');
    });

    it('should handle email not confirmed error', () => {
      const error = { message: 'email not confirmed' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('validation');
      expect(appError.title).toBe('Email Not Verified');
    });

    it('should handle user not found error', () => {
      const error = { message: 'user not found' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('notFound');
      expect(appError.title).toBe('Account Not Found');
    });

    it('should handle session expired errors', () => {
      let error = { message: 'user from sub claim in jwt does not exist' };
      let appError = handleSupabaseError(error);
      expect(appError.type).toBe('unauthorized');
      expect(appError.title).toBe('Session Expired');

      error = { message: 'session_not_found' };
      appError = handleSupabaseError(error);
      expect(appError.type).toBe('unauthorized');
      expect(appError.title).toBe('Session Expired');
    });

    it('should handle email already registered error', () => {
      const error = { message: 'email already registered' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('validation');
      expect(appError.title).toBe('Email Already Exists');
    });

    it('should handle password errors', () => {
      const error = { message: 'password must be at least 6 characters' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('validation');
      expect(appError.title).toBe('Password Error');
    });

    it('should handle network/connection errors', () => {
      const error = { message: 'network connection failed' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('network');
      expect(appError.title).toBe('Connection Error');
    });

    it('should handle PGRST116 error code', () => {
      const error = { code: 'PGRST116' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('notFound');
      expect(appError.title).toBe('Not Found');
    });

    it('should handle PGRST301 error code', () => {
      const error = { code: 'PGRST301' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('validation');
      expect(appError.title).toBe('Invalid Data');
    });

    it('should handle generic Supabase error', () => {
      const error = { message: 'Some other error' };
      const appError = handleSupabaseError(error);
      expect(appError.type).toBe('generic');
      expect(appError.title).toBe('Service Error');
      expect(appError.message).toBe('Some other error');
    });
  });

  describe('logError', () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    afterAll(() => {
      consoleErrorSpy.mockRestore();
    });

    it('should log error in development mode', () => {
      const originalDev = __DEV__;
      (global as any).__DEV__ = true;

      const error = createAppError('generic', 'Test', 'Test');
      logError(error, 'Test Context');
      expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error logged:', expect.objectContaining({
        context: 'Test Context',
        error: error,
      }));

      (global as any).__DEV__ = originalDev;
    });

    it('should not log error in production mode', () => {
      const originalDev = __DEV__;
      (global as any).__DEV__ = false;

      const error = createAppError('generic', 'Test', 'Test');
      logError(error, 'Test Context');
      expect(consoleErrorSpy).not.toHaveBeenCalled();

      (global as any).__DEV__ = originalDev;
    });
  });

  describe('retryWithBackoff', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.runOnlyPendingTimers();
      jest.useRealTimers();
    });

    it('should retry and succeed', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new Error('Attempt 1 failed'))
        .mockResolvedValueOnce('Success');

      const promise = retryWithBackoff(mockFn, 2, 100);

      jest.advanceTimersByTime(100);
      await Promise.resolve(); // Allow promise to resolve
      jest.advanceTimersByTime(200);
      await Promise.resolve();

      await expect(promise).resolves.toBe('Success');
      expect(mockFn).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retries', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new Error('Attempt 1 failed'))
        .mockRejectedValueOnce(new Error('Attempt 2 failed'))
        .mockRejectedValueOnce(new Error('Attempt 3 failed'));

      const promise = retryWithBackoff(mockFn, 2, 100);

      jest.advanceTimersByTime(100);
      await Promise.resolve();
      jest.advanceTimersByTime(200);
      await Promise.resolve();
      jest.advanceTimersByTime(400);
      await Promise.resolve();

      await expect(promise).rejects.toThrow('Attempt 3 failed');
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    it('should succeed on first attempt', async () => {
      const mockFn = jest.fn().mockResolvedValueOnce('Instant Success');

      const result = await retryWithBackoff(mockFn);
      expect(result).toBe('Instant Success');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });
});