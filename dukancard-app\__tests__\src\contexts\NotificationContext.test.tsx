import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react-native';
import { NotificationProvider, useNotifications } from '@/src/contexts/NotificationContext';
import { useAuth } from '@/src/contexts/AuthContext';
import { activityService } from '@/backend/supabase/services/activities/activityService';
import { realtimeService } from '@/backend/supabase/services/realtime/realtimeService';
import { AppState } from 'react-native';

// Mock all external dependencies
jest.mock('@/src/contexts/AuthContext');
jest.mock('@/backend/supabase/services/activities/activityService');
jest.mock('@/backend/supabase/services/realtime/realtimeService');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  AppState: {
    addEventListener: jest.fn(() => ({
      remove: jest.fn(),
    })),
    currentState: 'active',
  },
}));

describe('NotificationContext', () => {
  const mockUser = { id: 'user123' };
  const mockBusinessProfileStatus = { roleStatus: { role: 'business' } };
  const mockCustomerProfileStatus = { roleStatus: { role: 'customer' } };

  const mockNotifications = [
    { id: '1', title: 'Notif 1', is_read: false },
    { id: '2', title: 'Notif 2', is_read: true },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue({ user: mockUser, profileStatus: mockBusinessProfileStatus });
    (activityService.getBusinessActivities as jest.Mock).mockResolvedValue({
      success: true,
      data: mockNotifications,
    });
    (activityService.getUnreadActivitiesCount as jest.Mock).mockResolvedValue({
      count: 1,
    });
    (activityService.markActivitiesAsRead as jest.Mock).mockResolvedValue({
      success: true,
    });
    (realtimeService.subscribeToBusinessActivities as jest.Mock).mockReturnValue({
      unsubscribe: jest.fn(),
    });
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  const TestComponent = () => {
    const context = useNotifications();
    return (
      <>
        <Text testID="unread-count">{context.unreadCount}</Text>
        <Text testID="loading">{context.loading.toString()}</Text>
        <Text testID="refreshing">{context.refreshing.toString()}</Text>
        <Text testID="has-more">{context.hasMore.toString()}</Text>
        <Text testID="is-modal-visible">{context.isModalVisible.toString()}</Text>
        <button onPress={() => context.refreshNotifications()}>Refresh</button>
        <button onPress={() => context.loadMoreNotifications()}>Load More</button>
        <button onPress={() => context.markAsRead('1')}>Mark As Read</button>
        <button onPress={() => context.markAllAsRead()}>Mark All As Read</button>
        <button onPress={() => context.showModal()}>Show Modal</button>
        <button onPress={() => context.hideModal()}>Hide Modal</button>
        {context.notifications.map(n => (
          <Text key={n.id} testID={`notification-${n.id}`}>{n.title} - {n.is_read ? 'Read' : 'Unread'}</Text>
        ))}
      </>
    );
  };

  it('initializes notifications and unread count for business user', async () => {
    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('unread-count').props.children).toBe(1);
      expect(screen.getByTestId('loading').props.children).toBe('false');
      expect(screen.getByTestId('notification-1')).toBeOnTheScreen();
      expect(screen.getByTestId('notification-2')).toBeOnTheScreen();
    });
  });

  it('does not fetch notifications for non-business user', async () => {
    (useAuth as jest.Mock).mockReturnValue({ user: mockUser, profileStatus: mockCustomerProfileStatus });

    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    await waitFor(() => {
      expect(activityService.getBusinessActivities).not.toHaveBeenCalled();
      expect(activityService.getUnreadActivitiesCount).not.toHaveBeenCalled();
      expect(screen.getByTestId('unread-count').props.children).toBe(0);
    });
  });

  it('refreshes notifications', async () => {
    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    await waitFor(() => expect(screen.getByTestId('unread-count').props.children).toBe(1));

    (activityService.getBusinessActivities as jest.Mock).mockResolvedValueOnce({
      success: true,
      data: [{ id: '3', title: 'Notif 3', is_read: false }],
    });
    (activityService.getUnreadActivitiesCount as jest.Mock).mockResolvedValueOnce({
      count: 1,
    });

    fireEvent.press(screen.getByText('Refresh'));

    await waitFor(() => {
      expect(screen.getByTestId('notification-3')).toBeOnTheScreen();
      expect(screen.queryByTestId('notification-1')).toBeNull();
      expect(screen.getByTestId('unread-count').props.children).toBe(1);
    });
  });

  it('loads more notifications', async () => {
    (activityService.getBusinessActivities as jest.Mock).mockResolvedValueOnce({
      success: true,
      data: mockNotifications,
      hasMore: true,
    }).mockResolvedValueOnce({
      success: true,
      data: [{ id: '3', title: 'Notif 3', is_read: false }],
      hasMore: false,
    });

    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    await waitFor(() => expect(screen.getByTestId('notification-1')).toBeOnTheScreen());

    fireEvent.press(screen.getByText('Show Modal')); // Modal must be visible to load more
    fireEvent.press(screen.getByText('Load More'));

    await waitFor(() => {
      expect(screen.getByTestId('notification-3')).toBeOnTheScreen();
      expect(screen.getByTestId('has-more').props.children).toBe('false');
    });
  });

  it('marks a single notification as read', async () => {
    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    await waitFor(() => expect(screen.getByTestId('notification-1').props.children).toContain('Unread'));

    fireEvent.press(screen.getByText('Mark As Read'));

    await waitFor(() => {
      expect(activityService.markActivitiesAsRead).toHaveBeenCalledWith(mockUser.id, ['1']);
      expect(screen.getByTestId('notification-1').props.children).toContain('Read');
      expect(screen.getByTestId('unread-count').props.children).toBe(0);
    });
  });

  it('marks all notifications as read', async () => {
    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    await waitFor(() => expect(screen.getByTestId('notification-1').props.children).toContain('Unread'));

    fireEvent.press(screen.getByText('Mark All As Read'));

    await waitFor(() => {
      expect(activityService.markActivitiesAsRead).toHaveBeenCalledWith(mockUser.id, 'all');
      expect(screen.getByTestId('notification-1').props.children).toContain('Read');
      expect(screen.getByTestId('notification-2').props.children).toContain('Read');
      expect(screen.getByTestId('unread-count').props.children).toBe(0);
    });
  });

  it('shows and hides the modal', async () => {
    render(
      <NotificationProvider>
        <TestComponent />
      </NotificationProvider>
    );

    expect(screen.getByTestId('is-modal-visible').props.children).toBe('false');

    fireEvent.press(screen.getByText('Show Modal'));
    expect(screen.getByTestId('is-modal-visible').props.children).toBe('true');

    fireEvent.press(screen.getByText('Hide Modal'));
    expect(screen.getByTestId('is-modal-visible').props.children).toBe('false');
  });

  it('throws error if useNotifications is used outside provider', () => {
    expect(() => render(<useNotifications />)).toThrow(
      'useNotifications must be used within a NotificationProvider'
    );
  });
});