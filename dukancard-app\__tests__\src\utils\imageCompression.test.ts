import { 
  compressImageUltraAggressive,
  compressMultipleImages,
  toBase64DataUrl,
  getImageDimensions,
  validateImage,
  createThumbnail,
  cropToSquare,
  batchProcessImages,
} from '@/src/utils/imageCompression';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

// Mock external dependencies
jest.mock('expo-image-manipulator');

describe('imageCompression', () => {
  const mockImageUri = 'file://mock/image.jpg';
  const mockBase64 = 'data:image/jpeg;base64,mockbase64string';

  beforeEach(() => {
    jest.clearAllMocks();
    (manipulateAsync as jest.Mock).mockImplementation((uri, actions, options) => {
      const width = 800;
      const height = 600;
      return Promise.resolve({
        uri: uri,
        base64: options.base64 ? mockBase64 : undefined,
        width: actions.find(a => 'resize' in a)?.resize?.width || width,
        height: actions.find(a => 'resize' in a)?.resize?.height || height,
      });
    });
  });

  describe('compressImageUltraAggressive', () => {
    it('should compress image aggressively', async () => {
      const result = await compressImageUltraAggressive(mockImageUri);
      expect(result.uri).toBe(mockImageUri);
      expect(result.base64).toBe(mockBase64);
      expect(result.width).toBeDefined();
      expect(result.height).toBeDefined();
      expect(result.size).toBeDefined();
      expect(manipulateAsync).toHaveBeenCalled();
    });

    it('should handle compression failure', async () => {
      (manipulateAsync as jest.Mock).mockRejectedValue(new Error('Compression failed'));
      await expect(compressImageUltraAggressive(mockImageUri)).rejects.toThrow('Failed to compress image');
    });
  });

  describe('compressMultipleImages', () => {
    it('should compress multiple images in parallel', async () => {
      const uris = ['uri1.jpg', 'uri2.png'];
      const results = await compressMultipleImages(uris);
      expect(results.length).toBe(2);
      expect(manipulateAsync).toHaveBeenCalledTimes(2);
    });

    it('should handle failure of one image compression', async () => {
      (manipulateAsync as jest.Mock)
        .mockResolvedValueOnce({ uri: 'uri1.jpg', base64: mockBase64, width: 100, height: 100 })
        .mockRejectedValueOnce(new Error('Failed second image'));
      const uris = ['uri1.jpg', 'uri2.png'];
      await expect(compressMultipleImages(uris)).rejects.toThrow('Failed to compress images');
    });
  });

  describe('toBase64DataUrl', () => {
    it('should convert base64 to data URL for JPEG', () => {
      const dataUrl = toBase64DataUrl('somebase64', SaveFormat.JPEG);
      expect(dataUrl).toBe('data:image/jpeg;base64,somebase64');
    });

    it('should convert base64 to data URL for PNG', () => {
      const dataUrl = toBase64DataUrl('somebase64', SaveFormat.PNG);
      expect(dataUrl).toBe('data:image/png;base64,somebase64');
    });
  });

  describe('getImageDimensions', () => {
    it('should return image dimensions', async () => {
      const dimensions = await getImageDimensions(mockImageUri);
      expect(dimensions).toEqual({ width: 800, height: 600 });
      expect(manipulateAsync).toHaveBeenCalledWith(mockImageUri, [], expect.any(Object));
    });

    it('should handle error getting dimensions', async () => {
      (manipulateAsync as jest.Mock).mockRejectedValue(new Error('Dimension error'));
      await expect(getImageDimensions(mockImageUri)).rejects.toThrow('Failed to get image dimensions');
    });
  });

  describe('validateImage', () => {
    it('should return valid for image within size limit', () => {
      const result = validateImage(mockBase64, 1000);
      expect(result.isValid).toBe(true);
      expect(result.sizeKB).toBeCloseTo(mockBase64.length * 0.75 / 1024);
    });

    it('should return invalid for image exceeding size limit', () => {
      const largeBase64 = 'a'.repeat(20 * 1024 * 1024 / 0.75); // ~20MB
      const result = validateImage(largeBase64, 1000);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('exceeds maximum allowed size');
    });

    it('should return invalid for invalid base64 format', () => {
      const result = validateImage('invalid-base64');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid image format');
    });
  });

  describe('createThumbnail', () => {
    it('should create a thumbnail', async () => {
      const result = await createThumbnail(mockImageUri);
      expect(result.uri).toBe(mockImageUri);
      expect(result.width).toBe(150);
      expect(result.height).toBe(150);
      expect(manipulateAsync).toHaveBeenCalledWith(
        mockImageUri,
        [{ resize: { width: 150, height: 150 } }],
        expect.objectContaining({ compress: 0.8, format: SaveFormat.JPEG, base64: true })
      );
    });
  });

  describe('cropToSquare', () => {
    it('should crop image to square', async () => {
      const result = await cropToSquare(mockImageUri);
      expect(result.uri).toBe(mockImageUri);
      expect(result.width).toBe(800);
      expect(result.height).toBe(800);
      expect(manipulateAsync).toHaveBeenCalledWith(
        mockImageUri,
        expect.arrayContaining([
          expect.objectContaining({ crop: expect.any(Object) }),
          expect.objectContaining({ resize: { width: 800, height: 800 } }),
        ]),
        expect.objectContaining({ compress: 0.8, format: SaveFormat.JPEG, base64: true })
      );
    });
  });

  describe('batchProcessImages', () => {
    it('should batch process images with specified operations', async () => {
      const uris = ['uri1.jpg', 'uri2.png'];
      const results = await batchProcessImages(uris, { compress: { quality: 0.5 }, thumbnail: true, square: true });

      expect(results.compressed).toBeDefined();
      expect(results.thumbnails).toBeDefined();
      expect(results.squares).toBeDefined();

      expect(results.compressed.length).toBe(2);
      expect(results.thumbnails?.length).toBe(2);
      expect(results.squares?.length).toBe(2);

      expect(manipulateAsync).toHaveBeenCalledTimes(6); // 2 for compress, 2 for thumbnail, 2 for square
    });
  });
});