import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { AlertDialog, AlertButton } from '@/src/components/ui/AlertDialog';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { X, AlertTriangle, CheckCircle, XCircle, Info, HelpCircle } from 'lucide-react-native';

// Mock the useColorScheme hook
jest.mock('@/src/hooks/useColorScheme');

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  X: jest.fn(() => null),
  AlertTriangle: jest.fn(() => null),
  CheckCircle: jest.fn(() => null),
  XCircle: jest.fn(() => null),
  Info: jest.fn(() => null),
  HelpCircle: jest.fn(() => null),
}));

// Mock expo-blur's BlurView
jest.mock('expo-blur', () => ({
  BlurView: 'BlurView', // Mock BlurView as a simple string
}));

describe('AlertDialog', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
    // Reset icon mocks
    (X as jest.Mock).mockImplementation(() => <Text>XIcon</Text>);
    (AlertTriangle as jest.Mock).mockImplementation(() => <Text>AlertTriangleIcon</Text>);
    (CheckCircle as jest.Mock).mockImplementation(() => <Text>CheckCircleIcon</Text>);
    (XCircle as jest.Mock).mockImplementation(() => <Text>XCircleIcon</Text>);
    (Info as jest.Mock).mockImplementation(() => <Text>InfoIcon</Text>);
    (HelpCircle as jest.Mock).mockImplementation(() => <Text>HelpCircleIcon</Text>);
  });

  it('renders correctly with default props', () => {
    const buttons: AlertButton[] = [
      { text: 'OK', onPress: jest.fn() }
    ];
    render(<AlertDialog visible={true} title="Test Title" buttons={buttons} onClose={mockOnClose} />);

    expect(screen.getByText('Test Title')).toBeOnTheScreen();
    expect(screen.getByText('OK')).toBeOnTheScreen();
    expect(screen.getByText('InfoIcon')).toBeOnTheScreen(); // Default type is 'info'
    expect(screen.getByText('XIcon')).toBeOnTheScreen(); // Close button
  });

  it('renders with different alert types and their respective icons', () => {
    const buttons: AlertButton[] = [
      { text: 'OK', onPress: jest.fn() }
    ];

    const { rerender } = render(<AlertDialog visible={true} title="Success" type="success" buttons={buttons} />);
    expect(screen.getByText('CheckCircleIcon')).toBeOnTheScreen();

    rerender(<AlertDialog visible={true} title="Error" type="error" buttons={buttons} />);
    expect(screen.getByText('XCircleIcon')).toBeOnTheScreen();

    rerender(<AlertDialog visible={true} title="Warning" type="warning" buttons={buttons} />);
    expect(screen.getByText('AlertTriangleIcon')).toBeOnTheScreen();

    rerender(<AlertDialog visible={true} title="Question" type="question" buttons={buttons} />);
    expect(screen.getByText('HelpCircleIcon')).toBeOnTheScreen();
  });

  it('renders custom icon when provided', () => {
    const buttons: AlertButton[] = [
      { text: 'OK', onPress: jest.fn() }
    ];
    const CustomIcon = () => <Text>MyCustomIcon</Text>;
    render(<AlertDialog visible={true} title="Custom" buttons={buttons} customIcon={<CustomIcon />} />);
    expect(screen.getByText('MyCustomIcon')).toBeOnTheScreen();
    expect(screen.queryByText('InfoIcon')).toBeNull(); // Default icon should not be present
  });

  it('calls onClose when close button is pressed', () => {
    const buttons: AlertButton[] = [
      { text: 'OK', onPress: jest.fn() }
    ];
    render(<AlertDialog visible={true} title="Test" buttons={buttons} onClose={mockOnClose} showCloseButton={true} />);
    fireEvent.press(screen.getByText('XIcon'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('does not render close button if showCloseButton is false', () => {
    const buttons: AlertButton[] = [
      { text: 'OK', onPress: jest.fn() }
    ];
    render(<AlertDialog visible={true} title="Test" buttons={buttons} onClose={mockOnClose} showCloseButton={false} />);
    expect(screen.queryByText('XIcon')).toBeNull();
  });

  it('renders message when provided', () => {
    const buttons: AlertButton[] = [
      { text: 'OK', onPress: jest.fn() }
    ];
    render(<AlertDialog visible={true} title="Test" message="This is a test message." buttons={buttons} />);
    expect(screen.getByText('This is a test message.')).toBeOnTheScreen();
  });

  it('handles multiple buttons with different styles', () => {
    const mockOnPress1 = jest.fn();
    const mockOnPress2 = jest.fn();
    const mockOnPress3 = jest.fn();
    const buttons: AlertButton[] = [
      { text: 'Default', onPress: mockOnPress1, style: 'default' },
      { text: 'Destructive', onPress: mockOnPress2, style: 'destructive' },
      { text: 'Cancel', onPress: mockOnPress3, style: 'cancel' },
    ];
    render(<AlertDialog visible={true} title="Multiple Buttons" buttons={buttons} />);

    const defaultButton = screen.getByText('Default');
    const destructiveButton = screen.getByText('Destructive');
    const cancelButton = screen.getByText('Cancel');

    expect(defaultButton).toBeOnTheScreen();
    expect(destructiveButton).toBeOnTheScreen();
    expect(cancelButton).toBeOnTheScreen();

    fireEvent.press(defaultButton);
    expect(mockOnPress1).toHaveBeenCalledTimes(1);

    fireEvent.press(destructiveButton);
    expect(mockOnPress2).toHaveBeenCalledTimes(1);

    fireEvent.press(cancelButton);
    expect(mockOnPress3).toHaveBeenCalledTimes(1);
  });

  it('shows loading indicator for buttons with loading prop', () => {
    const buttons: AlertButton[] = [
      { text: 'Loading', onPress: jest.fn(), loading: true },
    ];
    render(<AlertDialog visible={true} title="Loading Button" buttons={buttons} />);
    expect(screen.getByTestId('activity-indicator')).toBeOnTheScreen();
    expect(screen.queryByText('Loading')).toBeNull(); // Text should be hidden when loading
  });

  it('disables button when loading', () => {
    const mockOnPress = jest.fn();
    const buttons: AlertButton[] = [
      { text: 'Loading', onPress: mockOnPress, loading: true },
    ];
    render(<AlertDialog visible={true} title="Loading Button" buttons={buttons} />);
    fireEvent.press(screen.getByTestId('activity-indicator')); // Try pressing the loading indicator
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('applies dark mode styles', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    const buttons: AlertButton[] = [
      { text: 'OK', onPress: jest.fn() }
    ];
    render(<AlertDialog visible={true} title="Dark Mode" buttons={buttons} />);

    // Check for dark mode specific styles (e.g., background color, text color)
    // This is a simplified check, a snapshot test would be more comprehensive for styles.
    expect(screen.getByText('Dark Mode').props.style).toContainEqual({
      color: '#FFFFFF',
    });
  });
});