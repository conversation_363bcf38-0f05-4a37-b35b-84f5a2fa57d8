import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import EditProfileModal from '@/src/components/modals/customer/EditProfileModal';
import { useTheme } from '@/src/hooks/useTheme';
import { useAuth } from '@/src/contexts/AuthContext';
import { useToast } from '@/src/components/ui/Toast';
import { useLocationPermission } from '@/src/hooks/useLocationPermission';
import { usePincodeDetails } from '@/src/hooks/usePincodeDetails';
import { getCustomerProfileForEdit, updateCustomerProfileWithCoordinates } from '@/backend/supabase/services/customer/customerProfileService';
import { uploadAvatarImage, openCameraForAvatar, openGalleryForAvatar, deleteCustomerAvatar } from '@/backend/supabase/services/storage/avatarUploadService';
import { getCurrentUser } from '@/lib/auth/customerAuth';
import { supabase } from '@/lib/supabase';
import NetInfo from '@react-native-community/netinfo';
import * as ImagePicker from 'expo-image-picker';

// Mock dependencies
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      background: '#fff',
      textPrimary: '#000',
      textSecondary: '#888',
      border: '#ccc',
      primary: '#C29D5B',
    },
    isDark: false,
  }),
}));

jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: 'user123', user_metadata: { full_name: 'Test User' } }, refreshProfileStatus: jest.fn() }),
}));

jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
  }),
}));

jest.mock('@/src/hooks/useLocationPermission', () => ({
  useLocationPermission: () => ({ permission: { granted: true } }),
}));

jest.mock('@/src/hooks/usePincodeDetails', () => ({
  usePincodeDetails: () => ({
    isPincodeLoading: false,
    availableLocalities: ['Locality A', 'Locality B'],
    handlePincodeChange: jest.fn(),
  }),
}));

jest.mock('@/backend/supabase/services/customer/customerProfileService', () => ({
  getCustomerProfileForEdit: jest.fn(),
  updateCustomerProfileWithCoordinates: jest.fn(),
}));

jest.mock('@/backend/supabase/services/storage/avatarUploadService', () => ({
  uploadAvatarImage: jest.fn(),
  openCameraForAvatar: jest.fn(),
  openGalleryForAvatar: jest.fn(),
  deleteCustomerAvatar: jest.fn(),
}));

jest.mock('@/lib/auth/customerAuth', () => ({
  getCurrentUser: jest.fn(() => Promise.resolve({ id: 'user123' })),
}));

jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      updateUser: jest.fn(() => Promise.resolve({ error: null })),
    },
  },
}));

jest.mock('@react-native-community/netinfo', () => ({
  fetch: jest.fn(() => Promise.resolve({ isConnected: true })),
}));

jest.mock('expo-image-picker', () => ({
  launchCameraAsync: jest.fn(),
  launchImageLibraryAsync: jest.fn(),
  MediaTypeOptions: { Images: 'Images' },
}));

describe('EditProfileModal', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (getCustomerProfileForEdit as jest.Mock).mockResolvedValue({ data: null, error: null });
    (updateCustomerProfileWithCoordinates as jest.Mock).mockResolvedValue({ data: {}, error: null });
    (uploadAvatarImage as jest.Mock).mockResolvedValue({ success: true, url: 'http://new-avatar.com/avatar.png' });
  });

  it('renders correctly when visible', async () => {
    const { getByText } = render(<EditProfileModal visible={true} onClose={mockOnClose} />);
    await waitFor(() => {
      expect(getByText('Edit Profile')).toBeTruthy();
    });
  });

  it('closes the modal when close button is pressed', async () => {
    const { getByTestId } = render(<EditProfileModal visible={true} onClose={mockOnClose} />);
    await act(async () => {
      fireEvent.press(getByTestId('close-button'));
    });
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles form submission successfully', async () => {
    const { getByText, getByPlaceholderText } = render(<EditProfileModal visible={true} onClose={mockOnClose} />);

    await waitFor(() => {
      fireEvent.changeText(getByPlaceholderText('Enter your full name'), 'Updated Name');
      fireEvent.changeText(getByPlaceholderText('Enter your complete address'), '123 Main St');
      fireEvent.changeText(getByPlaceholderText('Enter 6-digit pincode'), '123456');
    });

    // Mock location data for successful submission
    const locationPicker = screen.getByTestId('location-picker');
    fireEvent(locationPicker, 'onLocationDetected', 10, 20);
    fireEvent(locationPicker, 'onAddressDetected', { pincode: '123456', city: 'TestCity', state: 'TestState', locality: 'TestLocality' });

    await act(async () => {
      fireEvent.press(getByText('Update Profile'));
    });

    expect(updateCustomerProfileWithCoordinates).toHaveBeenCalled();
    expect(useToast().success).toHaveBeenCalledWith('Profile Updated!', 'Your profile has been updated successfully!');
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('shows error if no internet connection', async () => {
    (NetInfo.fetch as jest.Mock).mockResolvedValue({ isConnected: false });
    const { getByText } = render(<EditProfileModal visible={true} onClose={mockOnClose} />);

    await act(async () => {
      fireEvent.press(getByText('Update Profile'));
    });

    expect(useToast().error).toHaveBeenCalledWith('No Internet Connection', 'Please check your internet connection and try again.');
  });
});
