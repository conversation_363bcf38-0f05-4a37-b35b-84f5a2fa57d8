import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { AddressForm } from '../../../../src/components/profile/AddressForm';
import { Alert } from 'react-native';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/backend/supabase/services/common/profileService', () => ({
  updateCustomerAddress: jest.fn(() => Promise.resolve({ success: true })),
  validatePincode: jest.fn(() => Promise.resolve({ success: true, data: { city: 'MockCity', state: 'MockState', localities: ['MockLocality'] }})),
}));

jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: 'test-user-id' } }),
}));

jest.mock('@/src/hooks/usePincodeDetails', () => ({
  usePincodeDetails: jest.fn(() => ({
    isPincodeLoading: false,
    availableLocalities: ['MockLocality'],
    handlePincodeChange: jest.fn(),
  })),
}));

jest.mock('@/src/components/ui/Input', () => ({
  Input: jest.fn(({ onChangeText, value, placeholder, error, leftIcon, editable, maxLength, containerStyle, style }) => (
    <TextInput
      testID={`input-${placeholder}`}
      onChangeText={onChangeText}
      value={value}
      placeholder={placeholder}
      editable={editable}
    />
  )),
}));

jest.mock('@/src/components/ui/Button', () => ({
  Button: jest.fn(({ title, onPress, disabled, variant, icon }) => (
    <TouchableOpacity testID={`button-${title}`} onPress={onPress} disabled={disabled}>
      <Text>{title}</Text>
      {icon}
    </TouchableOpacity>
  )),
}));

jest.mock('@/src/components/ui/LocationPicker', () => ({
  LocationPicker: jest.fn(({ onLocationDetected, onAddressDetected, onError, disabled }) => (
    <TouchableOpacity
      testID="location-picker"
      onPress={() => onAddressDetected({ pincode: '111111', city: 'DetectedCity', state: 'DetectedState', locality: 'DetectedLocality' })}
      disabled={disabled}
    >
      <Text>Mock Location Picker</Text>
    </TouchableOpacity>
  )),
}));

jest.spyOn(Alert, 'alert');

describe('AddressForm', () => {
  it('renders correctly with initial data', () => {
    const initialData = {
      address: '123 Main St',
      pincode: '123456',
      city: 'TestCity',
      state: 'TestState',
      locality: 'TestLocality',
    };
    const { getByDisplayValue } = render(<AddressForm initialData={initialData} />);

    expect(getByDisplayValue('123 Main St')).toBeTruthy();
    expect(getByDisplayValue('123456')).toBeTruthy();
    expect(getByDisplayValue('TestCity')).toBeTruthy();
    expect(getByDisplayValue('TestState')).toBeTruthy();
    expect(getByDisplayValue('TestLocality')).toBeTruthy();
  });

  it('handles pincode input and auto-fills city/state', async () => {
    const { getByTestId, getByDisplayValue } = render(<AddressForm />);
    const pincodeInput = getByTestId('input-Enter 6-digit pincode');

    fireEvent.changeText(pincodeInput, '123456');

    await waitFor(() => {
      expect(getByDisplayValue('MockCity')).toBeTruthy();
      expect(getByDisplayValue('MockState')).toBeTruthy();
      expect(getByDisplayValue('MockLocality')).toBeTruthy();
    });
  });

  it('validates form and shows errors', async () => {
    const { getByTestId, getByText } = render(<AddressForm />);
    const saveButton = getByTestId('button-Save Address');

    fireEvent.press(saveButton);

    await waitFor(() => {
      expect(getByText('Pincode is required')).toBeTruthy();
      expect(getByText('City is required')).toBeTruthy();
      expect(getByText('State is required')).toBeTruthy();
      expect(getByText('Locality is required')).toBeTruthy();
    });
  });

  it('submits form successfully', async () => {
    const onAddressUpdatedMock = jest.fn();
    const { getByTestId } = render(<AddressForm onAddressUpdated={onAddressUpdatedMock} />);

    fireEvent.changeText(getByTestId('input-Enter 6-digit pincode'), '123456');
    fireEvent.changeText(getByTestId('input-Enter your locality'), 'SomeLocality');

    fireEvent.press(getByTestId('button-Save Address'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Address updated successfully!');
      expect(onAddressUpdatedMock).toHaveBeenCalled();
    });
  });

  it('handles location detection from LocationPicker', async () => {
    const { getByTestId, getByDisplayValue } = render(<AddressForm />);
    const locationPicker = getByTestId('location-picker');

    fireEvent.press(locationPicker);

    await waitFor(() => {
      expect(getByDisplayValue('111111')).toBeTruthy();
      expect(getByDisplayValue('DetectedCity')).toBeTruthy();
      expect(getByDisplayValue('DetectedState')).toBeTruthy();
      expect(getByDisplayValue('DetectedLocality')).toBeTruthy();
    });
  });
});
