import { 
  getRedirectPath,
  checkOnboardingStatus,
  handleAuthRedirect,
  canAccessRoute,
  getDashboardRoute,
  isProtectedRoute,
  isAuthRoute,
} from '@/src/utils/navigation';

describe('navigation utilities', () => {
  describe('getRedirectPath', () => {
    it('should redirect unauthenticated users to login', () => {
      const state = { isAuthenticated: false, roleStatus: null, currentSegments: [] };
      expect(getRedirectPath(state)).toBe('/(auth)/login');
    });

    it('should not redirect unauthenticated users if already on auth screen', () => {
      const state = { isAuthenticated: false, roleStatus: null, currentSegments: ['(auth)', 'login'] };
      expect(getRedirectPath(state)).toBeNull();
    });

    it('should redirect authenticated users without role status to choose-role', () => {
      const state = { isAuthenticated: true, roleStatus: null, currentSegments: [] };
      expect(getRedirectPath(state)).toBe('/(auth)/choose-role');
    });

    it('should not redirect authenticated users without role status if already on choose-role', () => {
      const state = { isAuthenticated: true, roleStatus: null, currentSegments: ['(auth)', 'choose-role'] };
      expect(getRedirectPath(state)).toBeNull();
    });

    it('should redirect business users needing onboarding to business-details', () => {
      const state = { isAuthenticated: true, roleStatus: { role: 'business', needsRoleSelection: false, needsOnboarding: true, needsProfileCompletion: false }, currentSegments: [] };
      expect(getRedirectPath(state)).toBe('/(onboarding)/business-details');
    });

    it('should not redirect business users needing onboarding if already on onboarding screen', () => {
      const state = { isAuthenticated: true, roleStatus: { role: 'business', needsRoleSelection: false, needsOnboarding: true, needsProfileCompletion: false }, currentSegments: ['(onboarding)', 'business-details'] };
      expect(getRedirectPath(state)).toBeNull();
    });

    it('should redirect customer users needing profile completion to complete-profile', () => {
      const state = { isAuthenticated: true, roleStatus: { role: 'customer', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: true }, currentSegments: [] };
      expect(getRedirectPath(state)).toBe('/(auth)/complete-profile');
    });

    it('should not redirect customer users needing profile completion if already on complete-profile', () => {
      const state = { isAuthenticated: true, roleStatus: { role: 'customer', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: true }, currentSegments: ['(auth)', 'complete-profile'] };
      expect(getRedirectPath(state)).toBeNull();
    });

    it('should redirect fully set up customer to customer dashboard from auth/onboarding/index', () => {
      const roleStatus = { role: 'customer', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: false };
      expect(getRedirectPath({ isAuthenticated: true, roleStatus, currentSegments: ['(auth)', 'login'] })).toBe('/(dashboard)/customer');
      expect(getRedirectPath({ isAuthenticated: true, roleStatus, currentSegments: ['(onboarding)', 'business-details'] })).toBe('/(dashboard)/customer');
      expect(getRedirectPath({ isAuthenticated: true, roleStatus, currentSegments: [] })).toBe('/(dashboard)/customer');
    });

    it('should redirect fully set up business to business dashboard from auth/onboarding/index', () => {
      const roleStatus = { role: 'business', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: false };
      expect(getRedirectPath({ isAuthenticated: true, roleStatus, currentSegments: ['(auth)', 'login'] })).toBe('/(dashboard)/business');
      expect(getRedirectPath({ isAuthenticated: true, roleStatus, currentSegments: ['(onboarding)', 'business-details'] })).toBe('/(dashboard)/business');
      expect(getRedirectPath({ isAuthenticated: true, roleStatus, currentSegments: [] })).toBe('/(dashboard)/business');
    });

    it('should redirect business user from customer dashboard', () => {
      const roleStatus = { role: 'business', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: false };
      const state = { isAuthenticated: true, roleStatus, currentSegments: ['(dashboard)', 'customer'] };
      expect(getRedirectPath(state)).toBe('/(dashboard)/business');
    });

    it('should redirect customer user from business dashboard', () => {
      const roleStatus = { role: 'customer', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: false };
      const state = { isAuthenticated: true, roleStatus, currentSegments: ['(dashboard)', 'business'] };
      expect(getRedirectPath(state)).toBe('/(dashboard)/customer');
    });

    it('should return null if no redirect is needed', () => {
      const roleStatus = { role: 'customer', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: false };
      const state = { isAuthenticated: true, roleStatus, currentSegments: ['(dashboard)', 'customer'] };
      expect(getRedirectPath(state)).toBeNull();
    });
  });

  describe('checkOnboardingStatus', () => {
    it('should return needsRoleSelection true if roleStatus is null', () => {
      const status = checkOnboardingStatus(null);
      expect(status).toEqual({ needsRoleSelection: true, needsOnboarding: false, isComplete: false });
    });

    it('should return correct status for business user needing onboarding', () => {
      const roleStatus = { role: 'business', needsRoleSelection: false, needsOnboarding: true, needsProfileCompletion: false };
      const status = checkOnboardingStatus(roleStatus);
      expect(status).toEqual({ needsRoleSelection: false, needsOnboarding: true, isComplete: false });
    });

    it('should return correct status for customer user needing profile completion', () => {
      const roleStatus = { role: 'customer', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: true };
      const status = checkOnboardingStatus(roleStatus);
      expect(status).toEqual({ needsRoleSelection: false, needsOnboarding: false, isComplete: false });
    });

    it('should return isComplete true for fully set up user', () => {
      const roleStatus = { role: 'customer', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: false };
      const status = checkOnboardingStatus(roleStatus);
      expect(status).toEqual({ needsRoleSelection: false, needsOnboarding: false, isComplete: true });
    });
  });

  describe('handleAuthRedirect', () => {
    it('should call router.replace and onRedirect if redirectPath is provided', () => {
      const mockRouter = { replace: jest.fn() };
      const mockOnRedirect = jest.fn();
      handleAuthRedirect('/dashboard', mockRouter, { onRedirect: mockOnRedirect });
      expect(mockRouter.replace).toHaveBeenCalledWith('/dashboard');
      expect(mockOnRedirect).toHaveBeenCalledWith('/dashboard');
    });

    it('should not do anything if redirectPath is null', () => {
      const mockRouter = { replace: jest.fn() };
      const mockOnRedirect = jest.fn();
      handleAuthRedirect(null, mockRouter, { onRedirect: mockOnRedirect });
      expect(mockRouter.replace).not.toHaveBeenCalled();
      expect(mockOnRedirect).not.toHaveBeenCalled();
    });

    it('should call onError if router.replace throws an error', () => {
      const mockError = new Error('Navigation error');
      const mockRouter = { replace: jest.fn(() => { throw mockError; }) };
      const mockOnError = jest.fn();
      handleAuthRedirect('/dashboard', mockRouter, { onError: mockOnError });
      expect(mockOnError).toHaveBeenCalledWith(mockError);
    });
  });

  describe('canAccessRoute', () => {
    it('should allow access to auth routes for unauthenticated users', () => {
      const state = { isAuthenticated: false, roleStatus: null, currentSegments: ['(auth)', 'login'] };
      expect(canAccessRoute('/(auth)/login', state).canAccess).toBe(true);
    });

    it('should deny access to auth routes for authenticated users', () => {
      const state = { isAuthenticated: true, roleStatus: { role: 'customer', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: false }, currentSegments: ['(auth)', 'login'] };
      expect(canAccessRoute('/(auth)/login', state).canAccess).toBe(false);
      expect(canAccessRoute('/(auth)/login', state).reason).toBe('Authenticated users should not access auth screens');
    });

    it('should deny access to protected routes for unauthenticated users', () => {
      const state = { isAuthenticated: false, roleStatus: null, currentSegments: [] };
      expect(canAccessRoute('/(dashboard)/customer', state).canAccess).toBe(false);
      expect(canAccessRoute('/(dashboard)/customer', state).reason).toBe('Authentication required');
    });

    it('should deny access if role selection is required and not on choose-role', () => {
      const state = { isAuthenticated: true, roleStatus: { needsRoleSelection: true } as any, currentSegments: [] };
      expect(canAccessRoute('/(dashboard)/customer', state).canAccess).toBe(false);
      expect(canAccessRoute('/(dashboard)/customer', state).reason).toBe('Role selection required');
    });

    it('should deny access if onboarding is required for business user', () => {
      const state = { isAuthenticated: true, roleStatus: { role: 'business', needsRoleSelection: false, needsOnboarding: true } as any, currentSegments: [] };
      expect(canAccessRoute('/(dashboard)/business', state).canAccess).toBe(false);
      expect(canAccessRoute('/(dashboard)/business', state).reason).toBe('Onboarding required');
    });

    it('should deny access if dashboard type mismatch', () => {
      const state = { isAuthenticated: true, roleStatus: { role: 'customer' } as any, currentSegments: [] };
      expect(canAccessRoute('/(dashboard)/business', state).canAccess).toBe(false);
      expect(canAccessRoute('/(dashboard)/business', state).reason).toContain('Access denied');
    });

    it('should allow access if all conditions met', () => {
      const state = { isAuthenticated: true, roleStatus: { role: 'customer', needsRoleSelection: false, needsOnboarding: false, needsProfileCompletion: false }, currentSegments: [] };
      expect(canAccessRoute('/(dashboard)/customer', state).canAccess).toBe(true);
    });
  });

  describe('getDashboardRoute', () => {
    it('should return customer dashboard route', () => {
      expect(getDashboardRoute('customer')).toBe('/(dashboard)/customer');
    });

    it('should return business dashboard route', () => {
      expect(getDashboardRoute('business')).toBe('/(dashboard)/business');
    });

    it('should return null for unknown role', () => {
      expect(getDashboardRoute(null)).toBeNull();
    });
  });

  describe('isProtectedRoute', () => {
    it('should return true for protected routes', () => {
      expect(isProtectedRoute(['(dashboard)', 'customer'])).toBe(true);
      expect(isProtectedRoute(['(onboarding)', 'business-details'])).toBe(true);
    });

    it('should return false for non-protected routes', () => {
      expect(isProtectedRoute(['(auth)', 'login'])).toBe(false);
      expect(isProtectedRoute(['some-other-route'])).toBe(false);
    });
  });

  describe('isAuthRoute', () => {
    it('should return true for auth routes', () => {
      expect(isAuthRoute(['(auth)', 'login'])).toBe(true);
      expect(isAuthRoute(['(auth)', 'choose-role'])).toBe(true);
    });

    it('should return false for non-auth routes', () => {
      expect(isAuthRoute(['(dashboard)', 'customer'])).toBe(false);
      expect(isAuthRoute(['some-other-route'])).toBe(false);
    });
  });
});