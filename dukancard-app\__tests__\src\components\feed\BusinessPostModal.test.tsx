import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { BusinessPostModal } from '../../../src/components/feed/BusinessPostModal';
import { supabase } from '@/lib/supabase';
import { createBusinessPost, updateBusinessPost, deleteBusinessPost } from '@/lib/actions/businessPosts';
import { uploadBusinessPostImage } from '@/backend/supabase/services/storage/businessPostImageUploadService';
import { getSelectedProducts } from '@/lib/actions/products';
import * as ImagePicker from 'expo-image-picker';

// Mock all external dependencies
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'user123' } } })),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: { business_name: 'Test Business', logo_url: 'http://test.com/logo.png' } })),
        })),
      })),
    })),
  },
}));

jest.mock('@/lib/actions/businessPosts', () => ({
  createBusinessPost: jest.fn(),
  updateBusinessPost: jest.fn(),
  deleteBusinessPost: jest.fn(),
}));

jest.mock('@/backend/supabase/services/storage/businessPostImageUploadService', () => ({
  uploadBusinessPostImage: jest.fn(),
}));

jest.mock('@/lib/actions/products', () => ({
  getSelectedProducts: jest.fn(),
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: jest.fn(() => 'light'),
}));

jest.mock('@/backend/supabase/services/storage/imageUploadService', () => ({
  openCamera: jest.fn(),
  openImageGallery: jest.fn(),
}));

describe('BusinessPostModal', () => {
  const defaultProps = {
    visible: true,
    onClose: jest.fn(),
    businessName: 'Test Business',
    onPostCreated: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (createBusinessPost as jest.Mock).mockResolvedValue({ success: true, data: { id: 'newPost123', created_at: new Date().toISOString() } });
    (updateBusinessPost as jest.Mock).mockResolvedValue({ success: true });
    (uploadBusinessPostImage as jest.Mock).mockResolvedValue({ success: true, url: 'http://uploaded.image.com/image.jpg' });
    (getSelectedProducts as jest.Mock).mockResolvedValue({ success: true, data: [] });
  });

  it('renders correctly when visible', async () => {
    const { getByText, getByPlaceholderText } = render(<BusinessPostModal {...defaultProps} />);

    await waitFor(() => {
      expect(getByText('Create Post')).toBeTruthy();
      expect(getByPlaceholderText("What's happening at Test Business?")).toBeTruthy();
    });
  });

  it('closes the modal when back button is pressed', async () => {
    const { getByTestId } = render(<BusinessPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByTestId('back-button'));
    });

    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('handles text input change', async () => {
    const { getByPlaceholderText } = render(<BusinessPostModal {...defaultProps} />);
    const input = getByPlaceholderText("What's happening at Test Business?");

    fireEvent.changeText(input, 'Hello world');
    expect(input.props.value).toBe('Hello world');
  });

  it('handles image selection from camera', async () => {
    const mockImage = { uri: 'file://camera_image.jpg', width: 100, height: 100 };
    (ImagePicker.openCamera as jest.Mock).mockResolvedValue({ canceled: false, assets: [mockImage] });

    const { getByText, getByTestId } = render(<BusinessPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByText('Add Photo'));
      // Simulate selection from bottom sheet
      await (ImagePicker.openCamera as jest.Mock)();
    });

    await waitFor(() => {
      expect(getByTestId('post-image-preview')).toBeTruthy();
      expect(getByTestId('post-image-preview').props.source.uri).toBe(mockImage.uri);
    });
  });

  it('handles image selection from gallery', async () => {
    const mockImage = { uri: 'file://gallery_image.jpg', width: 100, height: 100 };
    (ImagePicker.openImageGallery as jest.Mock).mockResolvedValue({ canceled: false, assets: [mockImage] });

    const { getByText, getByTestId } = render(<BusinessPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByText('Add Photo'));
      // Simulate selection from bottom sheet
      await (ImagePicker.openImageGallery as jest.Mock)();
    });

    await waitFor(() => {
      expect(getByTestId('post-image-preview')).toBeTruthy();
      expect(getByTestId('post-image-preview').props.source.uri).toBe(mockImage.uri);
    });
  });

  it('removes image when remove button is pressed', async () => {
    const mockImage = { uri: 'file://camera_image.jpg', width: 100, height: 100 };
    (ImagePicker.openCamera as jest.Mock).mockResolvedValue({ canceled: false, assets: [mockImage] });

    const { getByText, getByTestId, queryByTestId } = render(<BusinessPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByText('Add Photo'));
      await (ImagePicker.openCamera as jest.Mock)();
    });

    await waitFor(() => {
      expect(getByTestId('post-image-preview')).toBeTruthy();
    });

    fireEvent.press(getByTestId('remove-image-button'));
    expect(queryByTestId('post-image-preview')).toBeNull();
  });

  it('submits post with text content', async () => {
    const { getByText, getByPlaceholderText } = render(<BusinessPostModal {...defaultProps} />);
    const input = getByPlaceholderText("What's happening at Test Business?");
    fireEvent.changeText(input, 'My new post content');

    await act(async () => {
      fireEvent.press(getByText('Post'));
    });

    expect(createBusinessPost).toHaveBeenCalledWith({
      content: 'My new post content',
      image_url: null,
      product_ids: [],
    });
    expect(defaultProps.onPostCreated).toHaveBeenCalled();
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('submits post with image and uploads it', async () => {
    const mockImage = { uri: 'file://new_image.jpg', width: 100, height: 100 };
    (ImagePicker.openCamera as jest.Mock).mockResolvedValue({ canceled: false, assets: [mockImage] });

    const { getByText } = render(<BusinessPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByText('Add Photo'));
      await (ImagePicker.openCamera as jest.Mock)();
      fireEvent.press(getByText('Post'));
    });

    expect(createBusinessPost).toHaveBeenCalledWith(expect.objectContaining({ image_url: null }));
    expect(uploadBusinessPostImage).toHaveBeenCalledWith(mockImage.uri, 'newPost123', expect.any(String));
    expect(updateBusinessPost).toHaveBeenCalledWith('newPost123', expect.objectContaining({ image_url: 'http://uploaded.image.com/image.jpg' }));
    expect(defaultProps.onPostCreated).toHaveBeenCalled();
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('shows alert if no content, image, or products are selected', async () => {
    const { getByText } = render(<BusinessPostModal {...defaultProps} />);
    const alertSpy = jest.spyOn(require('react-native').Alert, 'alert');

    await act(async () => {
      fireEvent.press(getByText('Post'));
    });

    expect(alertSpy).toHaveBeenCalledWith(
      'Error',
      'Please add some content, an image, or select products for your post.'
    );
    alertSpy.mockRestore();
  });

  it('should rollback business post creation when image upload fails', async () => {
    const mockCreateBusinessPost = createBusinessPost as jest.MockedFunction<typeof createBusinessPost>;
    const mockUploadBusinessPostImage = uploadBusinessPostImage as jest.MockedFunction<typeof uploadBusinessPostImage>;
    const mockDeleteBusinessPost = deleteBusinessPost as jest.MockedFunction<typeof deleteBusinessPost>;

    mockCreateBusinessPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockUploadBusinessPostImage.mockResolvedValue({
      success: false,
      error: 'Business image upload failed'
    });

    mockDeleteBusinessPost.mockResolvedValue({
      success: true,
      message: 'Post deleted'
    });

    const alertSpy = jest.spyOn(require('react-native').Alert, 'alert');

    const { getByPlaceholderText, getByText } = render(
      <BusinessPostModal {...defaultProps} />
    );

    fireEvent.changeText(getByPlaceholderText("What's on your mind?"), 'Business post with image');

    await act(async () => {
      fireEvent.press(getByText('Post'));
    });

    await waitFor(() => {
      expect(mockCreateBusinessPost).toHaveBeenCalled();
      expect(mockDeleteBusinessPost).toHaveBeenCalledWith('post-123');
      expect(alertSpy).toHaveBeenCalledWith('Error', 'Failed to upload image. Please try again.');
    });

    alertSpy.mockRestore();
  });

  it('should handle compression errors with rollback', async () => {
    const mockCreateBusinessPost = createBusinessPost as jest.MockedFunction<typeof createBusinessPost>;
    const mockDeleteBusinessPost = deleteBusinessPost as jest.MockedFunction<typeof deleteBusinessPost>;

    mockCreateBusinessPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    mockDeleteBusinessPost.mockResolvedValue({
      success: true,
      message: 'Post deleted'
    });

    // Mock compression failure
    jest.doMock('@/src/utils/client-image-compression', () => ({
      compressFileUltraAggressive: jest.fn().mockRejectedValue(new Error('Business compression failed'))
    }));

    const alertSpy = jest.spyOn(require('react-native').Alert, 'alert');

    const { getByPlaceholderText, getByText } = render(
      <BusinessPostModal {...defaultProps} />
    );

    fireEvent.changeText(getByPlaceholderText("What's on your mind?"), 'Business post with image');

    await act(async () => {
      fireEvent.press(getByText('Post'));
    });

    await waitFor(() => {
      expect(mockDeleteBusinessPost).toHaveBeenCalledWith('post-123');
      expect(alertSpy).toHaveBeenCalledWith('Error', 'Failed to process image. Please try again.');
    });

    alertSpy.mockRestore();
  });
});
