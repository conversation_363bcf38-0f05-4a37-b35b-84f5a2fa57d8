import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { RoleCard } from '@/src/components/ui/RoleCard';
import { useTheme } from '@/src/hooks/useTheme';
import { Briefcase, ChevronRight, User } from 'lucide-react-native';

// Mock necessary modules
jest.mock('@/src/hooks/useTheme');
jest.mock('lucide-react-native', () => ({
  Briefcase: jest.fn(() => null),
  ChevronRight: jest.fn(() => null),
  User: jest.fn(() => null),
}));

describe('RoleCard', () => {
  const mockOnPress = jest.fn();
  const mockTheme = {
    isDark: false,
    colors: {
      border: '#E5E5E5',
      primary: '#D4AF37',
      textPrimary: '#000000',
      textSecondary: '#666666',
    },
    shadows: {
      md: { shadowOpacity: 0.2, elevation: 4 },
      sm: { shadowOpacity: 0.1, elevation: 2 },
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
    (Briefcase as jest.Mock).mockImplementation(() => <Text testID="briefcase-icon">BriefcaseIcon</Text>);
    (ChevronRight as jest.Mock).mockImplementation(() => <Text testID="chevron-right-icon">ChevronRightIcon</Text>);
    (User as jest.Mock).mockImplementation(() => <Text testID="user-icon">UserIcon</Text>);
  });

  it('renders correctly with user role and not selected', () => {
    const userRole = {
      title: 'Customer',
      description: 'Browse and purchase products',
      icon: 'user',
    };
    render(<RoleCard role={userRole} selected={false} onPress={mockOnPress} />);

    expect(screen.getByText('Customer')).toBeOnTheScreen();
    expect(screen.getByText('Browse and purchase products')).toBeOnTheScreen();
    expect(screen.getByTestId('user-icon')).toBeOnTheScreen();
    expect(screen.getByTestId('chevron-right-icon')).toBeOnTheScreen();
    expect(screen.queryByTestId('briefcase-icon')).toBeNull();
  });

  it('renders correctly with business role and selected', () => {
    const businessRole = {
      title: 'Business Owner',
      description: 'Manage your store and products',
      icon: 'briefcase',
    };
    render(<RoleCard role={businessRole} selected={true} onPress={mockOnPress} />);

    expect(screen.getByText('Business Owner')).toBeOnTheScreen();
    expect(screen.getByText('Manage your store and products')).toBeOnTheScreen();
    expect(screen.getByTestId('briefcase-icon')).toBeOnTheScreen();
    expect(screen.getByTestId('chevron-right-icon')).toBeOnTheScreen();
    expect(screen.queryByTestId('user-icon')).toBeNull();
  });

  it('calls onPress when not disabled', () => {
    const userRole = {
      title: 'Customer',
      description: 'Browse and purchase products',
      icon: 'user',
    };
    render(<RoleCard role={userRole} selected={false} onPress={mockOnPress} />);

    fireEvent.press(screen.getByText('Customer'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('does not call onPress when disabled', () => {
    const userRole = {
      title: 'Customer',
      description: 'Browse and purchase products',
      icon: 'user',
    };
    render(<RoleCard role={userRole} selected={false} onPress={mockOnPress} disabled={true} />);

    fireEvent.press(screen.getByText('Customer'));
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('applies selected styles when selected is true', () => {
    const userRole = {
      title: 'Customer',
      description: 'Browse and purchase products',
      icon: 'user',
    };
    render(<RoleCard role={userRole} selected={true} onPress={mockOnPress} />);

    const container = screen.getByTestId('role-card-container'); // Assuming a testID for the outer View
    expect(container.props.style).toContainEqual({
      borderWidth: 2,
      borderColor: mockTheme.colors.border,
    });
    expect(container.props.style).toContainEqual(mockTheme.shadows.md);
  });

  it('applies disabled styles when disabled is true', () => {
    const userRole = {
      title: 'Customer',
      description: 'Browse and purchase products',
      icon: 'user',
    };
    render(<RoleCard role={userRole} selected={false} onPress={mockOnPress} disabled={true} />);

    const container = screen.getByTestId('role-card-container');
    expect(container.props.style).toContainEqual({
      opacity: 0.6,
    });
  });

  it('applies dark mode styles', () => {
    (useTheme as jest.Mock).mockReturnValue({
      ...mockTheme,
      isDark: true,
      colors: {
        ...mockTheme.colors,
        border: '#333333',
        textPrimary: '#FFFFFF',
        textSecondary: '#999999',
      },
    });

    const userRole = {
      title: 'Customer',
      description: 'Browse and purchase products',
      icon: 'user',
    };
    render(<RoleCard role={userRole} selected={false} onPress={mockOnPress} />);

    expect(screen.getByText('Customer').props.style).toContainEqual({
      color: '#FFFFFF',
    });
    expect(screen.getByText('Browse and purchase products').props.style).toContainEqual({
      color: '#999999',
    });
  });
});