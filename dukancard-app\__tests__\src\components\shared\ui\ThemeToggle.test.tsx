import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ThemeToggle } from '../../../src/components/shared/ui/ThemeToggle';
import { useColorScheme } from '../../../src/hooks/useColorScheme';
import { Moon, Sun } from 'lucide-react-native';

// Mock the useColorScheme hook
jest.mock('../../../src/hooks/useColorScheme');

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  Moon: jest.fn(() => null),
  Sun: jest.fn(() => null),
}));

describe('ThemeToggle', () => {
  beforeEach(() => {
    // Reset mocks before each test
    (useColorScheme as jest.Mock).mockClear();
    (Moon as jest.Mock).mockClear();
    (Sun as jest.Mock).mockClear();
  });

  it('renders Sun icon when color scheme is dark', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    render(<ThemeToggle />);
    expect(Sun).toHaveBeenCalled();
    expect(Moon).not.toHaveBeenCalled();
  });

  it('renders Moon icon when color scheme is light', () => {
    (useColorScheme as jest.Mock).mockReturnValue('light');
    render(<ThemeToggle />);
    expect(Moon).toHaveBeenCalled();
    expect(Sun).not.toHaveBeenCalled();
  });

  it('calls toggleTheme on press', () => {
    const mockToggleTheme = jest.fn();
    // Temporarily override the internal toggleTheme for testing purposes
    // In a real scenario, you might mock the context that provides this function
    const { getByA11yRole } = render(<ThemeToggle />);
    const button = getByA11yRole('button');
    fireEvent.press(button);
    // Since toggleTheme is a placeholder, we can't directly assert its call
    // Instead, we'd assert changes in theme if it were implemented.
    // For now, we just ensure the button is pressable.
    expect(button).toBeDefined();
  });

  it('applies dashboard variant styles', () => {
    (useColorScheme as jest.Mock).mockReturnValue('light');
    const { getByA11yRole } = render(<ThemeToggle variant="dashboard" />);
    const button = getByA11yRole('button');
    expect(button.props.style).toContainEqual({
      padding: 6,
    });
  });

  it('passes size prop to icon', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    render(<ThemeToggle size={30} />);
    expect(Sun).toHaveBeenCalledWith(expect.objectContaining({ size: 30 }), {});
  });
});