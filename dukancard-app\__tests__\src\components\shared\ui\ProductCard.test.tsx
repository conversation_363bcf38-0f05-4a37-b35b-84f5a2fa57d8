import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { ProductCard } from '../../../src/components/shared/ui/ProductCard';
import { useColorScheme } from '../../../src/hooks/useColorScheme';
import { router } from 'expo-router';
import { useLocation } from '../../../src/contexts/LocationContext';

// Mock necessary modules
jest.mock('../../../src/hooks/useColorScheme');
jest.mock('expo-router');
jest.mock('../../../src/contexts/LocationContext');
jest.mock('@/src/utils/distanceCalculation', () => ({
  calculateDistanceWithFallback: jest.fn(() => 1000), // Mock a distance of 1km
  formatDistance: jest.fn((distance) => `${distance / 1000} km`),
}));

const mockProduct = {
  id: '1',
  name: 'Test Product',
  image_url: 'https://example.com/image.jpg',
  base_price: 100,
  discounted_price: 80,
  is_available: true,
};

describe('ProductCard', () => {
  beforeEach(() => {
    // Reset mocks before each test
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (router.push as jest.Mock).mockClear();
    (useLocation as jest.Mock).mockReturnValue({ currentLocation: { latitude: 0, longitude: 0 } });
  });

  it('renders correctly with default props', () => {
    render(<ProductCard product={mockProduct} />);

    expect(screen.getByText('Test Product')).toBeOnTheScreen();
    expect(screen.getByText('₹80')).toBeOnTheScreen();
    expect(screen.getByText('₹100')).toBeOnTheScreen();
    expect(screen.getByText('20%')).toBeOnTheScreen();
  });

  it('navigates to product details on press when clickable', () => {
    render(<ProductCard product={mockProduct} isClickable={true} />);

    fireEvent.press(screen.getByText('Test Product'));
    expect(router.push).toHaveBeenCalledWith('/product/1');
  });

  it('does not navigate when not clickable', () => {
    render(<ProductCard product={mockProduct} isClickable={false} />);

    fireEvent.press(screen.getByText('Test Product'));
    expect(router.push).not.toHaveBeenCalled();
  });

  it('displays "OUT OF STOCK" when product is not available', () => {
    const unavailableProduct = { ...mockProduct, is_available: false };
    render(<ProductCard product={unavailableProduct} />);

    expect(screen.getByText('OUT OF STOCK')).toBeOnTheScreen();
  });

  it('displays placeholder image when image_url is not provided', () => {
    const productWithoutImage = { ...mockProduct, image_url: null };
    render(<ProductCard product={productWithoutImage} />);

    expect(screen.getByText('T')).toBeOnTheScreen(); // First letter of product name
  });

  it('displays distance when showDistance is true and business location is provided', () => {
    render(
      <ProductCard
        product={mockProduct}
        businessLatitude={1}
        businessLongitude={1}
        showDistance={true}
      />
    );
    expect(screen.getByText('1 km')).toBeOnTheScreen();
  });

  it('does not display distance when showDistance is false', () => {
    render(
      <ProductCard
        product={mockProduct}
        businessLatitude={1}
        businessLongitude={1}
        showDistance={false}
      />
    );
    expect(screen.queryByText('1 km')).toBeNull();
  });

  it('handles image loading error by showing placeholder', () => {
    render(<ProductCard product={mockProduct} />);
    const image = screen.getByTestId('product-image'); // Assuming you add a testID to the Image component
    fireEvent(image, 'onError');
    expect(screen.getByText('T')).toBeOnTheScreen();
  });
});