import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { NotificationPreferences } from '@/src/components/notifications/NotificationPreferences';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useAuth } from '@/src/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { Alert } from 'react-native';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: 'user123' } }),
}));

jest.mock('@/lib/supabase', () => ({
  supabase: {},
}));

jest.mock('@/src/components/shared/ui/LoadingSpinner', () => ({
  LoadingSpinner: () => <mock-loading-spinner testID="loading-spinner" />,
}));

jest.spyOn(Alert, 'alert');

describe('NotificationPreferences', () => {
  const userId = 'user123';
  const localStorageKey = `notification_preferences_${userId}`;

  const defaultPreferences = {
    id: `temp_${userId}_${Date.now()}`,
    user_id: userId,
    likes_enabled: true,
    comments_enabled: true,
    subscriptions_enabled: true,
    reviews_enabled: true,
    business_updates_enabled: true,
    email_notifications_enabled: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null); // Default to no existing preferences
  });

  it('renders loading state initially', () => {
    (AsyncStorage.getItem as jest.Mock).mockReturnValueOnce(new Promise(() => {})); // Simulate loading
    const { getByTestId, getByText } = render(<NotificationPreferences />);
    expect(getByTestId('loading-spinner')).toBeTruthy();
    expect(getByText('Loading notification preferences...')).toBeTruthy();
  });

  it('renders preferences and creates default if none exist', async () => {
    const { getByText } = render(<NotificationPreferences />);

    await waitFor(() => {
      expect(AsyncStorage.getItem).toHaveBeenCalledWith(localStorageKey);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(localStorageKey, expect.any(String));
      expect(getByText('Likes')).toBeTruthy();
      expect(getByText('Email Notifications')).toBeTruthy();
    });
  });

  it('loads existing preferences from AsyncStorage', async () => {
    const existingPrefs = { ...defaultPreferences, likes_enabled: false };
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(existingPrefs));

    const { getByText, getByA11yRole } = render(<NotificationPreferences />);

    await waitFor(() => {
      expect(AsyncStorage.getItem).toHaveBeenCalledWith(localStorageKey);
      const likesSwitch = getByA11yRole('switch', { name: 'Likes' });
      expect(likesSwitch.props.value).toBe(false);
    });
  });

  it('updates preference when switch is toggled', async () => {
    const { getByText, getByA11yRole } = render(<NotificationPreferences />);

    await waitFor(() => {
      expect(getByText('Likes')).toBeTruthy();
    });

    const likesSwitch = getByA11yRole('switch', { name: 'Likes' });
    fireEvent(likesSwitch, 'valueChange', false); // Toggle off

    await waitFor(() => {
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        localStorageKey,
        expect.stringContaining('"likes_enabled":false')
      );
    });

    fireEvent(likesSwitch, 'valueChange', true); // Toggle on

    await waitFor(() => {
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        localStorageKey,
        expect.stringContaining('"likes_enabled":true')
      );
    });
  });

  it('shows error alert if loading preferences fails', async () => {
    (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Failed to read'));
    render(<NotificationPreferences />);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to load notification preferences');
    });
  });

  it('shows error alert if updating preference fails', async () => {
    (AsyncStorage.setItem as jest.Mock).mockRejectedValue(new Error('Failed to write'));
    const { getByText, getByA11yRole } = render(<NotificationPreferences />);

    await waitFor(() => {
      expect(getByText('Likes')).toBeTruthy();
    });

    const likesSwitch = getByA11yRole('switch', { name: 'Likes' });
    fireEvent(likesSwitch, 'valueChange', false);

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to update notification preferences');
    });
  });
});
