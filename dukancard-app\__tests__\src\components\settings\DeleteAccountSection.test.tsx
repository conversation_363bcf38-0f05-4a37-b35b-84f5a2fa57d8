import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { DeleteAccountSection } from '../../../../src/components/settings/DeleteAccountSection';
import { Alert } from 'react-native';

// Mock necessary modules
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({ colors: { text: '#000', background: '#fff', primary: '#D4AF37', card: '#f0f0f0' } }),
}));

jest.mock('@/src/components/ui/Input', () => ({
  Input: jest.fn(({ value, onChangeText, placeholder, containerStyle }) => (
    <TextInput
      testID="delete-confirm-input"
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
    />
  )),
}));

jest.mock('@/src/components/ui/Button', () => ({
  Button: jest.fn(({ title, onPress, disabled, loading, variant, style, icon }) => (
    <TouchableOpacity testID={`button-${title}`} onPress={onPress} disabled={disabled}>
      <Text>{title}</Text>
      {loading && <ActivityIndicator testID="button-loading-indicator" />}
      {icon}
    </TouchableOpacity>
  )),
}));

jest.mock('@/styles/dashboard/customer/settings/delete-account', () => ({
  createDeleteAccountSectionStyles: () => ({
    container: {},
    header: {},
    titleContainer: {},
    iconContainer: {},
    titleContent: {},
    title: {},
    subtitle: {},
    warningContainer: {},
    warningContent: {},
    warningTextContainer: {},
    warningTitle: {},
    warningDescription: {},
    buttonContainer: {},
    deleteButton: {},
    modalOverlay: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.5)' },
    modalContent: { backgroundColor: 'white', padding: 20, borderRadius: 10, width: '80%' },
    modalHeader: { alignItems: 'center', marginBottom: 20 },
    modalIconContainer: { marginBottom: 10 },
    modalTitle: { fontSize: 20, fontWeight: 'bold', textAlign: 'center' },
    modalDescription: { fontSize: 14, color: 'gray', textAlign: 'center', marginTop: 5 },
    modalBody: { marginBottom: 20 },
    modalWarningContainer: { backgroundColor: '#FEF3C7', padding: 15, borderRadius: 8, marginBottom: 15 },
    modalWarningContent: { flexDirection: 'row', alignItems: 'flex-start' },
    modalWarningTextContainer: { marginLeft: 10, flex: 1 },
    modalWarningTitle: { fontWeight: 'bold', color: '#EF4444' },
    modalWarningList: { fontSize: 12, color: 'gray', marginTop: 2 },
    confirmationContainer: {},
    confirmationLabel: { fontSize: 14, marginBottom: 5 },
    confirmationHighlight: { fontWeight: 'bold' },
    confirmationInput: {},
    modalFooter: { flexDirection: 'row', justifyContent: 'space-around' },
    cancelButton: { padding: 10 },
    cancelButtonText: { color: 'blue' },
    confirmDeleteButton: {},
  }),
}));

jest.spyOn(Alert, 'alert');

describe('DeleteAccountSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const { getByText } = render(<DeleteAccountSection />);
    expect(getByText('Delete Account')).toBeTruthy();
    expect(getByText('Permanently delete your account and all associated data')).toBeTruthy();
    expect(getByText('Warning: This action cannot be undone')).toBeTruthy();
  });

  it('opens the confirmation modal when "Delete Account" button is pressed', () => {
    const { getByText, getByTestId } = render(<DeleteAccountSection />);
    fireEvent.press(getByTestId('button-Delete Account'));
    expect(getByText('Delete your account?')).toBeTruthy();
    expect(getByTestId('delete-confirm-input')).toBeTruthy();
  });

  it('closes the modal when "Cancel" is pressed', () => {
    const { getByText, getByTestId, queryByText } = render(<DeleteAccountSection />);
    fireEvent.press(getByTestId('button-Delete Account'));
    expect(getByText('Delete your account?')).toBeTruthy();

    fireEvent.press(getByText('Cancel'));
    expect(queryByText('Delete your account?')).toBeNull();
  });

  it('shows an alert if confirmation text is incorrect', async () => {
    const { getByText, getByTestId } = render(<DeleteAccountSection />);
    fireEvent.press(getByTestId('button-Delete Account'));

    const confirmInput = getByTestId('delete-confirm-input');
    fireEvent.changeText(confirmInput, 'WRONG');

    const deleteButton = getByTestId('button-Delete Account');
    fireEvent.press(deleteButton);

    expect(Alert.alert).toHaveBeenCalledWith('Error', 'Please type "DELETE" to confirm');
  });

  it('simulates account deletion process', async () => {
    const { getByText, getByTestId } = render(<DeleteAccountSection />);
    fireEvent.press(getByTestId('button-Delete Account'));

    const confirmInput = getByTestId('delete-confirm-input');
    fireEvent.changeText(confirmInput, 'DELETE');

    const deleteButton = getByTestId('button-Delete Account');
    fireEvent.press(deleteButton);

    expect(Alert.alert).toHaveBeenCalledWith(
      'Final Confirmation',
      'Are you absolutely sure you want to delete your account? This action cannot be undone.',
      expect.any(Array)
    );

    // Manually trigger the onPress of the confirm button in the second alert
    const finalConfirmButton = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    await act(async () => {
      await finalConfirmButton.onPress();
    });

    await waitFor(() => {
      expect(getByTestId('button-loading-indicator')).toBeTruthy(); // Check for loading state
    });

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Account Deleted', 'Your account has been successfully deleted.');
    });
  });

  it('disables buttons during deletion process', async () => {
    const { getByText, getByTestId } = render(<DeleteAccountSection />);
    fireEvent.press(getByTestId('button-Delete Account'));

    const confirmInput = getByTestId('delete-confirm-input');
    fireEvent.changeText(confirmInput, 'DELETE');

    const deleteButton = getByTestId('button-Delete Account');
    fireEvent.press(deleteButton);

    const finalConfirmButton = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    let resolvePromise: (value?: unknown) => void;
    (global as any)._simulateDeleteAccount = new Promise(resolve => { resolvePromise = resolve; });

    await act(async () => {
      await finalConfirmButton.onPress();
    });

    expect(getByTestId('button-Delete Account').props.disabled).toBe(true);
    expect(getByText('Cancel').props.disabled).toBe(true);

    act(() => { resolvePromise(); });

    await waitFor(() => {
      expect(getByTestId('button-Delete Account').props.disabled).toBe(false);
      expect(getByText('Cancel').props.disabled).toBe(false);
    });
  });
});
