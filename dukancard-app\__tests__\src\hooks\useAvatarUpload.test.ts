import { renderHook, act } from '@testing-library/react-hooks';
import { Alert } from 'react-native';
import {
  showImagePickerOptions,
  openCamera,
  openImagePicker,
  uploadAvatarImage,
  updateCustomerAvatarUrl,
} from '@/backend/supabase/services/storage/avatarUploadService';
import { useAvatarUpload } from '@/src/hooks/useAvatarUpload';

// Mock external modules
jest.mock('@/backend/supabase/services/storage/avatarUploadService');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
}));

describe('useAvatarUpload', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default successful mocks
    (showImagePickerOptions as jest.Mock).mockResolvedValue('camera');
    (openCamera as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/image.jpg', fileSize: 100000 }],
    });
    (openImagePicker as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/image.jpg', fileSize: 100000 }],
    });
    (uploadAvatarImage as jest.Mock).mockResolvedValue({
      success: true,
      url: 'https://example.com/uploaded.jpg',
    });
    (updateCustomerAvatarUrl as jest.Mock).mockResolvedValue({
      success: true,
    });
  });

  it('should return initial state', () => {
    const { result } = renderHook(() => useAvatarUpload());
    expect(result.current.avatarUrl).toBeNull();
    expect(result.current.uploadStatus).toBe('idle');
    expect(result.current.uploadError).toBeNull();
    expect(result.current.isUploading).toBe(false);
  });

  it('should set initial avatar URL if provided', () => {
    const { result } = renderHook(() => useAvatarUpload({ initialAvatarUrl: 'https://example.com/initial.jpg' }));
    expect(result.current.avatarUrl).toBe('https://example.com/initial.jpg');
  });

  it('should handle camera selection and successful upload', async () => {
    const { result } = renderHook(() => useAvatarUpload({ onUploadSuccess: jest.fn() }));

    await act(async () => {
      await result.current.selectAndUploadAvatar();
    });

    expect(result.current.uploadStatus).toBe('success');
    expect(result.current.avatarUrl).toBe('https://example.com/uploaded.jpg');
    expect(result.current.uploadError).toBeNull();
    expect(showImagePickerOptions).toHaveBeenCalledTimes(1);
    expect(openCamera).toHaveBeenCalledTimes(1);
    expect(uploadAvatarImage).toHaveBeenCalledWith('file://mock/image.jpg');
    expect(updateCustomerAvatarUrl).toHaveBeenCalledWith('https://example.com/uploaded.jpg');
    expect(Alert.alert).toHaveBeenCalledWith('Success', 'Your profile picture has been updated successfully!', expect.any(Array));
  });

  it('should handle gallery selection and successful upload', async () => {
    (showImagePickerOptions as jest.Mock).mockResolvedValue('gallery');
    const { result } = renderHook(() => useAvatarUpload({ onUploadSuccess: jest.fn() }));

    await act(async () => {
      await result.current.selectAndUploadAvatar();
    });

    expect(result.current.uploadStatus).toBe('success');
    expect(openImagePicker).toHaveBeenCalledTimes(1);
  });

  it('should handle image selection cancellation', async () => {
    (openCamera as jest.Mock).mockResolvedValue({ canceled: true });
    const { result } = renderHook(() => useAvatarUpload());

    await act(async () => {
      await result.current.selectAndUploadAvatar();
    });

    expect(result.current.uploadStatus).toBe('idle');
    expect(result.current.avatarUrl).toBeNull();
  });

  it('should handle upload failure', async () => {
    (uploadAvatarImage as jest.Mock).mockResolvedValue({
      success: false,
      error: 'Upload failed',
    });
    const mockOnUploadError = jest.fn();
    const { result } = renderHook(() => useAvatarUpload({ onUploadError: mockOnUploadError }));

    await act(async () => {
      await result.current.selectAndUploadAvatar();
    });

    expect(result.current.uploadStatus).toBe('error');
    expect(result.current.uploadError).toBe('Upload failed');
    expect(mockOnUploadError).toHaveBeenCalledWith('Upload failed');
    expect(Alert.alert).toHaveBeenCalledWith('Upload Failed', 'Upload failed', expect.any(Array));
  });

  it('should handle profile update failure', async () => {
    (updateCustomerAvatarUrl as jest.Mock).mockResolvedValue({
      success: false,
      error: 'Profile update failed',
    });
    const mockOnUploadError = jest.fn();
    const { result } = renderHook(() => useAvatarUpload({ onUploadError: mockOnUploadError }));

    await act(async () => {
      await result.current.selectAndUploadAvatar();
    });

    expect(result.current.uploadStatus).toBe('error');
    expect(result.current.uploadError).toBe('Profile update failed');
    expect(mockOnUploadError).toHaveBeenCalledWith('Profile update failed');
    expect(Alert.alert).toHaveBeenCalledWith('Upload Failed', 'Profile update failed', expect.any(Array));
  });

  it('should handle permission errors', async () => {
    (openCamera as jest.Mock).mockRejectedValue(new Error('Permission denied'));
    const mockOnUploadError = jest.fn();
    const { result } = renderHook(() => useAvatarUpload({ onUploadError: mockOnUploadError }));

    await act(async () => {
      await result.current.selectAndUploadAvatar();
    });

    expect(result.current.uploadStatus).toBe('error');
    expect(result.current.uploadError).toContain('Permission required');
    expect(mockOnUploadError).toHaveBeenCalledWith(expect.stringContaining('Permission required'));
  });

  it('should handle large file size', async () => {
    (openCamera as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/large_image.jpg', fileSize: 16 * 1024 * 1024 }], // 16MB
    });
    const mockOnUploadError = jest.fn();
    const { result } = renderHook(() => useAvatarUpload({ onUploadError: mockOnUploadError }));

    await act(async () => {
      await result.current.selectAndUploadAvatar();
    });

    expect(result.current.uploadStatus).toBe('error');
    expect(result.current.uploadError).toBe('Image size must be less than 15MB. Please select a smaller image.');
    expect(mockOnUploadError).toHaveBeenCalledWith('Image size must be less than 15MB. Please select a smaller image.');
    expect(uploadAvatarImage).not.toHaveBeenCalled();
  });

  it('should reset upload state', async () => {
    const { result } = renderHook(() => useAvatarUpload());

    await act(async () => {
      await result.current.selectAndUploadAvatar(); // Simulate a successful upload
    });

    expect(result.current.uploadStatus).toBe('success');

    act(() => {
      result.current.resetUpload();
    });

    expect(result.current.uploadStatus).toBe('idle');
    expect(result.current.uploadError).toBeNull();
  });
});