import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { IconSymbol } from '@/src/components/ui/IconSymbol.ios';
import { SymbolView } from 'expo-symbols';

// Mock expo-symbols
jest.mock('expo-symbols', () => ({
  SymbolView: jest.fn(() => null), // Mock SymbolView to return null
}));

describe('IconSymbol', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (SymbolView as jest.Mock).mockImplementation((props) => <Text testID="symbol-view" {...props}>SymbolView</Text>);
  });

  it('renders SymbolView with correct props', () => {
    render(<IconSymbol name="house" size={30} color="blue" weight="bold" />);

    expect(SymbolView).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'house',
        size: 30,
        color: 'blue',
        weight: 'bold',
        tintColor: 'blue',
        resizeMode: 'scaleAspectFit',
        style: expect.arrayContaining([
          expect.objectContaining({
            width: 30,
            height: 30,
          }),
        ]),
      }),
      {}
    );
  });

  it('applies default size and weight when not provided', () => {
    render(<IconSymbol name="gear" color="red" />);

    expect(SymbolView).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'gear',
        size: 24,
        color: 'red',
        weight: 'regular',
        tintColor: 'red',
        style: expect.arrayContaining([
          expect.objectContaining({
            width: 24,
            height: 24,
          }),
        ]),
      }),
      {}
    );
  });

  it('merges custom style prop', () => {
    render(<IconSymbol name="heart" color="pink" style={{ opacity: 0.5 }} />);

    expect(SymbolView).toHaveBeenCalledWith(
      expect.objectContaining({
        style: expect.arrayContaining([
          expect.objectContaining({
            width: 24,
            height: 24,
          }),
          expect.objectContaining({
            opacity: 0.5,
          }),
        ]),
      }),
      {}
    );
  });
});