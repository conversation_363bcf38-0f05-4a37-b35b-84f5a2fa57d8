import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import VariantForm from '@/src/components/modals/business/components/VariantForm';
import { useForm } from 'react-hook-form';
import { useTheme } from '@/src/hooks/useTheme';
import { useToast } from '@/src/components/ui/Toast';
import * as ImagePicker from 'expo-image-picker';
import { compressImageUltraAggressive, toBase64DataUrl } from '@/src/utils/imageCompression';
import { getAllVariantTypes, getPredefinedOptionsForType } from '@/src/constants/predefinedVariants';

// Mock dependencies
jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: (fn: any) => fn,
    setValue: jest.fn(),
    watch: jest.fn(() => 'physical'),
    formState: { errors: {} },
  }),
}));

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
      textPrimary: '#000',
      textSecondary: '#888',
      mutedForeground: '#aaa',
      background: '#fff',
      border: '#ccc',
      success: 'green',
      destructive: 'red',
      muted: '#eee',
    },
    borderRadius: { full: 999, lg: 8 },
    shadows: { sm: {} },
    isDark: false,
  }),
}));

jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
  }),
}));

jest.mock('expo-image-picker', () => ({
  launchCameraAsync: jest.fn(),
  launchImageLibraryAsync: jest.fn(),
  MediaTypeOptions: { Images: 'Images' },
}));

jest.mock('@/src/utils/imageCompression', () => ({
  compressImageUltraAggressive: jest.fn(),
  toBase64DataUrl: jest.fn((base64) => `data:image/jpeg;base64,${base64}`),
}));

jest.mock('@/src/constants/predefinedVariants', () => ({
  getAllVariantTypes: jest.fn(() => [
    { id: '1', name: 'Color', display_name: 'Color' },
    { id: '2', name: 'Size', display_name: 'Size' },
  ]),
  getPredefinedOptionsForType: jest.fn((type) => {
    if (type === 'Color') {
      return [{ label: 'Red', value: 'red', color_code: '#FF0000' }];
    }
    return [];
  }),
}));

jest.mock('@/src/components/pickers/ImagePickerBottomSheet', () => ({
  __esModule: true,
  default: React.forwardRef(({ onCameraPress, onGalleryPress }: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      present: jest.fn(),
    }));
    return (
      <mock-image-picker-bottom-sheet testID="image-picker-sheet">
        <button onClick={onCameraPress}>Camera</button>
        <button onClick={onGalleryPress}>Gallery</button>
      </mock-image-picker-bottom-sheet>
    );
  }),
}));

jest.mock('@/src/components/pickers/VariantTypeBottomSheet', () => ({
  __esModule: true,
  default: React.forwardRef(({ onSelect }: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      open: jest.fn(),
    }));
    return (
      <mock-variant-type-sheet testID="variant-type-sheet">
        <button onClick={() => onSelect({ name: 'Material', display_name: 'Material' })}>Material</button>
      </mock-variant-type-sheet>
    );
  }),
}));

jest.mock('@/src/components/pickers/ColorPickerBottomSheet', () => ({
  __esModule: true,
  default: React.forwardRef(({ onSelect }: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      open: jest.fn(),
    }));
    return (
      <mock-color-picker-sheet testID="color-picker-sheet">
        <button onClick={() => onSelect({ value: 'blue', color_code: '#0000FF' })}>Blue</button>
      </mock-color-picker-sheet>
    );
  }),
}));

describe('VariantForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  const defaultProps = {
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
    loading: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (compressImageUltraAggressive as jest.Mock).mockResolvedValue({
      base64: 'mockBase64',
      size: 50000,
    });
  });

  it('renders correctly for adding a new variant', () => {
    const { getByText, getByPlaceholderText } = render(<VariantForm {...defaultProps} />);
    expect(getByText('Add Variant')).toBeTruthy();
    expect(getByPlaceholderText('e.g., Red Large, 64GB Blue, Cotton Medium')).toBeTruthy();
  });

  it('renders correctly for editing an existing variant', () => {
    const mockVariant = {
      variant_name: 'Existing Variant',
      variant_values: { Color: 'red' },
      base_price: 100,
      is_available: true,
      images: ['http://example.com/img.jpg'],
    };
    const { getByText, getByDisplayValue } = render(
      <VariantForm {...defaultProps} variant={mockVariant} />
    );
    expect(getByText('Edit Variant')).toBeTruthy();
    expect(getByDisplayValue('Existing Variant')).toBeTruthy();
  });

  it('handles variant name input', () => {
    const { getByPlaceholderText } = render(<VariantForm {...defaultProps} />);
    const input = getByPlaceholderText('e.g., Red Large, 64GB Blue, Cotton Medium');
    fireEvent.changeText(input, 'New Variant Name');
    expect(input.props.value).toBe('New Variant Name');
  });

  it('adds a new variant property', async () => {
    const { getByText } = render(<VariantForm {...defaultProps} />);
    fireEvent.press(getByText('Add Property'));
    await waitFor(() => {
      expect(getByText('Color')).toBeTruthy();
    });
  });

  it('removes a variant property', async () => {
    const mockVariant = {
      variant_name: 'Existing Variant',
      variant_values: { Color: 'red' },
      base_price: 100,
      is_available: true,
      images: ['http://example.com/img.jpg'],
    };
    const { getByText, queryByText } = render(
      <VariantForm {...defaultProps} variant={mockVariant} />
    );

    await waitFor(() => {
      expect(getByText('Color')).toBeTruthy();
    });

    fireEvent.press(getByText('Remove')); // Assuming a remove button with this text
    expect(queryByText('Color')).toBeNull();
  });

  it('handles image selection', async () => {
    const { getByText, getByTestId } = render(<VariantForm {...defaultProps} />);
    (ImagePicker.launchImageLibraryAsync as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://test.jpg' }],
    });

    fireEvent.press(getByText('Add Images'));
    await act(async () => {
      fireEvent.press(getByTestId('image-picker-sheet').findByText('Gallery'));
    });

    await waitFor(() => {
      expect(compressImageUltraAggressive).toHaveBeenCalled();
      expect(useToast().success).toHaveBeenCalledWith('Success', '1 image(s) added successfully');
    });
  });

  it('submits the form with variant data', async () => {
    const { getByText, getByPlaceholderText } = render(<VariantForm {...defaultProps} />);

    fireEvent.changeText(getByPlaceholderText('e.g., Red Large, 64GB Blue, Cotton Medium'), 'My Variant');
    fireEvent.changeText(getByPlaceholderText('0.00'), '150');

    fireEvent.press(getByText('Add Variant'));

    expect(mockOnSubmit).toHaveBeenCalledWith(expect.objectContaining({
      variant_name: 'My Variant',
      base_price: 150,
      variant_values: { Color: '' }, // Default added variant type
    }));
  });
});
