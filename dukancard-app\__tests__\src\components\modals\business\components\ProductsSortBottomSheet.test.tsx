import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ProductsSortBottomSheet, {
  ProductsSortBottomSheetRef,
} from '@/src/components/modals/business/components/ProductsSortBottomSheet';
import { useTheme } from '@/src/hooks/useTheme';

// Mock dependencies
jest.mock('@gorhom/bottom-sheet', () => {
  const RN = jest.requireActual('react-native');
  const React = jest.requireActual('react');
  return {
    __esModule: true,
    default: React.forwardRef(({ children }: any, ref: any) => {
      React.useImperativeHandle(ref, () => ({
        expand: jest.fn(),
        close: jest.fn(),
      }));
      return <RN.View testID="bottom-sheet">{children}</RN.View>;
    }),
    BottomSheetView: RN.View,
    BottomSheetFlatList: RN.FlatList,
  };
});

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      textPrimary: '#000',
      textSecondary: '#888',
      border: '#ccc',
      cardBackground: '#f0f0f0',
    },
    isDark: false,
  }),
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({
    bottom: 0,
  }),
}));

describe('ProductsSortBottomSheet', () => {
  const mockOnSortSelect = jest.fn();
  const defaultProps = {
    sortBy: 'newest',
    onSortSelect: mockOnSortSelect,
  };

  let bottomSheetRef: React.RefObject<ProductsSortBottomSheetRef>;

  beforeEach(() => {
    jest.clearAllMocks();
    bottomSheetRef = React.createRef<ProductsSortBottomSheetRef>();
  });

  it('renders correctly with default sort option selected', () => {
    const { getByText } = render(
      <ProductsSortBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    expect(getByText('Sort Products')).toBeTruthy();
    expect(getByText('Newest First')).toBeTruthy();
  });

  it('calls onSortSelect when a sort option is pressed', () => {
    const { getByText } = render(
      <ProductsSortBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    fireEvent.press(getByText('Name (A-Z)'));
    expect(mockOnSortSelect).toHaveBeenCalledWith('name_asc');
  });

  it('calls dismiss when close button is pressed', () => {
    const { getByTestId } = render(
      <ProductsSortBottomSheet ref={bottomSheetRef} {...defaultProps} />
    );

    fireEvent.press(getByTestId('close-button'));
    expect(bottomSheetRef.current?.close).toHaveBeenCalled();
  });
});
