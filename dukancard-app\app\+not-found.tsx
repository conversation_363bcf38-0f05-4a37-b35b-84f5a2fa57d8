import { Link, Stack } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { createNotFoundStyles } from '@/styles/not-found-styles';
import { useTheme } from '@/hooks/useTheme';

export default function NotFoundScreen({ styles: customStyles }: { styles?: any }) {
  const theme = useTheme();
  const styles = customStyles || createNotFoundStyles(theme);

  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <ThemedView style={styles.container}>
        <ThemedText type="title">This screen does not exist.</ThemedText>
        <Link href="/" style={styles.link}>
          <ThemedText type="link">Go to home screen!</ThemedText>
        </Link>
      </ThemedView>
    </>
  );
}


