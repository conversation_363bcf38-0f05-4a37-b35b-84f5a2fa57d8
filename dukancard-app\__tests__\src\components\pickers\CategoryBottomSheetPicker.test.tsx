import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import CategoryBottomSheetPicker, {
  CategoryBottomSheetPickerRef,
} from '@/src/components/pickers/CategoryBottomSheetPicker';
import { useTheme } from '@/src/hooks/useTheme';
import { BUSINESS_CATEGORIES } from '@/lib/config/categories';

// Mock dependencies
jest.mock('@gorhom/bottom-sheet', () => {
  const RN = jest.requireActual('react-native');
  const React = jest.requireActual('react');
  return {
    __esModule: true,
    default: React.forwardRef(({ children }: any, ref: any) => {
      React.useImperativeHandle(ref, () => ({
        expand: jest.fn(),
        close: jest.fn(),
      }));
      return <RN.View testID="bottom-sheet">{children}</RN.View>;
    }),
    BottomSheetView: RN.View,
    BottomSheetFlatList: RN.FlatList,
    BottomSheetTextInput: RN.TextInput,
  };
});

jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
      textPrimary: '#000',
      textSecondary: '#888',
      border: '#ccc',
      background: '#fff',
      destructive: '#f00',
    },
    isDark: false,
  }),
}));

jest.mock('@/lib/config/categories', () => ({
  BUSINESS_CATEGORIES: [
    { name: 'Restaurant', description: 'Food and dining' },
    { name: 'Retail', description: 'Shopping and goods' },
    { name: 'Services', description: 'Professional services' },
  ],
}));

describe('CategoryBottomSheetPicker', () => {
  const mockOnCategorySelect = jest.fn();
  const defaultProps = {
    onCategorySelect: mockOnCategorySelect,
  };

  let bottomSheetRef: React.RefObject<CategoryBottomSheetPickerRef>;

  beforeEach(() => {
    jest.clearAllMocks();
    bottomSheetRef = React.createRef<CategoryBottomSheetPickerRef>();
  });

  it('renders correctly with categories', () => {
    const { getByText, getByPlaceholderText } = render(
      <CategoryBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    expect(getByText('Select Category')).toBeTruthy();
    expect(getByPlaceholderText('Search categories...')).toBeTruthy();
    expect(getByText('Restaurant')).toBeTruthy();
    expect(getByText('Retail')).toBeTruthy();
  });

  it('calls onCategorySelect and closes on category press', async () => {
    const { getByText } = render(
      <CategoryBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    fireEvent.press(getByText('Restaurant'));
    expect(mockOnCategorySelect).toHaveBeenCalledWith('Restaurant');
    expect(bottomSheetRef.current?.close).toHaveBeenCalled();
  });

  it('filters categories based on search query', () => {
    const { getByPlaceholderText, queryByText } = render(
      <CategoryBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    fireEvent.changeText(getByPlaceholderText('Search categories...'), 'rest');

    expect(getByText('Restaurant')).toBeTruthy();
    expect(queryByText('Retail')).toBeNull();
  });

  it('clears search query when clear button is pressed', () => {
    const { getByPlaceholderText, getByTestId } = render(
      <CategoryBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    fireEvent.changeText(getByPlaceholderText('Search categories...'), 'test');
    fireEvent.press(getByTestId('clear-search-button'));

    expect(getByPlaceholderText('Search categories...').props.value).toBe('');
  });

  it('calls onCategorySelect with null when Clear All Categories is pressed', () => {
    const { getByText } = render(
      <CategoryBottomSheetPicker ref={bottomSheetRef} {...defaultProps} selectedCategory="Restaurant" />
    );

    fireEvent.press(getByText('Clear All Categories'));
    expect(mockOnCategorySelect).toHaveBeenCalledWith(null);
    expect(bottomSheetRef.current?.close).toHaveBeenCalled();
  });

  it('shows empty state when no categories match search', () => {
    const { getByPlaceholderText, getByText } = render(
      <CategoryBottomSheetPicker ref={bottomSheetRef} {...defaultProps} />
    );

    fireEvent.changeText(getByPlaceholderText('Search categories...'), 'xyz');

    expect(getByText('No categories found')).toBeTruthy();
    expect(getByText('No categories match "xyz"')).toBeTruthy();
  });
});
