import React from 'react';
import { render, screen } from '@testing-library/react-native';
import BlurTabBarBackground, { useBottomTabOverflow } from '@/src/components/ui/TabBarBackground.ios';
import { BlurView } from 'expo-blur';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';

// Mock necessary modules
jest.mock('expo-blur', () => ({
  BlurView: 'BlurView', // Mock BlurView as a simple string
}));
jest.mock('@react-navigation/bottom-tabs', () => ({
  useBottomTabBarHeight: jest.fn(),
}));

describe('BlurTabBarBackground', () => {
  it('renders BlurView with correct props', () => {
    render(<BlurTabBarBackground />);
    expect(screen.getByTestId('blur-view')).toBeOnTheScreen();
    expect(screen.getByTestId('blur-view').props.tint).toBe('systemChromeMaterial');
    expect(screen.getByTestId('blur-view').props.intensity).toBe(100);
  });
});

describe('useBottomTabOverflow', () => {
  it('returns the result of useBottomTabBarHeight', () => {
    (useBottomTabBarHeight as jest.Mock).mockReturnValue(50);
    const height = useBottomTabOverflow();
    expect(height).toBe(50);
    expect(useBottomTabBarHeight).toHaveBeenCalledTimes(1);
  });
});