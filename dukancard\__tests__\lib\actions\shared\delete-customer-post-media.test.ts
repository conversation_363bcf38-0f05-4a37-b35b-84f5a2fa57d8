import { deleteCustomerPostMedia } from '@/lib/actions/shared/delete-customer-post-media';
import { createAdminClient } from '@/utils/supabase/admin';
import { getPostFolderPath } from '@/lib/utils/storage-paths';

// Mock dependencies
jest.mock('@/utils/supabase/admin');
jest.mock('@/lib/utils/storage-paths');

const mockCreateAdminClient = createAdminClient as jest.MockedFunction<typeof createAdminClient>;
const mockGetPostFolderPath = getPostFolderPath as jest.MockedFunction<typeof getPostFolderPath>;

describe('deleteCustomerPostMedia', () => {
  const mockUserId = 'user-123';
  const mockPostId = 'post-123';
  const mockCreatedAt = '2024-01-01T00:00:00Z';
  const mockPostFolderPath = 'users/us/er/user-123/posts/2024/01/post-123';

  let mockAdminSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock admin supabase client
    mockAdminSupabase = {
      storage: {
        from: jest.fn().mockReturnValue({
          list: jest.fn(),
          remove: jest.fn()
        })
      }
    };

    mockCreateAdminClient.mockReturnValue(mockAdminSupabase);
    mockGetPostFolderPath.mockReturnValue(mockPostFolderPath);
  });

  it('should delete customer post media successfully', async () => {
    const mockFiles = [
      { name: 'image_0_1234567890.webp' },
      { name: 'image_1_1234567891.webp' }
    ];

    mockAdminSupabase.storage.from().list.mockResolvedValue({
      data: mockFiles,
      error: null
    });

    mockAdminSupabase.storage.from().remove.mockResolvedValue({
      error: null
    });

    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);

    expect(result).toEqual({ success: true });
    expect(mockGetPostFolderPath).toHaveBeenCalledWith(mockUserId, mockPostId, mockCreatedAt);
    expect(mockAdminSupabase.storage.from).toHaveBeenCalledWith('customers');
    expect(mockAdminSupabase.storage.from().list).toHaveBeenCalledWith(mockPostFolderPath, {
      limit: 1000,
      sortBy: { column: 'name', order: 'asc' }
    });
    expect(mockAdminSupabase.storage.from().remove).toHaveBeenCalledWith([
      `${mockPostFolderPath}/image_0_1234567890.webp`,
      `${mockPostFolderPath}/image_1_1234567891.webp`
    ]);
  });

  it('should handle empty folder successfully', async () => {
    mockAdminSupabase.storage.from().list.mockResolvedValue({
      data: [],
      error: null
    });

    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);

    expect(result).toEqual({ success: true });
    expect(mockAdminSupabase.storage.from().remove).not.toHaveBeenCalled();
  });

  it('should handle null files successfully', async () => {
    mockAdminSupabase.storage.from().list.mockResolvedValue({
      data: null,
      error: null
    });

    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);

    expect(result).toEqual({ success: true });
    expect(mockAdminSupabase.storage.from().remove).not.toHaveBeenCalled();
  });

  it('should return error when listing files fails', async () => {
    mockAdminSupabase.storage.from().list.mockResolvedValue({
      data: null,
      error: { message: 'Failed to list files' }
    });

    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);

    expect(result).toEqual({
      success: false,
      error: 'Failed to list customer post folder: Failed to list files'
    });
  });

  it('should return error when removing files fails', async () => {
    const mockFiles = [{ name: 'image_0_1234567890.webp' }];

    mockAdminSupabase.storage.from().list.mockResolvedValue({
      data: mockFiles,
      error: null
    });

    mockAdminSupabase.storage.from().remove.mockResolvedValue({
      error: { message: 'Failed to remove files' }
    });

    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);

    expect(result).toEqual({
      success: false,
      error: 'Failed to delete customer post folder: Failed to remove files'
    });
  });

  it('should handle unexpected errors gracefully', async () => {
    mockAdminSupabase.storage.from().list.mockRejectedValue(new Error('Unexpected error'));

    const result = await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);

    expect(result).toEqual({
      success: false,
      error: 'An unexpected error occurred while deleting customer post folder.'
    });
  });

  it('should use correct bucket name for customers', async () => {
    mockAdminSupabase.storage.from().list.mockResolvedValue({
      data: [],
      error: null
    });

    await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);

    expect(mockAdminSupabase.storage.from).toHaveBeenCalledWith('customers');
  });

  it('should construct correct file paths for deletion', async () => {
    const mockFiles = [
      { name: 'image_0_1234567890.webp' },
      { name: 'image_1_1234567891.webp' },
      { name: 'thumbnail.jpg' }
    ];

    mockAdminSupabase.storage.from().list.mockResolvedValue({
      data: mockFiles,
      error: null
    });

    mockAdminSupabase.storage.from().remove.mockResolvedValue({
      error: null
    });

    await deleteCustomerPostMedia(mockUserId, mockPostId, mockCreatedAt);

    expect(mockAdminSupabase.storage.from().remove).toHaveBeenCalledWith([
      `${mockPostFolderPath}/image_0_1234567890.webp`,
      `${mockPostFolderPath}/image_1_1234567891.webp`,
      `${mockPostFolderPath}/thumbnail.jpg`
    ]);
  });
});
