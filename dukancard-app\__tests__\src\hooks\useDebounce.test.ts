import { renderHook, act } from '@testing-library/react-hooks';
import { useDebounce, useDebounceCallback } from '@/src/hooks/useDebounce';

describe('useDebounce', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('should debounce the value', () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'initial', delay: 500 },
    });

    expect(result.current).toBe('initial');

    rerender({ value: 'first change', delay: 500 });
    expect(result.current).toBe('initial'); // Value should not change immediately

    jest.advanceTimersByTime(250);
    expect(result.current).toBe('initial'); // Still not changed

    jest.advanceTimersByTime(250);
    expect(result.current).toBe('first change'); // Value should update after delay

    rerender({ value: 'second change', delay: 500 });
    expect(result.current).toBe('first change');

    jest.advanceTimersByTime(500);
    expect(result.current).toBe('second change');
  });

  it('should handle rapid changes correctly', () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'a', delay: 100 },
    });

    expect(result.current).toBe('a');

    rerender({ value: 'b', delay: 100 });
    jest.advanceTimersByTime(50);
    rerender({ value: 'c', delay: 100 });
    jest.advanceTimersByTime(50);
    rerender({ value: 'd', delay: 100 });

    expect(result.current).toBe('a'); // Still 'a' as timers are reset

    jest.advanceTimersByTime(100);
    expect(result.current).toBe('d'); // Only the last value should be debounced
  });

  it('should not update if value is the same', () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'same', delay: 500 },
    });

    expect(result.current).toBe('same');

    rerender({ value: 'same', delay: 500 });
    jest.advanceTimersByTime(500);
    expect(result.current).toBe('same'); // Should remain 'same'
  });
});

describe('useDebounceCallback', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('should debounce the callback function', () => {
    const mockCallback = jest.fn();
    const { result } = renderHook(() => useDebounceCallback(mockCallback, 500));

    act(() => {
      result.current();
      result.current();
      result.current();
    });

    expect(mockCallback).not.toHaveBeenCalled();

    jest.advanceTimersByTime(500);
    expect(mockCallback).toHaveBeenCalledTimes(1);
  });

  it('should pass arguments to the debounced callback', () => {
    const mockCallback = jest.fn();
    const { result } = renderHook(() => useDebounceCallback(mockCallback, 500));

    act(() => {
      result.current(1, 'test');
    });

    jest.advanceTimersByTime(500);
    expect(mockCallback).toHaveBeenCalledWith(1, 'test');
  });

  it('should cancel previous calls if a new call is made within the delay', () => {
    const mockCallback = jest.fn();
    const { result } = renderHook(() => useDebounceCallback(mockCallback, 500));

    act(() => {
      result.current();
      jest.advanceTimersByTime(200);
      result.current();
      jest.advanceTimersByTime(200);
      result.current();
    });

    jest.advanceTimersByTime(500);
    expect(mockCallback).toHaveBeenCalledTimes(1);
  });

  it('should clear timeout on unmount', () => {
    const mockCallback = jest.fn();
    const { result, unmount } = renderHook(() => useDebounceCallback(mockCallback, 500));

    act(() => {
      result.current();
    });

    expect(mockCallback).not.toHaveBeenCalled();

    unmount();
    jest.runAllTimers(); // Try to run timers after unmount
    expect(mockCallback).not.toHaveBeenCalled(); // Should not be called
  });
});