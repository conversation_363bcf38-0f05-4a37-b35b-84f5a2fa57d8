import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { PasswordManagementSection } from '../../../../src/components/settings/PasswordManagementSection';
import { Alert } from 'react-native';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/backend/supabase/services/common/settingsService', () => ({
  updatePassword: jest.fn(),
}));

jest.mock('@/src/components/ui/Input', () => ({
  Input: jest.fn(({ value, onChangeText, placeholder, error, type, leftIcon, editable }) => (
    <TextInput
      testID={`input-${placeholder}`}
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      secureTextEntry={type === 'password'}
      editable={editable}
    />
  )),
}));

jest.mock('@/src/components/ui/Button', () => ({
  Button: jest.fn(({ title, onPress, disabled, variant, icon }) => (
    <TouchableOpacity testID={`button-${title}`} onPress={onPress} disabled={disabled}>
      <Text>{title}</Text>
      {icon}
    </TouchableOpacity>
  )),
}));

jest.spyOn(Alert, 'alert');

describe('PasswordManagementSection', () => {
  const mockOnPasswordUpdated = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    require('@/backend/supabase/services/common/settingsService').updatePassword.mockReset();
  });

  it('renders correctly for Google user', () => {
    const { getByText } = render(
      <PasswordManagementSection registrationType="google" />
    );
    expect(getByText('Password Management')).toBeTruthy();
    expect(getByText('You signed up with Google. Password management is handled by your Google account.')).toBeTruthy();
    expect(getByText('This will permanently delete:')).toBeFalsy(); // Should not show form fields
  });

  it('renders correctly for non-Google user', () => {
    const { getByText, getByPlaceholderText } = render(
      <PasswordManagementSection registrationType="email" />
    );
    expect(getByText('Password Management')).toBeTruthy();
    expect(getByText('Change your account password for enhanced security.')).toBeTruthy();
    expect(getByPlaceholderText('Enter your current password')).toBeTruthy();
    expect(getByPlaceholderText('Enter your new password')).toBeTruthy();
    expect(getByPlaceholderText('Confirm your new password')).toBeTruthy();
  });

  it('validates empty fields on submit', async () => {
    const { getByTestId, getByText } = render(
      <PasswordManagementSection registrationType="email" />
    );
    const updateButton = getByTestId('button-Update Password');

    fireEvent.press(updateButton);

    await waitFor(() => {
      expect(getByText('Current password is required')).toBeTruthy();
      expect(getByText('Password is required')).toBeTruthy();
      expect(getByText('Please confirm your new password')).toBeTruthy();
    });
  });

  it('validates new password strength', async () => {
    const { getByTestId, getByText } = render(
      <PasswordManagementSection registrationType="email" />
    );
    const newPasswordInput = getByTestId('input-Enter your new password');

    fireEvent.changeText(newPasswordInput, 'short');
    expect(getByText('Weak')).toBeTruthy();

    fireEvent.changeText(newPasswordInput, 'shortP');
    expect(getByText('Fair')).toBeTruthy();

    fireEvent.changeText(newPasswordInput, 'shortP1');
    expect(getByText('Good')).toBeTruthy();

    fireEvent.changeText(newPasswordInput, 'StrongP@ss1');
    expect(getByText('Strong')).toBeTruthy();

    fireEvent.changeText(newPasswordInput, 'password');
    expect(getByText('Password must contain at least one uppercase letter')).toBeTruthy();
  });

  it('validates new password and confirm password match', async () => {
    const { getByTestId, getByText } = render(
      <PasswordManagementSection registrationType="email" />
    );
    const newPasswordInput = getByTestId('input-Enter your new password');
    const confirmPasswordInput = getByTestId('input-Confirm your new password');

    fireEvent.changeText(newPasswordInput, 'Password123!');
    fireEvent.changeText(confirmPasswordInput, 'Password123');

    fireEvent.press(getByTestId('button-Update Password'));

    await waitFor(() => {
      expect(getByText('Passwords do not match')).toBeTruthy();
    });
  });

  it('submits form successfully', async () => {
    require('@/backend/supabase/services/common/settingsService').updatePassword.mockResolvedValue({
      success: true
    });

    const { getByTestId } = render(
      <PasswordManagementSection registrationType="email" onPasswordUpdated={mockOnPasswordUpdated} />
    );

    fireEvent.changeText(getByTestId('input-Enter your current password'), 'OldPassword123!');
    fireEvent.changeText(getByTestId('input-Enter your new password'), 'NewPassword123!');
    fireEvent.changeText(getByTestId('input-Confirm your new password'), 'NewPassword123!');

    fireEvent.press(getByTestId('button-Update Password'));

    await waitFor(() => {
      expect(require('@/backend/supabase/services/common/settingsService').updatePassword).toHaveBeenCalledWith({
        currentPassword: 'OldPassword123!',
        newPassword: 'NewPassword123!',
        confirmPassword: 'NewPassword123!',
      });
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Password updated successfully!');
      expect(mockOnPasswordUpdated).toHaveBeenCalled();
    });
  });

  it('handles update password failure', async () => {
    require('@/backend/supabase/services/common/settingsService').updatePassword.mockResolvedValue({
      success: false, error: 'Incorrect current password'
    });

    const { getByTestId, getByText } = render(
      <PasswordManagementSection registrationType="email" />
    );

    fireEvent.changeText(getByTestId('input-Enter your current password'), 'WrongPassword');
    fireEvent.changeText(getByTestId('input-Enter your new password'), 'NewPassword123!');
    fireEvent.changeText(getByTestId('input-Confirm your new password'), 'NewPassword123!');

    fireEvent.press(getByTestId('button-Update Password'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Incorrect current password');
      expect(getByText('Incorrect current password')).toBeTruthy();
    });
  });
});
