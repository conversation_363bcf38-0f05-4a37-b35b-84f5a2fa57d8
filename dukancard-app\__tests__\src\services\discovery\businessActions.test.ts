import { fetchBusinessesBySearch, fetchMoreBusinessCardsCombined } from '@/src/services/discovery/businessActions';
import { getSecureBusinessProfiles } from '@/src/services/discovery/utils/secureBusinessProfiles';

// Mock external dependencies
jest.mock('@/src/services/discovery/utils/secureBusinessProfiles');

describe('businessActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchBusinessesBySearch', () => {
    const mockBusinessData = [
      { id: '1', business_name: 'Business A', subscription_status: 'active', created_at: '2023-01-01T00:00:00Z', logo_url: 'logoA.png', member_name: 'Member A', title: 'Title A', address_line: 'Address A', city: 'City A', state: 'State A', pincode: '123456', locality: 'Locality A', phone: '123', business_category: 'Category A', instagram_url: 'instaA', facebook_url: 'fbA', whatsapp_number: 'waA', about_bio: 'Bio A', status: 'online', business_slug: 'slug-a', total_likes: 10, total_subscriptions: 5, average_rating: 4.5, theme_color: '#FFF', delivery_info: 'Info A', business_hours: {}, established_year: 2000, website_url: 'webA', linkedin_url: 'liA', twitter_url: 'twA', youtube_url: 'ytA', call_number: 'callA', total_visits: 100, today_visits: 10, yesterday_visits: 5, visits_7_days: 50, visits_30_days: 200, city_slug: 'city-a', state_slug: 'state-a', locality_slug: 'locality-a', gallery: [], latitude: 1, longitude: 1, custom_branding: null, custom_ads: null },
      { id: '2', business_name: 'Business B', subscription_status: 'inactive', created_at: '2023-01-02T00:00:00Z', logo_url: 'logoB.png', member_name: 'Member B', title: 'Title B', address_line: 'Address B', city: 'City B', state: 'State B', pincode: '654321', locality: 'Locality B', phone: '456', business_category: 'Category B', instagram_url: 'instaB', facebook_url: 'fbB', whatsapp_number: 'waB', about_bio: 'Bio B', status: 'offline', business_slug: 'slug-b', total_likes: 20, total_subscriptions: 10, average_rating: 3.5, theme_color: '#000', delivery_info: 'Info B', business_hours: {}, established_year: 2001, website_url: 'webB', linkedin_url: 'liB', twitter_url: 'twB', youtube_url: 'ytB', call_number: 'callB', total_visits: 200, today_visits: 20, yesterday_visits: 10, visits_7_days: 100, visits_30_days: 400, city_slug: 'city-b', state_slug: 'state-b', locality_slug: 'locality-b', gallery: [], latitude: 2, longitude: 2, custom_branding: null, custom_ads: null },
    ];

    it('should fetch businesses by search criteria', async () => {
      (getSecureBusinessProfiles as jest.Mock).mockResolvedValue({
        data: mockBusinessData,
        count: 2,
        error: null,
      });

      const params = {
        businessName: 'Business',
        pincode: '123456',
        page: 1,
        limit: 20,
        sortBy: 'created_desc',
        category: 'Retail',
      };
      const result = await fetchBusinessesBySearch(params);

      expect(getSecureBusinessProfiles).toHaveBeenCalledWith(
        params.businessName,
        params.pincode,
        undefined,
        undefined,
        params.page,
        params.limit,
        params.sortBy,
        params.category
      );
      expect(result.data?.businesses.length).toBe(2);
      expect(result.data?.totalCount).toBe(2);
      expect(result.data?.hasMore).toBe(false);
      expect(result.data?.nextPage).toBeNull();
      expect(result.data?.isAuthenticated).toBe(true);
    });

    it('should handle no businesses found', async () => {
      (getSecureBusinessProfiles as jest.Mock).mockResolvedValue({
        data: [],
        count: 0,
        error: null,
      });

      const params = {
        businessName: 'NonExistent',
        page: 1,
        limit: 20,
      };
      const result = await fetchBusinessesBySearch(params);

      expect(result.data?.businesses.length).toBe(0);
      expect(result.data?.totalCount).toBe(0);
      expect(result.data?.hasMore).toBe(false);
      expect(result.data?.nextPage).toBeNull();
    });

    it('should handle error from getSecureBusinessProfiles', async () => {
      (getSecureBusinessProfiles as jest.Mock).mockResolvedValue({
        data: null,
        count: null,
        error: 'Database error',
      });

      const params = {
        businessName: 'Business',
        page: 1,
        limit: 20,
      };
      const result = await fetchBusinessesBySearch(params);

      expect(result.error).toBe('Database error');
      expect(result.data).toBeUndefined();
    });

    it('should correctly calculate hasMore and nextPage', async () => {
      (getSecureBusinessProfiles as jest.Mock).mockResolvedValue({
        data: mockBusinessData,
        count: 30, // Total 30 items
        error: null,
      });

      const params = {
        businessName: 'Business',
        page: 1,
        limit: 20,
      };
      const result = await fetchBusinessesBySearch(params);

      expect(result.data?.hasMore).toBe(true);
      expect(result.data?.nextPage).toBe(2);
    });
  });

  describe('fetchMoreBusinessCardsCombined', () => {
    it('should fetch more business cards', async () => {
      // Mock fetchBusinessesBySearch for this specific test
      const mockFetchBusinessesBySearch = jest.fn().mockResolvedValueOnce({
        data: {
          businesses: [{ id: '3', business_name: 'Business C' }],
          totalCount: 3,
          hasMore: false,
          nextPage: null,
        },
      });
      // Manually set the mock for fetchBusinessesBySearch within the same file
      // This is a workaround for mocking functions within the same module in Jest
      // In a real scenario, you might refactor to avoid this.
      const businessActions = require('@/src/services/discovery/businessActions');
      businessActions.fetchBusinessesBySearch = mockFetchBusinessesBySearch;

      const params = {
        businessName: 'Business',
        page: 2,
        limit: 1,
      };
      const result = await fetchMoreBusinessCardsCombined(params);

      expect(mockFetchBusinessesBySearch).toHaveBeenCalledWith(params);
      expect(result.data?.businesses.length).toBe(1);
      expect(result.data?.totalCount).toBe(3);
      expect(result.data?.hasMore).toBe(false);
      expect(result.data?.nextPage).toBeNull();
    });

    it('should handle error from fetchBusinessesBySearch', async () => {
      // Mock fetchBusinessesBySearch for this specific test
      const mockFetchBusinessesBySearch = jest.fn().mockResolvedValueOnce({
        error: 'Search error',
      });
      const businessActions = require('@/src/services/discovery/businessActions');
      businessActions.fetchBusinessesBySearch = mockFetchBusinessesBySearch;

      const params = {
        businessName: 'Business',
        page: 1,
        limit: 10,
      };
      const result = await fetchMoreBusinessCardsCombined(params);

      expect(result.error).toBe('Search error');
      expect(result.data).toBeUndefined();
    });
  });
});