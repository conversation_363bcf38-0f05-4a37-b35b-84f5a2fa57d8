import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { SearchComponent } from '@/src/components/social/SearchComponent';
import { Ionicons } from '@expo/vector-icons';

// Mock Ionicons
jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons', // Mock Ionicons as a simple string
}));

jest.useFakeTimers();

describe('SearchComponent', () => {
  const onChangeTextMock = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    render(<SearchComponent value="" onChangeText={onChangeTextMock} />);
    expect(screen.getByPlaceholderText('Search...')).toBeOnTheScreen();
    expect(screen.queryByLabelText('clear-button')).toBeNull();
  });

  it('updates input value and debounces onChangeText', () => {
    render(<SearchComponent value="" onChangeText={onChangeTextMock} />);
    const input = screen.getByPlaceholderText('Search...');

    fireEvent.changeText(input, 'test query');
    expect(input.props.value).toBe('test query');
    expect(onChangeTextMock).not.toHaveBeenCalled(); // Should not be called immediately

    jest.advanceTimersByTime(500); // Advance timers by debounceMs
    expect(onChangeTextMock).toHaveBeenCalledWith('test query');
  });

  it('clears input when clear button is pressed', () => {
    render(<SearchComponent value="initial" onChangeText={onChangeTextMock} />);
    const input = screen.getByDisplayValue('initial');
    expect(screen.getByLabelText('clear-button')).toBeOnTheScreen();

    fireEvent.press(screen.getByLabelText('clear-button'));
    expect(input.props.value).toBe('');
    expect(onChangeTextMock).toHaveBeenCalledWith('');
    expect(screen.queryByLabelText('clear-button')).toBeNull();
  });

  it('updates local value when prop value changes', () => {
    const { rerender } = render(<SearchComponent value="initial" onChangeText={onChangeTextMock} />);
    const input = screen.getByDisplayValue('initial');

    rerender(<SearchComponent value="new value" onChangeText={onChangeTextMock} />);
    expect(input.props.value).toBe('new value');
  });

  it('uses custom placeholder', () => {
    render(<SearchComponent value="" onChangeText={onChangeTextMock} placeholder="Custom Search" />);
    expect(screen.getByPlaceholderText('Custom Search')).toBeOnTheScreen();
  });

  it('does not call onChangeText if value is the same after debounce', () => {
    render(<SearchComponent value="same value" onChangeText={onChangeTextMock} />);
    jest.advanceTimersByTime(500);
    expect(onChangeTextMock).not.toHaveBeenCalled();
  });
});