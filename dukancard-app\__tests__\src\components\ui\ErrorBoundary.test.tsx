import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { ErrorBoundary } from '@/src/components/ui/ErrorBoundary';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react-native';

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  AlertTriangle: jest.fn(() => null),
  RefreshCw: jest.fn(() => null),
  Home: jest.fn(() => null),
}));

describe('ErrorBoundary', () => {
  const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  const mockOnError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (AlertTriangle as jest.Mock).mockImplementation(() => <Text testID="alert-triangle-icon">AlertTriangleIcon</Text>);
    (RefreshCw as jest.Mock).mockImplementation(() => <Text testID="refresh-icon">RefreshIcon</Text>);
    (Home as jest.Mock).mockImplementation(() => <Text testID="home-icon">HomeIcon</Text>);
  });

  afterAll(() => {
    consoleErrorSpy.mockRestore();
  });

  const ProblematicComponent = () => {
    throw new Error('Test Error');
  };

  it('catches errors and displays fallback UI', () => {
    render(
      <ErrorBoundary>
        <ProblematicComponent />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeOnTheScreen();
    expect(screen.getByText("We're sorry, but something unexpected happened. Please try again.")).toBeOnTheScreen();
    expect(screen.getByTestId('alert-triangle-icon')).toBeOnTheScreen();
  });

  it('displays custom fallback when provided', () => {
    render(
      <ErrorBoundary fallback={<Text>Custom Fallback UI</Text>}>
        <ProblematicComponent />
      </ErrorBoundary>
    );

    expect(screen.getByText('Custom Fallback UI')).toBeOnTheScreen();
    expect(screen.queryByText('Something went wrong')).toBeNull();
  });

  it('calls onError callback when an error occurs', () => {
    render(
      <ErrorBoundary onError={mockOnError}>
        <ProblematicComponent />
      </ErrorBoundary>
    );

    expect(mockOnError).toHaveBeenCalledTimes(1);
    expect(mockOnError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({ componentStack: expect.any(String) })
    );
  });

  it('recovers from error when retry button is pressed', () => {
    const SafeComponent = () => <Text>Safe Content</Text>;
    render(
      <ErrorBoundary>
        <ProblematicComponent />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeOnTheScreen();

    fireEvent.press(screen.getByText('Try Again'));

    // Re-render with a safe component to simulate recovery
    render(
      <ErrorBoundary>
        <SafeComponent />
      </ErrorBoundary>
    );
    expect(screen.getByText('Safe Content')).toBeOnTheScreen();
    expect(screen.queryByText('Something went wrong')).toBeNull();
  });

  it('displays error details in development mode when showDetails is true', () => {
    // Temporarily set __DEV__ to true for this test
    const originalDev = __DEV__;
    (global as any).__DEV__ = true;

    render(
      <ErrorBoundary showDetails={true}>
        <ProblematicComponent />
      </ErrorBoundary>
    );

    expect(screen.getByText('Error Details (Development)')).toBeOnTheScreen();
    expect(screen.getByText('Error: Test Error')).toBeOnTheScreen();

    (global as any).__DEV__ = originalDev; // Restore original __DEV__
  });

  it('does not display error details in production mode', () => {
    // Temporarily set __DEV__ to false for this test
    const originalDev = __DEV__;
    (global as any).__DEV__ = false;

    render(
      <ErrorBoundary showDetails={true}>
        <ProblematicComponent />
      </ErrorBoundary>
    );

    expect(screen.queryByText('Error Details (Development)')).toBeNull();

    (global as any).__DEV__ = originalDev; // Restore original __DEV__
  });
});