import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react-native';
import { OnboardingProvider, useOnboarding } from '@/src/contexts/OnboardingContext';
import { useAuth } from '@/src/contexts/AuthContext';
import { 
  saveOnboardingData, 
  getOnboardingData, 
  clearOnboardingData,
} from '@/backend/supabase/services/common/onboardingService';
import { useSlugValidation } from '@/hooks/useSlugValidation';

// Mock all external dependencies
jest.mock('@/src/contexts/AuthContext');
jest.mock('@/backend/supabase/services/common/onboardingService');
jest.mock('@/hooks/useSlugValidation');

describe('OnboardingContext', () => {
  const mockUser = { id: 'user123', email: '<EMAIL>' };

  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue({ user: mockUser });
    (getOnboardingData as jest.Mock).mockResolvedValue({});
    (saveOnboardingData as jest.Mock).mockResolvedValue(undefined);
    (clearOnboardingData as jest.Mock).mockResolvedValue(undefined);
    (useSlugValidation as jest.Mock).mockReturnValue({
      slugValidation: { isValid: null, isAvailable: null, isLoading: false, error: null },
      validateSlug: jest.fn(),
    });
  });

  const TestComponent = () => {
    const context = useOnboarding();
    return (
      <>
        <Text testID="current-step">{context.currentStep}</Text>
        <Text testID="is-loading">{context.isLoading.toString()}</Text>
        <Text testID="is-saving">{context.isSaving.toString()}</Text>
        <Text testID="form-data-email">{context.formData.email || 'N/A'}</Text>
        <Text testID="slug-validation-is-valid">{context.slugValidation.isValid?.toString() || 'N/A'}</Text>
        <Text testID="progress">{context.progress}</Text>
        <Text testID="completed-steps">{context.completedSteps.join(',')}</Text>

        <button onPress={() => context.nextStep()}>Next Step</button>
        <button onPress={() => context.prevStep()}>Prev Step</button>
        <button onPress={() => context.setCurrentStep(3)}>Set Step 3</button>
        <button onPress={() => context.updateFormData({ businessName: 'Test Business' })}>Update Business Name</button>
        <button onPress={() => context.clearFormData()}>Clear Data</button>
        <button onPress={() => context.validateStep(1)}>Validate Step 1</button>
        <button onPress={() => context.validateSlug('test-slug')}>Validate Slug</button>
      </>
    );
  };

  it('initializes with default values and user email', async () => {
    render(
      <OnboardingProvider>
        <TestComponent />
      </OnboardingProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('current-step').props.children).toBe(1);
      expect(screen.getByTestId('is-loading').props.children).toBe('false');
      expect(screen.getByTestId('form-data-email').props.children).toBe('<EMAIL>');
      expect(saveOnboardingData).toHaveBeenCalledWith({ email: '<EMAIL>' });
    });
  });

  it('loads existing onboarding data', async () => {
    (getOnboardingData as jest.Mock).mockResolvedValue({
      businessName: 'Existing Business',
      email: '<EMAIL>',
    });

    render(
      <OnboardingProvider>
        <TestComponent />
      </OnboardingProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('form-data-email').props.children).toBe('<EMAIL>');
      expect(screen.getByTestId('current-step').props.children).toBe(1);
    });
  });

  it('updates form data and saves it', async () => {
    render(
      <OnboardingProvider>
        <TestComponent />
      </OnboardingProvider>
    );

    fireEvent.press(screen.getByText('Update Business Name'));

    await waitFor(() => {
      expect(screen.getByTestId('is-saving').props.children).toBe('false');
      expect(saveOnboardingData).toHaveBeenCalledWith({
        email: '<EMAIL>',
        businessName: 'Test Business',
      });
    });
  });

  it('clears form data', async () => {
    render(
      <OnboardingProvider>
        <TestComponent />
      </OnboardingProvider>
    );

    fireEvent.press(screen.getByText('Clear Data'));

    await waitFor(() => {
      expect(screen.getByTestId('form-data-email').props.children).toBe('N/A');
      expect(screen.getByTestId('current-step').props.children).toBe(1);
      expect(clearOnboardingData).toHaveBeenCalledTimes(1);
    });
  });

  it('navigates to next step', async () => {
    render(
      <OnboardingProvider>
        <TestComponent />
      </OnboardingProvider>
    );

    // Update form data to make step 1 valid
    await act(async () => {
      await (getOnboardingData as jest.Mock).mockResolvedValue({
        businessName: 'Test Business',
        businessCategory: 'Retail',
        memberName: 'John Doe',
        email: '<EMAIL>',
      });
      fireEvent.press(screen.getByText('Update Business Name')); // Trigger re-render and re-evaluation
    });

    fireEvent.press(screen.getByText('Next Step'));

    await waitFor(() => {
      expect(screen.getByTestId('current-step').props.children).toBe(2);
      expect(screen.getByTestId('completed-steps').props.children).toBe('1');
    });
  });

  it('navigates to previous step', async () => {
    render(
      <OnboardingProvider>
        <TestComponent />
      </OnboardingProvider>
    );

    // Advance to step 2 first
    await act(async () => {
      await (getOnboardingData as jest.Mock).mockResolvedValue({
        businessName: 'Test Business',
        businessCategory: 'Retail',
        memberName: 'John Doe',
        email: '<EMAIL>',
      });
      fireEvent.press(screen.getByText('Update Business Name'));
    });
    fireEvent.press(screen.getByText('Next Step'));
    await waitFor(() => expect(screen.getByTestId('current-step').props.children).toBe(2));

    fireEvent.press(screen.getByText('Prev Step'));

    await waitFor(() => {
      expect(screen.getByTestId('current-step').props.children).toBe(1);
    });
  });

  it('validates a step', async () => {
    render(
      <OnboardingProvider>
        <TestComponent />
      </OnboardingProvider>
    );

    // Step 1 is initially invalid without data
    let validationResult = await act(() => useOnboarding().validateStep(1));
    expect(validationResult.isValid).toBe(false);
    expect(validationResult.errors).toContain('businessName is required');

    // Update data to make step 1 valid
    await act(async () => {
      await (getOnboardingData as jest.Mock).mockResolvedValue({
        businessName: 'Test Business',
        businessCategory: 'Retail',
        memberName: 'John Doe',
        email: '<EMAIL>',
      });
      fireEvent.press(screen.getByText('Update Business Name'));
    });

    validationResult = await act(() => useOnboarding().validateStep(1));
    expect(validationResult.isValid).toBe(true);
    expect(validationResult.errors).toEqual([]);
  });

  it('validates slug using useSlugValidation hook', async () => {
    const mockValidateSlug = jest.fn();
    (useSlugValidation as jest.Mock).mockReturnValue({
      slugValidation: { isValid: true, isAvailable: true, isLoading: false, error: null },
      validateSlug: mockValidateSlug,
    });

    render(
      <OnboardingProvider>
        <TestComponent />
      </OnboardingProvider>
    );

    fireEvent.press(screen.getByText('Validate Slug'));
    expect(mockValidateSlug).toHaveBeenCalledWith('test-slug');
  });

  it('throws error if useOnboarding is used outside provider', () => {
    expect(() => render(<useOnboarding />)).toThrow(
      'useOnboarding must be used within an OnboardingProvider'
    );
  });
});