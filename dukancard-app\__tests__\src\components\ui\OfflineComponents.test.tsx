import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { OfflineState, OfflineBanner } from '@/src/components/ui/OfflineComponents';
import { WifiOff } from 'lucide-react-native';

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  WifiOff: jest.fn(() => null),
}));

describe('OfflineState', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (WifiOff as jest.Mock).mockImplementation(() => <Text testID="wifi-off-icon">WifiOffIcon</Text>);
  });

  it('renders correctly with default message', () => {
    render(<OfflineState />);
    expect(screen.getByText('No Internet Connection')).toBeOnTheScreen();
    expect(screen.getByText('You are currently offline. Please check your internet connection.')).toBeOnTheScreen();
    expect(screen.getByTestId('wifi-off-icon')).toBeOnTheScreen();
  });

  it('renders with custom message', () => {
    render(<OfflineState message="Custom offline message." />);
    expect(screen.getByText('Custom offline message.')).toBeOnTheScreen();
  });
});

describe('OfflineBanner', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (WifiOff as jest.Mock).mockImplementation(() => <Text testID="wifi-off-icon">WifiOffIcon</Text>);
  });

  it('renders when visible is true', () => {
    render(<OfflineBanner visible={true} />);
    expect(screen.getByText('No internet connection')).toBeOnTheScreen();
    expect(screen.getByTestId('wifi-off-icon')).toBeOnTheScreen();
  });

  it('does not render when visible is false', () => {
    render(<OfflineBanner visible={false} />);
    expect(screen.queryByText('No internet connection')).toBeNull();
    expect(screen.queryByTestId('wifi-off-icon')).toBeNull();
  });
});