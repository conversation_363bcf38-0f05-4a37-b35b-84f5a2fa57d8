import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { EmailLinkingSection } from '../../../../src/components/settings/EmailLinkingSection';
import { Alert } from 'react-native';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/backend/supabase/services/common/settingsService', () => ({
  linkCustomerEmail: jest.fn(),
  verifyEmailOTP: jest.fn(),
}));

jest.mock('@/src/components/ui/Input', () => ({
  Input: jest.fn(({ value, onChangeText, placeholder, error, leftIcon, keyboardType, autoCapitalize, editable }) => (
    <TextInput
      testID={`input-${placeholder}`}
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      keyboardType={keyboardType}
      autoCapitalize={autoCapitalize}
      editable={editable}
    />
  )),
}));

jest.mock('@/src/components/ui/Button', () => ({
  Button: jest.fn(({ title, onPress, disabled, variant, icon }) => (
    <TouchableOpacity testID={`button-${title}`} onPress={onPress} disabled={disabled}>
      <Text>{title}</Text>
      {icon}
    </TouchableOpacity>
  )),
}));

jest.spyOn(Alert, 'alert');

describe('EmailLinkingSection', () => {
  const mockOnEmailUpdated = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mocks for each test
    require('@/backend/supabase/services/common/settingsService').linkCustomerEmail.mockReset();
    require('@/backend/supabase/services/common/settingsService').verifyEmailOTP.mockReset();
  });

  it('renders correctly for Google user', () => {
    const { getByText, getByDisplayValue } = render(
      <EmailLinkingSection
        currentEmail="<EMAIL>"
        registrationType="google"
        onEmailUpdated={mockOnEmailUpdated}
      />
    );
    expect(getByText('Email Address (Google)')).toBeTruthy();
    expect(getByDisplayValue('<EMAIL>')).toBeTruthy();
    expect(getByText('This email is managed by your Google account.')).toBeTruthy();
  });

  it('renders correctly for email user (update email)', () => {
    const { getByText, getByPlaceholderText } = render(
      <EmailLinkingSection
        currentEmail="<EMAIL>"
        registrationType="email"
        onEmailUpdated={mockOnEmailUpdated}
      />
    );
    expect(getByText('Update Email Address')).toBeTruthy();
    expect(getByPlaceholderText('<EMAIL>')).toBeTruthy();
    expect(getByText("We'll send verification codes to both your current and new email addresses.")).toBeTruthy();
  });

  it('renders correctly for phone user (link email)', () => {
    const { getByText, getByPlaceholderText } = render(
      <EmailLinkingSection
        registrationType="phone"
        onEmailUpdated={mockOnEmailUpdated}
      />
    );
    expect(getByText('Link Email Address')).toBeTruthy();
    expect(getByPlaceholderText('<EMAIL>')).toBeTruthy();
    expect(getByText("We'll send a verification code to this email address.")).toBeTruthy();
  });

  it('validates email input', async () => {
    const { getByTestId, getByText } = render(
      <EmailLinkingSection registrationType="phone" />
    );
    const emailInput = getByTestId('<EMAIL>');
    const linkButton = getByTestId('button-Link Email');

    fireEvent.changeText(emailInput, 'invalid-email');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(getByText('Please enter a valid email address')).toBeTruthy();
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Please enter a valid email address');
    });
  });

  it('sends OTP and transitions to OTP step on successful email link', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerEmail.mockResolvedValue({
      success: true, requiresOTP: true
    });

    const { getByTestId, getByText } = render(
      <EmailLinkingSection registrationType="phone" />
    );
    const emailInput = getByTestId('<EMAIL>');
    const linkButton = getByTestId('button-Link Email');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(require('@/backend/supabase/services/common/settingsService').linkCustomerEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(getByText("We've sent a 6-digit verification code to your email address.")).toBeTruthy();
      expect(getByTestId('input-Enter 6-digit code')).toBeTruthy();
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Verification code sent to your email!');
    });
  });

  it('links email directly if no OTP is required', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerEmail.mockResolvedValue({
      success: true, requiresOTP: false
    });

    const { getByTestId, getByText } = render(
      <EmailLinkingSection registrationType="phone" onEmailUpdated={mockOnEmailUpdated} />
    );
    const emailInput = getByTestId('<EMAIL>');
    const linkButton = getByTestId('button-Link Email');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(require('@/backend/supabase/services/common/settingsService').linkCustomerEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(getByText('Email address has been linked to your account.')).toBeTruthy();
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Email linked successfully!');
      expect(mockOnEmailUpdated).toHaveBeenCalled();
    });
  });

  it('validates OTP input', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerEmail.mockResolvedValue({
      success: true, requiresOTP: true
    });

    const { getByTestId, getByText } = render(
      <EmailLinkingSection registrationType="phone" />
    );
    const emailInput = getByTestId('<EMAIL>');
    const linkButton = getByTestId('button-Link Email');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(getByTestId('input-Enter 6-digit code')).toBeTruthy();
    });

    const otpInput = getByTestId('input-Enter 6-digit code');
    const verifyButton = getByTestId('button-Verify Code');

    fireEvent.changeText(otpInput, '123'); // Invalid OTP length
    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(getByText('Verification code must be 6 digits')).toBeTruthy();
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Verification code must be 6 digits');
    });

    fireEvent.changeText(otpInput, 'abcde'); // Invalid OTP characters
    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(getByText('Verification code must contain only numbers')).toBeTruthy();
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'Verification code must contain only numbers');
    });
  });

  it('verifies OTP and links email successfully', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerEmail.mockResolvedValue({
      success: true, requiresOTP: true
    });
    require('@/backend/supabase/services/common/settingsService').verifyEmailOTP.mockResolvedValue({
      success: true
    });

    const { getByTestId, getByText } = render(
      <EmailLinkingSection registrationType="phone" onEmailUpdated={mockOnEmailUpdated} />
    );
    const emailInput = getByTestId('<EMAIL>');
    const linkButton = getByTestId('button-Link Email');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(getByTestId('input-Enter 6-digit code')).toBeTruthy();
    });

    const otpInput = getByTestId('input-Enter 6-digit code');
    const verifyButton = getByTestId('button-Verify Code');

    fireEvent.changeText(otpInput, '123456');
    fireEvent.press(verifyButton);

    await waitFor(() => {
      expect(require('@/backend/supabase/services/common/settingsService').verifyEmailOTP).toHaveBeenCalledWith('<EMAIL>', '123456');
      expect(getByText('Email address has been linked to your account.')).toBeTruthy();
      expect(Alert.alert).toHaveBeenCalledWith('Success', 'Email linked successfully!');
      expect(mockOnEmailUpdated).toHaveBeenCalled();
    });
  });

  it('goes back to email step from OTP step', async () => {
    require('@/backend/supabase/services/common/settingsService').linkCustomerEmail.mockResolvedValue({
      success: true, requiresOTP: true
    });

    const { getByTestId, getByText } = render(
      <EmailLinkingSection registrationType="phone" />
    );
    const emailInput = getByTestId('<EMAIL>');
    const linkButton = getByTestId('button-Link Email');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.press(linkButton);

    await waitFor(() => {
      expect(getByTestId('input-Enter 6-digit code')).toBeTruthy();
    });

    const backButton = getByTestId('button-Back');
    fireEvent.press(backButton);

    await waitFor(() => {
      expect(getByTestId('<EMAIL>')).toBeTruthy();
      expect(getByTestId('button-Link Email')).toBeTruthy();
    });
  });
});
