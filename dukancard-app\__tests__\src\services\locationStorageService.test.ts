import { 
  saveLocationData,
  loadLocationData,
  clearLocationData,
  savePincode,
  saveCity,
  saveLocality,
  saveGPSCoordinates,
  getStoredPincode,
  getStoredCity,
  getStoredLocality,
  getStoredGPSCoordinates,
  hasStoredLocationData,
  detectAndSaveCurrentLocation,
  isFirstAppLaunch,
} from '@/src/services/locationStorageService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  getCurrentLocation,
  reverseGeocodeCoordinates,
  requestLocationPermission,
} from '@/backend/supabase/services/location/locationService';

// Mock external dependencies
jest.mock('@react-native-async-storage/async-storage');
jest.mock('@/backend/supabase/services/location/locationService');

describe('locationStorageService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(null);
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
    (AsyncStorage.removeItem as jest.Mock).mockResolvedValue(null);
    (requestLocationPermission as jest.Mock).mockResolvedValue({ granted: true });
    (getCurrentLocation as jest.Mock).mockResolvedValue({ coords: { latitude: 10, longitude: 20 } });
    (reverseGeocodeCoordinates as jest.Mock).mockResolvedValue({
      success: true,
      pincode: '123456',
      city: 'TestCity',
      state: 'TestState',
      locality: 'TestLocality',
    });
  });

  describe('saveLocationData', () => {
    it('should save location data to AsyncStorage', async () => {
      const locationData = { latitude: 1, longitude: 2, city: 'City', pincode: '123456' };
      const result = await saveLocationData(locationData);
      expect(result.success).toBe(true);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        '@dukancard_location_preferences',
        expect.stringContaining('"latitude":1,"longitude":2,"city":"City","pincode":"123456"')
      );
    });

    it('should include lastUpdated timestamp', async () => {
      const locationData = { latitude: 1, longitude: 2 };
      const result = await saveLocationData(locationData);
      expect(result.success).toBe(true);
      const storedData = JSON.parse((AsyncStorage.setItem as jest.Mock).mock.calls[0][1]);
      expect(storedData.lastUpdated).toBeDefined();
    });

    it('should return error on AsyncStorage failure', async () => {
      (AsyncStorage.setItem as jest.Mock).mockRejectedValue(new Error('Storage error'));
      const result = await saveLocationData({});
      expect(result.success).toBe(false);
      expect(result.error).toBe('Storage error');
    });
  });

  describe('loadLocationData', () => {
    it('should load and parse location data from AsyncStorage', async () => {
      const storedData = {
        latitude: 1,
        longitude: 2,
        city: 'City',
        pincode: '123456',
        lastUpdated: new Date().toISOString(),
      };
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify(storedData));

      const result = await loadLocationData();
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        latitude: 1,
        longitude: 2,
        city: 'City',
        pincode: '123456',
      });
    });

    it('should return undefined data if no data in AsyncStorage', async () => {
      const result = await loadLocationData();
      expect(result.success).toBe(true);
      expect(result.data).toBeUndefined();
    });

    it('should return error on AsyncStorage failure', async () => {
      (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Storage error'));
      const result = await loadLocationData();
      expect(result.success).toBe(false);
      expect(result.error).toBe('Storage error');
    });

    it('should clear invalid data and return undefined', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue('{"invalid":"data"}');
      const result = await loadLocationData();
      expect(result.success).toBe(true);
      expect(result.data).toBeUndefined();
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('@dukancard_location_preferences');
    });
  });

  describe('clearLocationData', () => {
    it('should remove location data from AsyncStorage', async () => {
      const result = await clearLocationData();
      expect(result.success).toBe(true);
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith('@dukancard_location_preferences');
    });

    it('should return error on AsyncStorage failure', async () => {
      (AsyncStorage.removeItem as jest.Mock).mockRejectedValue(new Error('Storage error'));
      const result = await clearLocationData();
      expect(result.success).toBe(false);
      expect(result.error).toBe('Storage error');
    });
  });

  describe('savePincode', () => {
    it('should save pincode and merge with existing data', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ city: 'OldCity', lastUpdated: new Date().toISOString() }));
      const result = await savePincode('654321');
      expect(result.success).toBe(true);
      const storedData = JSON.parse((AsyncStorage.setItem as jest.Mock).mock.calls[0][1]);
      expect(storedData.pincode).toBe('654321');
      expect(storedData.city).toBe('OldCity');
    });
  });

  describe('saveCity', () => {
    it('should save city and merge with existing data', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ pincode: '123456', lastUpdated: new Date().toISOString() }));
      const result = await saveCity('NewCity');
      expect(result.success).toBe(true);
      const storedData = JSON.parse((AsyncStorage.setItem as jest.Mock).mock.calls[0][1]);
      expect(storedData.city).toBe('NewCity');
      expect(storedData.pincode).toBe('123456');
    });
  });

  describe('saveLocality', () => {
    it('should save locality and merge with existing data', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ city: 'City', lastUpdated: new Date().toISOString() }));
      const result = await saveLocality('NewLocality');
      expect(result.success).toBe(true);
      const storedData = JSON.parse((AsyncStorage.setItem as jest.Mock).mock.calls[0][1]);
      expect(storedData.locality).toBe('NewLocality');
      expect(storedData.city).toBe('City');
    });
  });

  describe('saveGPSCoordinates', () => {
    it('should save GPS coordinates and merge with existing data', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ city: 'City', lastUpdated: new Date().toISOString() }));
      const result = await saveGPSCoordinates(30, 40);
      expect(result.success).toBe(true);
      const storedData = JSON.parse((AsyncStorage.setItem as jest.Mock).mock.calls[0][1]);
      expect(storedData.latitude).toBe(30);
      expect(storedData.longitude).toBe(40);
      expect(storedData.city).toBe('City');
    });
  });

  describe('getStoredPincode', () => {
    it('should return stored pincode', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ pincode: '123456', lastUpdated: new Date().toISOString() }));
      const result = await getStoredPincode();
      expect(result.success).toBe(true);
      expect(result.pincode).toBe('123456');
    });
  });

  describe('getStoredCity', () => {
    it('should return stored city', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ city: 'TestCity', lastUpdated: new Date().toISOString() }));
      const result = await getStoredCity();
      expect(result.success).toBe(true);
      expect(result.city).toBe('TestCity');
    });
  });

  describe('getStoredLocality', () => {
    it('should return stored locality', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ locality: 'TestLocality', lastUpdated: new Date().toISOString() }));
      const result = await getStoredLocality();
      expect(result.success).toBe(true);
      expect(result.locality).toBe('TestLocality');
    });
  });

  describe('getStoredGPSCoordinates', () => {
    it('should return stored GPS coordinates', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ latitude: 10, longitude: 20, lastUpdated: new Date().toISOString() }));
      const result = await getStoredGPSCoordinates();
      expect(result.success).toBe(true);
      expect(result.coordinates).toEqual({ latitude: 10, longitude: 20 });
    });

    it('should return undefined if coordinates are missing', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ city: 'City', lastUpdated: new Date().toISOString() }));
      const result = await getStoredGPSCoordinates();
      expect(result.success).toBe(true);
      expect(result.coordinates).toBeUndefined();
    });
  });

  describe('hasStoredLocationData', () => {
    it('should return true if location data exists', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ city: 'City', lastUpdated: new Date().toISOString() }));
      const result = await hasStoredLocationData();
      expect(result.success).toBe(true);
      expect(result.hasData).toBe(true);
    });

    it('should return false if no location data exists', async () => {
      const result = await hasStoredLocationData();
      expect(result.success).toBe(true);
      expect(result.hasData).toBe(false);
    });
  });

  describe('detectAndSaveCurrentLocation', () => {
    it('should return existing location if already stored', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ city: 'StoredCity', lastUpdated: new Date().toISOString() }));
      const result = await detectAndSaveCurrentLocation();
      expect(result.success).toBe(true);
      expect(result.location?.city).toBe('StoredCity');
      expect(requestLocationPermission).not.toHaveBeenCalled();
    });

    it('should detect and save new location', async () => {
      const result = await detectAndSaveCurrentLocation();
      expect(result.success).toBe(true);
      expect(result.location?.city).toBe('TestCity');
      expect(requestLocationPermission).toHaveBeenCalledTimes(1);
      expect(getCurrentLocation).toHaveBeenCalledTimes(1);
      expect(reverseGeocodeCoordinates).toHaveBeenCalledTimes(1);
      expect(saveLocationData).toHaveBeenCalledTimes(1);
    });

    it('should return error if permission denied', async () => {
      (requestLocationPermission as jest.Mock).mockResolvedValue({ granted: false });
      const result = await detectAndSaveCurrentLocation();
      expect(result.success).toBe(false);
      expect(result.error).toBe('Location permission denied. Please enable location access in settings.');
    });

    it('should return error if getCurrentLocation fails', async () => {
      (getCurrentLocation as jest.Mock).mockResolvedValue(null);
      const result = await detectAndSaveCurrentLocation();
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to get current location. Please check if location services are enabled.');
    });

    it('should return error if saveLocationData fails', async () => {
      (saveLocationData as jest.Mock).mockResolvedValue({ success: false, error: 'Save error' });
      const result = await detectAndSaveCurrentLocation();
      expect(result.success).toBe(false);
      expect(result.error).toBe('Save error');
    });
  });

  describe('isFirstAppLaunch', () => {
    it('should return true if no stored location data', async () => {
      const result = await isFirstAppLaunch();
      expect(result.success).toBe(true);
      expect(result.isFirst).toBe(true);
    });

    it('should return false if stored location data exists', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue(JSON.stringify({ city: 'City', lastUpdated: new Date().toISOString() }));
      const result = await isFirstAppLaunch();
      expect(result.success).toBe(true);
      expect(result.isFirst).toBe(false);
    });
  });
});