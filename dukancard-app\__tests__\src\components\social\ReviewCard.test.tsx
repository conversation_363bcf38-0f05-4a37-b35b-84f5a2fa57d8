import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { Alert, ActivityIndicator, TextInput } from 'react-native';
import { ReviewCard } from '@/src/components/social/ReviewCard';
import { useRouter } from 'expo-router';
import { useToast } from '@/src/components/ui/Toast';

// Mock necessary modules
jest.mock('expo-router');
jest.mock('@/src/components/ui/Toast');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
  ActivityIndicator: jest.fn(() => null), // Mock ActivityIndicator to return null
  TextInput: jest.fn((props) => <jest.requireActual('react-native').TextInput {...props} />),
}));

const mockReview = {
  id: 'review1',
  rating: 4,
  review_text: 'This is a great business!',
  created_at: '2023-01-15T10:00:00Z',
  updated_at: '2023-01-15T10:00:00Z',
  business_profile_id: 'business1',
  user_id: 'user1',
  business_profiles: {
    id: 'business1',
    business_name: 'Test Business',
    business_slug: 'test-business',
    logo_url: 'https://example.com/logo.jpg',
  },
};

describe('ReviewCard', () => {
  const onDeleteMock = jest.fn();
  const onUpdateMock = jest.fn();
  const mockRouterPush = jest.fn();
  const mockToastError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockRouterPush });
    (useToast as jest.Mock).mockReturnValue({ error: mockToastError });
    (ActivityIndicator as jest.Mock).mockImplementation(({ color }) => <Text testID="activity-indicator" style={{ color }}>Loading...</Text>);
  });

  it('renders correctly with review data', () => {
    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);

    expect(screen.getByText('Test Business')).toBeOnTheScreen();
    expect(screen.getByText('Reviewed on Jan 15, 2023')).toBeOnTheScreen();
    expect(screen.getByText('This is a great business!')).toBeOnTheScreen();
    expect(screen.getAllByLabelText('star-filled').length).toBe(4);
    expect(screen.getAllByLabelText('star-outline').length).toBe(1);
  });

  it('displays edited status if updated_at is different from created_at', () => {
    const editedReview = { ...mockReview, updated_at: '2023-01-16T10:00:00Z' };
    render(<ReviewCard review={editedReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    expect(screen.getByText('Reviewed on Jan 15, 2023 (edited)')).toBeOnTheScreen();
  });

  it('navigates to business profile on business header press', () => {
    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByText('Test Business'));
    expect(mockRouterPush).toHaveBeenCalledWith('/business/test-business');
  });

  it('shows alert on delete button press', () => {
    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByLabelText('Delete review')); // Assuming accessibilityLabel
    expect(Alert.alert).toHaveBeenCalledWith(
      'Delete Review',
      'Are you sure you want to delete this review? This action cannot be undone.',
      expect.any(Array)
    );
  });

  it('calls onDelete when delete is confirmed', async () => {
    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByLabelText('Delete review'));

    const deleteAction = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    await deleteAction.onPress();

    expect(onDeleteMock).toHaveBeenCalledWith('review1');
  });

  it('enters edit mode on edit button press', () => {
    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByLabelText('Edit review')); // Assuming accessibilityLabel

    expect(screen.getByDisplayValue('This is a great business!')).toBeOnTheScreen();
    expect(screen.getByText('Save')).toBeOnTheScreen();
    expect(screen.getByText('Cancel')).toBeOnTheScreen();
  });

  it('updates rating and review text in edit mode', () => {
    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByLabelText('Edit review'));

    fireEvent.press(screen.getAllByLabelText('star-outline')[0]); // Click the 5th star to make it 5 stars
    fireEvent.changeText(screen.getByDisplayValue('This is a great business!'), 'Updated review text');

    expect(screen.getAllByLabelText('star-filled').length).toBe(5);
    expect(screen.getByDisplayValue('Updated review text')).toBeOnTheScreen();
  });

  it('calls onUpdate and exits edit mode on save', async () => {
    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByLabelText('Edit review'));

    fireEvent.changeText(screen.getByDisplayValue('This is a great business!'), 'Updated review text');
    fireEvent.press(screen.getByText('Save'));

    expect(onUpdateMock).toHaveBeenCalledWith('review1', 4, 'Updated review text');
    await screen.findByText('This is a great business!'); // Wait for edit mode to exit
    expect(screen.queryByText('Save')).toBeNull();
  });

  it('exits edit mode and reverts changes on cancel', () => {
    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByLabelText('Edit review'));

    fireEvent.changeText(screen.getByDisplayValue('This is a great business!'), 'Temporary text');
    fireEvent.press(screen.getByText('Cancel'));

    expect(screen.getByText('This is a great business!')).toBeOnTheScreen();
    expect(screen.queryByText('Temporary text')).toBeNull();
    expect(screen.queryByText('Save')).toBeNull();
  });

  it('shows activity indicator when deleting', async () => {
    onDeleteMock.mockImplementationOnce(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByLabelText('Delete review'));

    const deleteAction = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    deleteAction.onPress();

    expect(screen.getByTestId('activity-indicator')).toBeOnTheScreen();
    await screen.findByText('Test Business'); // Wait for deletion to complete
    expect(screen.queryByTestId('activity-indicator')).toBeNull();
  });

  it('shows activity indicator when updating', async () => {
    onUpdateMock.mockImplementationOnce(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(<ReviewCard review={mockReview} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByLabelText('Edit review'));
    fireEvent.press(screen.getByText('Save'));

    expect(screen.getByTestId('activity-indicator')).toBeOnTheScreen();
    await screen.findByText('This is a great business!'); // Wait for update to complete
    expect(screen.queryByTestId('activity-indicator')).toBeNull();
  });

  it('handles navigation error gracefully', () => {
    const reviewWithoutSlug = {
      ...mockReview,
      business_profiles: { ...mockReview.business_profiles, business_slug: null },
    };
    render(<ReviewCard review={reviewWithoutSlug} onDelete={onDeleteMock} onUpdate={onUpdateMock} />);
    fireEvent.press(screen.getByText('Test Business'));
    expect(mockToastError).toHaveBeenCalledWith("Navigation Error", "Business profile not available");
    expect(mockRouterPush).not.toHaveBeenCalled();
  });
});