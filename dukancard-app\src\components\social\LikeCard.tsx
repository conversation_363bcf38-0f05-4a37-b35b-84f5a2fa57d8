/**
 * LikeCard Component for React Native
 * Displays individual liked business with unlike functionality
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useToast } from '@/src/components/ui/Toast';
import { useTheme } from '@/src/hooks/useTheme';
import { createLikeCardStyles } from '@/styles/components/social/like-card';

interface BusinessProfileData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  locality: string | null;
  city: string | null;
  state: string | null;
}

interface LikeWithProfile {
  id: string;
  business_profiles: BusinessProfileData | null;
}

interface LikeCardProps {
  like: LikeWithProfile;
  onUnlike: (likeId: string) => Promise<void>;
}

export const LikeCard: React.FC<LikeCardProps> = ({
  like,
  onUnlike,
}) => {
  const [isUnliking, setIsUnliking] = useState(false);
  const business = like.business_profiles;
  const router = useRouter();
  const toast = useToast();
  const theme = useTheme();
  const styles = createLikeCardStyles(theme);

  if (!business) {
    return null;
  }

  const handleUnlike = () => {
    Alert.alert(
      'Unlike Business',
      `Are you sure you want to remove ${business.business_name} from your liked businesses?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Unlike',
          style: 'destructive',
          onPress: async () => {
            setIsUnliking(true);
            try {
              await onUnlike(like.id);
            } catch (error) {
              console.error('Error unliking:', error);
            } finally {
              setIsUnliking(false);
            }
          },
        },
      ]
    );
  };

  const handleVisitBusiness = () => {
    try {
      if (!business?.business_slug) {
        toast.error("Navigation Error", "Business profile not available");
        return;
      }

      // Navigate to business profile using the slug
      router.push(`/business/${business.business_slug}`);
    } catch (error) {
      console.error("Error navigating to business profile:", error);
      toast.error("Navigation Error", "Failed to open business profile");
    }
  };

  const getBusinessInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatAddress = () => {
    const parts = [];
    if (business.locality) parts.push(business.locality);
    if (business.city) parts.push(business.city);
    if (business.state) parts.push(business.state);
    return parts.join(', ');
  };

  return (
    <View style={styles.listItem}>
      <TouchableOpacity
        style={styles.listContent}
        onPress={handleVisitBusiness}
        activeOpacity={0.7}
      >
        {/* Business Logo/Avatar */}
        <View style={styles.logoContainer}>
          {business.logo_url ? (
            <Image source={{ uri: business.logo_url }} style={styles.logo} />
          ) : (
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>
                {getBusinessInitials(business.business_name || 'B')}
              </Text>
            </View>
          )}
        </View>

        {/* Business Info */}
        <View style={styles.businessInfo}>
          <Text style={styles.businessName} numberOfLines={1}>
            {business.business_name}
          </Text>
          {formatAddress() && (
            <Text style={styles.businessAddress} numberOfLines={1}>
              {formatAddress()}
            </Text>
          )}
          <View style={styles.statusContainer}>
            <Ionicons name="heart" size={12} color="#ff4444" />
            <Text style={styles.statusText}>Liked</Text>
          </View>
        </View>

        {/* Unlike Button - Right Corner */}
        <TouchableOpacity
          style={[styles.unlikeButton, isUnliking && styles.unlikeButtonDisabled]}
          onPress={handleUnlike}
          disabled={isUnliking}
        >
          {isUnliking ? (
            <ActivityIndicator size="small" color="#ff4444" />
          ) : (
            <Ionicons name="heart-dislike" size={20} color="#ff4444" />
          )}
        </TouchableOpacity>
      </TouchableOpacity>

      {/* Separator */}
      <View style={styles.separator} />
    </View>
  );
};

