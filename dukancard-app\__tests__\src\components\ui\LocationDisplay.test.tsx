import React from 'react';
import { render, screen, waitFor } from '@testing-library/react-native';
import { LocationDisplay } from '@/src/components/ui/LocationDisplay';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/src/contexts/AuthContext';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme');
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
    })),
  },
}));
jest.mock('@/src/contexts/AuthContext');

describe('LocationDisplay', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
    (useAuth as jest.Mock).mockReturnValue({ user: { id: 'user123' } });
  });

  it('renders loading state initially', () => {
    (supabase.from as jest.Mock).mockReturnValue({
      select: () => ({
        eq: () => ({
          single: () => new Promise(() => {}), // Never resolve to keep it loading
        }),
      }),
    });

    render(<LocationDisplay />);
    expect(screen.getByText('Loading location...')).toBeOnTheScreen();
  });

  it('renders location not available when no user is authenticated', async () => {
    (useAuth as jest.Mock).mockReturnValue({ user: null });
    render(<LocationDisplay />);
    await waitFor(() => {
      expect(screen.getByText('Location not available')).toBeOnTheScreen();
    });
  });

  it('renders location from business profile', async () => {
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'business_profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({ data: { city: 'Mumbai', state: 'Maharashtra', locality: 'Bandra', pincode: '400050' } }),
            }),
          }),
        };
      }
      return jest.requireActual('@/lib/supabase').supabase.from(tableName); // Fallback for other tables
    });

    render(<LocationDisplay />);
    await waitFor(() => {
      expect(screen.getByText('Posting from: Bandra, Mumbai, Maharashtra, 400050')).toBeOnTheScreen();
    });
  });

  it('renders location from customer profile if business profile is not found', async () => {
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'business_profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({ data: null }),
            }),
          }),
        };
      } else if (tableName === 'customer_profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({ data: { city: 'Delhi', state: 'Delhi', locality: 'Connaught Place', pincode: '110001' } }),
            }),
          }),
        };
      }
      return jest.requireActual('@/lib/supabase').supabase.from(tableName); // Fallback for other tables
    });

    render(<LocationDisplay />);
    await waitFor(() => {
      expect(screen.getByText('Posting from: Connaught Place, Delhi, Delhi, 110001')).toBeOnTheScreen();
    });
  });

  it('renders location not available if no profile found', async () => {
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'business_profiles' || tableName === 'customer_profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({ data: null }),
            }),
          }),
        };
      }
      return jest.requireActual('@/lib/supabase').supabase.from(tableName); // Fallback for other tables
    });

    render(<LocationDisplay />);
    await waitFor(() => {
      expect(screen.getByText('Location not available')).toBeOnTheScreen();
    });
  });

  it('handles API errors gracefully', async () => {
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'business_profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: () => Promise.reject(new Error('API Error')),
            }),
          }),
        };
      }
      return jest.requireActual('@/lib/supabase').supabase.from(tableName); // Fallback for other tables
    });

    render(<LocationDisplay />);
    await waitFor(() => {
      expect(screen.getByText('Location not available')).toBeOnTheScreen();
    });
  });

  it('applies dark mode styles', async () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    (supabase.from as jest.Mock).mockImplementation((tableName: string) => {
      if (tableName === 'business_profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({ data: { city: 'Mumbai', state: 'Maharashtra', locality: 'Bandra', pincode: '400050' } }),
            }),
          }),
        };
      }
      return jest.requireActual('@/lib/supabase').supabase.from(tableName); // Fallback for other tables
    });

    render(<LocationDisplay />);
    await waitFor(() => {
      // Check for dark mode specific styles (e.g., background color, text color)
      // This is a simplified check, a snapshot test would be more comprehensive for styles.
      expect(screen.getByText('Posting from:').parent?.props.style).toContainEqual({
        backgroundColor: '#1F2937',
      });
    });
  });
});