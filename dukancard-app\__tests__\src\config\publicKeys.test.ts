import { SUPABASE_CONFIG, GOOGLE_OAUTH_CONFIG, APP_CONFIG, BACKEND_CONFIG } from '@/src/config/publicKeys';

describe('Public Keys Configuration', () => {
  // Mock __DEV__ and process.env.NODE_ENV for consistent testing
  const originalDev = __DEV__;
  const originalNodeEnv = process.env.NODE_ENV;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(() => {
    (global as any).__DEV__ = originalDev;
    process.env.NODE_ENV = originalNodeEnv;
  });

  it('SUPABASE_CONFIG should have correct values', () => {
    expect(SUPABASE_CONFIG.url).toBe('https://rnjolcoecogzgglnblqn.supabase.co');
    expect(SUPABASE_CONFIG.anonKey).toBe('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o');
  });

  describe('GOOGLE_OAUTH_CONFIG', () => {
    it('should have correct iOS client ID', () => {
      expect(GOOGLE_OAUTH_CONFIG.iosClientId).toBe('110991972471-9g5hbuj78s88b5fi0m97i4k0823v7430.apps.googleusercontent.com');
    });

    it('should have correct web client ID', () => {
      expect(GOOGLE_OAUTH_CONFIG.webClientId).toBe('110991972471-ek9ra016ca12ucaobil2s8oid86k6to4.apps.googleusercontent.com');
    });

    it('should have production Android client ID in production environment', () => {
      process.env.NODE_ENV = 'production';
      // Re-import to get the updated value based on NODE_ENV
      jest.resetModules();
      const { GOOGLE_OAUTH_CONFIG: ProdGoogleOAuthConfig } = require('@/src/config/publicKeys');
      expect(ProdGoogleOAuthConfig.androidClientId).toBe('110991972471-bauq8cmll9nlrdl6ok7svvt5rgfuhno7.apps.googleusercontent.com');
    });

    it('should have development Android client ID in development environment', () => {
      process.env.NODE_ENV = 'development';
      // Re-import to get the updated value based on NODE_ENV
      jest.resetModules();
      const { GOOGLE_OAUTH_CONFIG: DevGoogleOAuthConfig } = require('@/src/config/publicKeys');
      expect(DevGoogleOAuthConfig.androidClientId).toBe('110991972471-jskkg6qg8g33mk1qrv9el5u0bj0f8cql.apps.googleusercontent.com');
    });
  });

  describe('APP_CONFIG', () => {
    it('should be development in __DEV__ mode', () => {
      (global as any).__DEV__ = true;
      jest.resetModules();
      const { APP_CONFIG: DevAppConfig } = require('@/src/config/publicKeys');
      expect(DevAppConfig.environment).toBe('development');
      expect(DevAppConfig.enableCrashReporting).toBe(false);
      expect(DevAppConfig.notificationMode).toBe('development');
    });

    it('should be production in non-__DEV__ mode', () => {
      (global as any).__DEV__ = false;
      jest.resetModules();
      const { APP_CONFIG: ProdAppConfig } = require('@/src/config/publicKeys');
      expect(ProdAppConfig.environment).toBe('production');
      expect(ProdAppConfig.enableCrashReporting).toBe(true);
      expect(ProdAppConfig.notificationMode).toBe('production');
    });

    it('should have correct privacy policy URL', () => {
      expect(APP_CONFIG.privacyPolicyUrl).toBe('https://dukancard.in/privacy');
    });
  });

  describe('BACKEND_CONFIG', () => {
    it('should have localhost baseUrl in __DEV__ mode', () => {
      (global as any).__DEV__ = true;
      jest.resetModules();
      const { BACKEND_CONFIG: DevBackendConfig } = require('@/src/config/publicKeys');
      expect(DevBackendConfig.baseUrl).toBe('http://localhost:3000');
    });

    it('should have production baseUrl in non-__DEV__ mode', () => {
      (global as any).__DEV__ = false;
      jest.resetModules();
      const { BACKEND_CONFIG: ProdBackendConfig } = require('@/src/config/publicKeys');
      expect(ProdBackendConfig.baseUrl).toBe('https://dukancard.in');
    });

    it('should have correct apiPrefix', () => {
      expect(BACKEND_CONFIG.apiPrefix).toBe('/api');
    });
  });
});