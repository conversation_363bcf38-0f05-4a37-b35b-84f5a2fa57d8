import { renderHook, act } from '@testing-library/react-hooks';
import { useAuthRefresh, usePeriodicAuthValidation } from '@/src/hooks/useAuthRefresh';
import { useAuth } from '@/src/contexts/AuthContext';

// Mock useAuth hook
jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: jest.fn(),
}));

describe('useAuthRefresh', () => {
  const mockForceRefreshAuth = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue({
      forceRefreshAuth: mockForceRefreshAuth,
    });
  });

  it('should return initial state', () => {
    const { result } = renderHook(() => useAuthRefresh());
    expect(result.current.isRefreshing).toBe(false);
  });

  it('should set isRefreshing to true during refresh and false after success', async () => {
    mockForceRefreshAuth.mockResolvedValueOnce(undefined);

    const { result } = renderHook(() => useAuthRefresh());

    let refreshPromise;
    act(() => {
      refreshPromise = result.current.refreshAuth();
    });

    expect(result.current.isRefreshing).toBe(true);

    await act(async () => {
      await refreshPromise;
    });

    expect(mockForceRefreshAuth).toHaveBeenCalledTimes(1);
    expect(result.current.isRefreshing).toBe(false);
    expect(result.current.refreshAuth()).toEqual({ success: true });
  });

  it('should set isRefreshing to true during refresh and false after failure', async () => {
    const mockError = new Error('Auth refresh failed');
    mockForceRefreshAuth.mockRejectedValueOnce(mockError);

    const { result } = renderHook(() => useAuthRefresh());

    let refreshPromise;
    act(() => {
      refreshPromise = result.current.refreshAuth();
    });

    expect(result.current.isRefreshing).toBe(true);

    await act(async () => {
      await refreshPromise;
    });

    expect(mockForceRefreshAuth).toHaveBeenCalledTimes(1);
    expect(result.current.isRefreshing).toBe(false);
    expect(result.current.refreshAuth()).toEqual({ success: false, error: mockError.message });
  });

  it('should not refresh if already refreshing', async () => {
    mockForceRefreshAuth.mockImplementation(() => new Promise(() => {})); // Never resolves

    const { result } = renderHook(() => useAuthRefresh());

    act(() => {
      result.current.refreshAuth();
    });

    const secondCallResult = act(() => result.current.refreshAuth());

    expect(mockForceRefreshAuth).toHaveBeenCalledTimes(1);
    expect(secondCallResult).toEqual({ success: false, error: 'Refresh already in progress' });
  });
});

describe('usePeriodicAuthValidation', () => {
  const mockForceRefreshAuth = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    (useAuth as jest.Mock).mockReturnValue({
      forceRefreshAuth: mockForceRefreshAuth,
    });
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('should validate auth on mount and periodically', async () => {
    mockForceRefreshAuth.mockResolvedValue(undefined);

    const { result } = renderHook(() => usePeriodicAuthValidation(1000)); // 1 second interval

    // Initial validation on mount
    expect(mockForceRefreshAuth).toHaveBeenCalledTimes(1);
    expect(result.current.validationError).toBeNull();
    expect(result.current.lastValidation).toBeInstanceOf(Date);

    // Advance timers for periodic validation
    jest.advanceTimersByTime(1000);
    expect(mockForceRefreshAuth).toHaveBeenCalledTimes(2);

    jest.advanceTimersByTime(1000);
    expect(mockForceRefreshAuth).toHaveBeenCalledTimes(3);
  });

  it('should set validationError on periodic validation failure', async () => {
    const mockError = new Error('Periodic validation failed');
    mockForceRefreshAuth.mockRejectedValue(mockError);

    const { result } = renderHook(() => usePeriodicAuthValidation(1000));

    await act(async () => {
      jest.runAllTimers();
    });

    expect(result.current.validationError).toBe(mockError.message);
  });

  it('should clear validationError on subsequent success after failure', async () => {
    mockForceRefreshAuth.mockRejectedValueOnce(new Error('First failure'));
    mockForceRefreshAuth.mockResolvedValueOnce(undefined);

    const { result } = renderHook(() => usePeriodicAuthValidation(1000));

    await act(async () => {
      jest.runAllTimers(); // First validation fails
    });
    expect(result.current.validationError).toBe('First failure');

    await act(async () => {
      jest.runAllTimers(); // Second validation succeeds
    });
    expect(result.current.validationError).toBeNull();
    expect(result.current.lastValidation).toBeInstanceOf(Date);
  });
});