{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/planPrioritizer.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Plan Prioritizer - Handles business plan-based post prioritization\r\n * Higher tier businesses get better visibility while maintaining fairness\r\n */\r\n\r\nexport const PLAN_PRIORITY: Record<string, number> = {\r\n  'enterprise': 5,\r\n  'pro': 4,\r\n  'growth': 3,\r\n  'basic': 2,\r\n  'free': 1\r\n};\r\n\r\nexport interface BusinessGroup {\r\n  authorId: string;\r\n  priority: number;\r\n  latestPostTime: number;\r\n  posts: UnifiedPost[];\r\n}\r\n\r\n/**\r\n * Create business priority groups based on subscription plans\r\n * Similar to how LinkedIn prioritizes premium content\r\n */\r\nexport function createBusinessPriorityGroups(businessPosts: UnifiedPost[]): BusinessGroup[] {\r\n  // Group posts by business author\r\n  const businessPostsByAuthor = new Map<string, UnifiedPost[]>();\r\n  businessPosts.forEach(post => {\r\n    if (!businessPostsByAuthor.has(post.author_id)) {\r\n      businessPostsByAuthor.set(post.author_id, []);\r\n    }\r\n    businessPostsByAuthor.get(post.author_id)!.push(post);\r\n  });\r\n\r\n  // Sort posts within each business group chronologically (latest first)\r\n  businessPostsByAuthor.forEach((posts, authorId) => {\r\n    businessPostsByAuthor.set(authorId, posts.sort((a, b) =>\r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n\r\n  // Create priority groups\r\n  return Array.from(businessPostsByAuthor.entries())\r\n    .map(([authorId, authorPosts]) => {\r\n      const latestPost = authorPosts[0];\r\n      const priority = PLAN_PRIORITY[latestPost.business_plan || 'free'] || 1;\r\n      return {\r\n        authorId,\r\n        priority,\r\n        latestPostTime: new Date(latestPost.created_at).getTime(),\r\n        posts: authorPosts\r\n      };\r\n    })\r\n    .sort((a, b) => {\r\n      // Sort by plan priority first\r\n      if (a.priority !== b.priority) {\r\n        return b.priority - a.priority; // Higher priority first\r\n      }\r\n      // If same plan, sort by latest post timestamp\r\n      return b.latestPostTime - a.latestPostTime;\r\n    });\r\n}\r\n\r\n/**\r\n * Distribute business posts with plan-based prioritization\r\n * Uses tier-based round-robin to ensure diversity within each plan level\r\n */\r\nexport function distributePrioritizedBusinessPosts(businessGroups: BusinessGroup[]): UnifiedPost[] {\r\n  const result: UnifiedPost[] = [];\r\n\r\n  // Group businesses by plan priority\r\n  const businessesByPlan = new Map<number, BusinessGroup[]>();\r\n  businessGroups.forEach(business => {\r\n    if (!businessesByPlan.has(business.priority)) {\r\n      businessesByPlan.set(business.priority, []);\r\n    }\r\n    businessesByPlan.get(business.priority)!.push(business);\r\n  });\r\n\r\n  // Sort plan priorities (highest first)\r\n  const sortedPlanPriorities = Array.from(businessesByPlan.keys()).sort((a, b) => b - a);\r\n\r\n  // Distribute posts: round-robin within each plan tier\r\n  for (const planPriority of sortedPlanPriorities) {\r\n    const businessesInPlan = businessesByPlan.get(planPriority)!;\r\n\r\n    // Create queues for round-robin distribution\r\n    const businessPostQueues = businessesInPlan.map(business => ({\r\n      ...business,\r\n      remainingPosts: [...business.posts]\r\n    }));\r\n\r\n    // Round-robin within this plan tier until all posts are distributed\r\n    while (businessPostQueues.some(queue => queue.remainingPosts.length > 0)) {\r\n      businessPostQueues.forEach(business => {\r\n        if (business.remainingPosts.length > 0) {\r\n          const post = business.remainingPosts.shift()!;\r\n          result.push(post);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Get plan display name for UI purposes\r\n */\r\nexport function getPlanDisplayName(planId: string): string {\r\n  const planNames: Record<string, string> = {\r\n    'enterprise': 'Enterprise',\r\n    'pro': 'Pro',\r\n    'growth': 'Growth',\r\n    'basic': 'Basic',\r\n    'free': 'Free'\r\n  };\r\n  \r\n  return planNames[planId] || 'Free';\r\n}\r\n\r\n/**\r\n * Check if a business has premium features based on plan\r\n */\r\nexport function hasPremiumFeatures(planId: string): boolean {\r\n  const priority = PLAN_PRIORITY[planId] || 1;\r\n  return priority >= PLAN_PRIORITY.growth; // Growth and above\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAOO,MAAM,gBAAwC;IACnD,cAAc;IACd,OAAO;IACP,UAAU;IACV,SAAS;IACT,QAAQ;AACV;AAaO,SAAS,6BAA6B,aAA4B;IACvE,iCAAiC;IACjC,MAAM,wBAAwB,IAAI;IAClC,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,CAAC,sBAAsB,GAAG,CAAC,KAAK,SAAS,GAAG;YAC9C,sBAAsB,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QAC9C;QACA,sBAAsB,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IAClD;IAEA,uEAAuE;IACvE,sBAAsB,OAAO,CAAC,CAAC,OAAO;QACpC,sBAAsB,GAAG,CAAC,UAAU,MAAM,IAAI,CAAC,CAAC,GAAG,IACjD,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,yBAAyB;IACzB,OAAO,MAAM,IAAI,CAAC,sBAAsB,OAAO,IAC5C,GAAG,CAAC,CAAC,CAAC,UAAU,YAAY;QAC3B,MAAM,aAAa,WAAW,CAAC,EAAE;QACjC,MAAM,WAAW,aAAa,CAAC,WAAW,aAAa,IAAI,OAAO,IAAI;QACtE,OAAO;YACL;YACA;YACA,gBAAgB,IAAI,KAAK,WAAW,UAAU,EAAE,OAAO;YACvD,OAAO;QACT;IACF,GACC,IAAI,CAAC,CAAC,GAAG;QACR,8BAA8B;QAC9B,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;YAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ,EAAE,wBAAwB;QAC1D;QACA,8CAA8C;QAC9C,OAAO,EAAE,cAAc,GAAG,EAAE,cAAc;IAC5C;AACJ;AAMO,SAAS,mCAAmC,cAA+B;IAChF,MAAM,SAAwB,EAAE;IAEhC,oCAAoC;IACpC,MAAM,mBAAmB,IAAI;IAC7B,eAAe,OAAO,CAAC,CAAA;QACrB,IAAI,CAAC,iBAAiB,GAAG,CAAC,SAAS,QAAQ,GAAG;YAC5C,iBAAiB,GAAG,CAAC,SAAS,QAAQ,EAAE,EAAE;QAC5C;QACA,iBAAiB,GAAG,CAAC,SAAS,QAAQ,EAAG,IAAI,CAAC;IAChD;IAEA,uCAAuC;IACvC,MAAM,uBAAuB,MAAM,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAEpF,sDAAsD;IACtD,KAAK,MAAM,gBAAgB,qBAAsB;QAC/C,MAAM,mBAAmB,iBAAiB,GAAG,CAAC;QAE9C,6CAA6C;QAC7C,MAAM,qBAAqB,iBAAiB,GAAG,CAAC,CAAA,WAAY,CAAC;gBAC3D,GAAG,QAAQ;gBACX,gBAAgB;uBAAI,SAAS,KAAK;iBAAC;YACrC,CAAC;QAED,oEAAoE;QACpE,MAAO,mBAAmB,IAAI,CAAC,CAAA,QAAS,MAAM,cAAc,CAAC,MAAM,GAAG,GAAI;YACxE,mBAAmB,OAAO,CAAC,CAAA;gBACzB,IAAI,SAAS,cAAc,CAAC,MAAM,GAAG,GAAG;oBACtC,MAAM,OAAO,SAAS,cAAc,CAAC,KAAK;oBAC1C,OAAO,IAAI,CAAC;gBACd;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAoC;QACxC,cAAc;QACd,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,WAAW,aAAa,CAAC,OAAO,IAAI;IAC1C,OAAO,YAAY,cAAc,MAAM,EAAE,mBAAmB;AAC9D", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/diversityEngine.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Diversity Engine - Ensures no consecutive posts from the same author\r\n * Inspired by Facebook/Instagram feed algorithms that maintain user engagement\r\n * through content diversity and prevent feed monotony.\r\n */\r\n\r\nexport interface DiversityOptions {\r\n  maxConsecutiveFromSameAuthor?: number;\r\n  prioritizeRecency?: boolean;\r\n}\r\n\r\n/**\r\n * Apply diversity rules to prevent consecutive posts from the same author\r\n * Uses a sliding window approach similar to major social media platforms\r\n */\r\nexport function applyDiversityRules(\r\n  posts: UnifiedPost[], \r\n  options: DiversityOptions = {}\r\n): UnifiedPost[] {\r\n  const { maxConsecutiveFromSameAuthor = 1 } = options;\r\n  \r\n  if (posts.length <= 1) return posts;\r\n\r\n  const diversifiedPosts: UnifiedPost[] = [];\r\n  const remainingPosts = [...posts];\r\n  let lastAuthorId: string | null = null;\r\n  let consecutiveCount = 0;\r\n\r\n  while (remainingPosts.length > 0) {\r\n    let selectedIndex = -1;\r\n    \r\n    // First, try to find a post from a different author\r\n    for (let i = 0; i < remainingPosts.length; i++) {\r\n      const post = remainingPosts[i];\r\n      \r\n      if (post.author_id !== lastAuthorId) {\r\n        selectedIndex = i;\r\n        break;\r\n      }\r\n    }\r\n    \r\n    // If no different author found, or we haven't exceeded consecutive limit\r\n    if (selectedIndex === -1 && consecutiveCount < maxConsecutiveFromSameAuthor) {\r\n      selectedIndex = 0; // Take the first available post\r\n    }\r\n    \r\n    // If still no selection, force diversity by taking first different author\r\n    if (selectedIndex === -1) {\r\n      for (let i = 0; i < remainingPosts.length; i++) {\r\n        if (remainingPosts[i].author_id !== lastAuthorId) {\r\n          selectedIndex = i;\r\n          break;\r\n        }\r\n      }\r\n      // If still no different author, take first available (edge case)\r\n      if (selectedIndex === -1) selectedIndex = 0;\r\n    }\r\n\r\n    const selectedPost = remainingPosts.splice(selectedIndex, 1)[0];\r\n    diversifiedPosts.push(selectedPost);\r\n    \r\n    // Update tracking variables\r\n    if (selectedPost.author_id === lastAuthorId) {\r\n      consecutiveCount++;\r\n    } else {\r\n      consecutiveCount = 1;\r\n      lastAuthorId = selectedPost.author_id;\r\n    }\r\n  }\r\n\r\n  return diversifiedPosts;\r\n}\r\n\r\n/**\r\n * Group posts by author while maintaining chronological order within groups\r\n */\r\nexport function groupPostsByAuthor(posts: UnifiedPost[]): Map<string, UnifiedPost[]> {\r\n  const grouped = new Map<string, UnifiedPost[]>();\r\n  \r\n  posts.forEach(post => {\r\n    if (!grouped.has(post.author_id)) {\r\n      grouped.set(post.author_id, []);\r\n    }\r\n    grouped.get(post.author_id)!.push(post);\r\n  });\r\n  \r\n  // Sort posts within each group chronologically (latest first)\r\n  grouped.forEach((authorPosts, authorId) => {\r\n    grouped.set(authorId, authorPosts.sort((a, b) => \r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n  \r\n  return grouped;\r\n}\r\n\r\n/**\r\n * Round-robin distribution to ensure fair representation\r\n * Similar to how Instagram distributes stories from different accounts\r\n */\r\nexport function roundRobinDistribution(groupedPosts: Map<string, UnifiedPost[]>): UnifiedPost[] {\r\n  const result: UnifiedPost[] = [];\r\n  const queues = Array.from(groupedPosts.entries()).map(([authorId, posts]) => ({\r\n    authorId,\r\n    posts: [...posts]\r\n  }));\r\n\r\n  // Continue until all queues are empty\r\n  while (queues.some(queue => queue.posts.length > 0)) {\r\n    queues.forEach(queue => {\r\n      if (queue.posts.length > 0) {\r\n        const post = queue.posts.shift()!;\r\n        result.push(post);\r\n      }\r\n    });\r\n  }\r\n\r\n  return result;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAiBO,SAAS,oBACd,KAAoB,EACpB,UAA4B,CAAC,CAAC;IAE9B,MAAM,EAAE,+BAA+B,CAAC,EAAE,GAAG;IAE7C,IAAI,MAAM,MAAM,IAAI,GAAG,OAAO;IAE9B,MAAM,mBAAkC,EAAE;IAC1C,MAAM,iBAAiB;WAAI;KAAM;IACjC,IAAI,eAA8B;IAClC,IAAI,mBAAmB;IAEvB,MAAO,eAAe,MAAM,GAAG,EAAG;QAChC,IAAI,gBAAgB,CAAC;QAErB,oDAAoD;QACpD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,MAAM,OAAO,cAAc,CAAC,EAAE;YAE9B,IAAI,KAAK,SAAS,KAAK,cAAc;gBACnC,gBAAgB;gBAChB;YACF;QACF;QAEA,yEAAyE;QACzE,IAAI,kBAAkB,CAAC,KAAK,mBAAmB,8BAA8B;YAC3E,gBAAgB,GAAG,gCAAgC;QACrD;QAEA,0EAA0E;QAC1E,IAAI,kBAAkB,CAAC,GAAG;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;gBAC9C,IAAI,cAAc,CAAC,EAAE,CAAC,SAAS,KAAK,cAAc;oBAChD,gBAAgB;oBAChB;gBACF;YACF;YACA,iEAAiE;YACjE,IAAI,kBAAkB,CAAC,GAAG,gBAAgB;QAC5C;QAEA,MAAM,eAAe,eAAe,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE;QAC/D,iBAAiB,IAAI,CAAC;QAEtB,4BAA4B;QAC5B,IAAI,aAAa,SAAS,KAAK,cAAc;YAC3C;QACF,OAAO;YACL,mBAAmB;YACnB,eAAe,aAAa,SAAS;QACvC;IACF;IAEA,OAAO;AACT;AAKO,SAAS,mBAAmB,KAAoB;IACrD,MAAM,UAAU,IAAI;IAEpB,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,SAAS,GAAG;YAChC,QAAQ,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QAChC;QACA,QAAQ,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IACpC;IAEA,8DAA8D;IAC9D,QAAQ,OAAO,CAAC,CAAC,aAAa;QAC5B,QAAQ,GAAG,CAAC,UAAU,YAAY,IAAI,CAAC,CAAC,GAAG,IACzC,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,OAAO;AACT;AAMO,SAAS,uBAAuB,YAAwC;IAC7E,MAAM,SAAwB,EAAE;IAChC,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM,GAAK,CAAC;YAC5E;YACA,OAAO;mBAAI;aAAM;QACnB,CAAC;IAED,sCAAsC;IACtC,MAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,KAAK,CAAC,MAAM,GAAG,GAAI;QACnD,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG;gBAC1B,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK;gBAC9B,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/optimizedHybridAlgorithm.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\nimport { PLAN_PRIORITY } from './planPrioritizer';\r\nimport { applyDiversityRules } from './diversityEngine';\r\n\r\n/**\r\n * Optimized Hybrid Algorithm for Exact Post Count\r\n * \r\n * Works with exactly the fetched posts (e.g., 10 posts) without losing any content\r\n * Strategy:\r\n * 1. Separate customer and business posts\r\n * 2. Apply plan prioritization to business posts\r\n * 3. Merge customer + business posts by timestamp\r\n * 4. Apply diversity rules\r\n * 5. Return all posts (no loss)\r\n */\r\n\r\nexport interface OptimizedHybridOptions {\r\n  enableDiversity?: boolean;\r\n  maintainChronologicalFlow?: boolean;\r\n}\r\n\r\n/**\r\n * Main optimized hybrid algorithm - processes exactly the fetched posts\r\n */\r\nexport function processOptimizedHybrid(\r\n  posts: UnifiedPost[],\r\n  options: OptimizedHybridOptions = {}\r\n): UnifiedPost[] {\r\n  const {\r\n    enableDiversity = true,\r\n    maintainChronologicalFlow = true\r\n  } = options;\r\n\r\n  if (posts.length === 0) return [];\r\n\r\n  // Separate customer and business posts\r\n  const customerPosts = posts.filter(post => post.post_source === 'customer');\r\n  const businessPosts = posts.filter(post => post.post_source === 'business');\r\n\r\n  // Process customer posts (maintain chronological order)\r\n  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);\r\n\r\n  // Process business posts (apply plan prioritization)\r\n  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);\r\n\r\n  // Merge both types\r\n  const mergedPosts = mergeOptimizedPosts(\r\n    processedCustomerPosts,\r\n    processedBusinessPosts,\r\n    maintainChronologicalFlow\r\n  );\r\n\r\n  // Apply diversity rules if enabled\r\n  return enableDiversity ? applyDiversityRules(mergedPosts) : mergedPosts;\r\n}\r\n\r\n/**\r\n * Process customer posts - simple chronological sort\r\n */\r\nfunction processCustomerPostsOptimized(customerPosts: UnifiedPost[]): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return [];\r\n\r\n  // Sort chronologically (latest first)\r\n  return customerPosts.sort((a, b) => \r\n    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n  );\r\n}\r\n\r\n/**\r\n * Process business posts - apply plan prioritization ONLY to latest post per business\r\n * Other posts from same business compete purely on timestamp\r\n */\r\nfunction processBusinessPostsOptimized(businessPosts: UnifiedPost[]): UnifiedPost[] {\r\n  if (businessPosts.length === 0) return [];\r\n\r\n  // Group posts by business (author_id)\r\n  const postsByBusiness = new Map<string, UnifiedPost[]>();\r\n  businessPosts.forEach(post => {\r\n    if (!postsByBusiness.has(post.author_id)) {\r\n      postsByBusiness.set(post.author_id, []);\r\n    }\r\n    postsByBusiness.get(post.author_id)!.push(post);\r\n  });\r\n\r\n  // Sort posts within each business by timestamp (latest first)\r\n  postsByBusiness.forEach((posts, businessId) => {\r\n    postsByBusiness.set(businessId, posts.sort((a, b) =>\r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n\r\n  // Separate latest posts (get plan priority) from other posts (time-based only)\r\n  const latestPostsPerBusiness: UnifiedPost[] = [];\r\n  const otherPostsFromBusinesses: UnifiedPost[] = [];\r\n\r\n  postsByBusiness.forEach((posts, _businessId) => {\r\n    if (posts.length > 0) {\r\n      // First post is latest (already sorted)\r\n      latestPostsPerBusiness.push(posts[0]);\r\n\r\n      // Rest are other posts from same business\r\n      if (posts.length > 1) {\r\n        otherPostsFromBusinesses.push(...posts.slice(1));\r\n      }\r\n    }\r\n  });\r\n\r\n  // Sort latest posts by plan priority + timestamp\r\n  const prioritizedLatestPosts = latestPostsPerBusiness.sort((a, b) => {\r\n    const planA = a.business_plan || 'free';\r\n    const planB = b.business_plan || 'free';\r\n\r\n    const priorityA = PLAN_PRIORITY[planA] || 1;\r\n    const priorityB = PLAN_PRIORITY[planB] || 1;\r\n\r\n    // Sort by plan priority first\r\n    if (priorityA !== priorityB) {\r\n      return priorityB - priorityA; // Higher priority first\r\n    }\r\n\r\n    // If same plan, sort by timestamp (latest first)\r\n    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n  });\r\n\r\n  // Sort other posts purely by timestamp (no plan priority)\r\n  const timeBasedOtherPosts = otherPostsFromBusinesses.sort((a, b) =>\r\n    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n  );\r\n\r\n  // Return prioritized latest posts first, then time-based other posts\r\n  return [...prioritizedLatestPosts, ...timeBasedOtherPosts];\r\n}\r\n\r\n/**\r\n * Merge customer and business posts with equal treatment\r\n * No priority between customer vs business - only plan priority within business posts\r\n */\r\nfunction mergeOptimizedPosts(\r\n  customerPosts: UnifiedPost[],\r\n  businessPosts: UnifiedPost[],\r\n  maintainChronologicalFlow: boolean\r\n): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return businessPosts;\r\n  if (businessPosts.length === 0) return customerPosts;\r\n\r\n  if (maintainChronologicalFlow) {\r\n    // Merge all posts by timestamp - equal treatment\r\n    return [...customerPosts, ...businessPosts]\r\n      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());\r\n  } else {\r\n    // Business posts first (due to plan prioritization), then customer posts\r\n    return [...businessPosts, ...customerPosts];\r\n  }\r\n}\r\n\r\n/**\r\n * Alternative approach: Intelligent interleaving\r\n * Ensures both customer and business posts get representation\r\n */\r\nexport function processOptimizedHybridWithInterleaving(\r\n  posts: UnifiedPost[],\r\n  options: OptimizedHybridOptions = {}\r\n): UnifiedPost[] {\r\n  const {\r\n    enableDiversity = true,\r\n    maintainChronologicalFlow = true\r\n  } = options;\r\n\r\n  if (posts.length === 0) return [];\r\n\r\n  // Separate and process posts\r\n  const customerPosts = posts.filter(post => post.post_source === 'customer');\r\n  const businessPosts = posts.filter(post => post.post_source === 'business');\r\n\r\n  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);\r\n  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);\r\n\r\n  // Intelligent interleaving\r\n  const interleavedPosts = intelligentInterleave(\r\n    processedCustomerPosts,\r\n    processedBusinessPosts,\r\n    maintainChronologicalFlow\r\n  );\r\n\r\n  // Apply diversity rules if enabled\r\n  return enableDiversity ? applyDiversityRules(interleavedPosts) : interleavedPosts;\r\n}\r\n\r\n/**\r\n * Intelligent interleaving of customer and business posts\r\n */\r\nfunction intelligentInterleave(\r\n  customerPosts: UnifiedPost[],\r\n  businessPosts: UnifiedPost[],\r\n  respectTimestamp: boolean\r\n): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return businessPosts;\r\n  if (businessPosts.length === 0) return customerPosts;\r\n\r\n  const result: UnifiedPost[] = [];\r\n  let customerIndex = 0;\r\n  let businessIndex = 0;\r\n\r\n  // Interleave posts while respecting timestamps if enabled\r\n  while (customerIndex < customerPosts.length || businessIndex < businessPosts.length) {\r\n    const customerPost = customerPosts[customerIndex];\r\n    const businessPost = businessPosts[businessIndex];\r\n\r\n    if (!customerPost && businessPost) {\r\n      // Only business posts left\r\n      result.push(businessPost);\r\n      businessIndex++;\r\n    } else if (customerPost && !businessPost) {\r\n      // Only customer posts left\r\n      result.push(customerPost);\r\n      customerIndex++;\r\n    } else if (customerPost && businessPost) {\r\n      // Both available - decide based on timestamp or alternating pattern\r\n      if (respectTimestamp) {\r\n        const customerTime = new Date(customerPost.created_at).getTime();\r\n        const businessTime = new Date(businessPost.created_at).getTime();\r\n        \r\n        if (businessTime >= customerTime) {\r\n          result.push(businessPost);\r\n          businessIndex++;\r\n        } else {\r\n          result.push(customerPost);\r\n          customerIndex++;\r\n        }\r\n      } else {\r\n        // Alternating pattern - business posts get slight preference due to plan prioritization\r\n        if (result.length % 2 === 0) {\r\n          result.push(businessPost);\r\n          businessIndex++;\r\n        } else {\r\n          result.push(customerPost);\r\n          customerIndex++;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Get statistics for the optimized algorithm\r\n */\r\nexport function getOptimizedAlgorithmStats(\r\n  originalPosts: UnifiedPost[],\r\n  processedPosts: UnifiedPost[]\r\n): {\r\n  originalCount: number;\r\n  processedCount: number;\r\n  customerPosts: number;\r\n  businessPosts: number;\r\n  planDistribution: Record<string, number>;\r\n  postsLost: number;\r\n  efficiency: number;\r\n} {\r\n  const planDistribution: Record<string, number> = {};\r\n  \r\n  const businessPosts = processedPosts.filter(p => p.post_source === 'business');\r\n  const customerPosts = processedPosts.filter(p => p.post_source === 'customer');\r\n  \r\n  businessPosts.forEach(post => {\r\n    const plan = post.business_plan || 'free';\r\n    planDistribution[plan] = (planDistribution[plan] || 0) + 1;\r\n  });\r\n\r\n  const postsLost = originalPosts.length - processedPosts.length;\r\n  const efficiency = processedPosts.length / originalPosts.length;\r\n\r\n  return {\r\n    originalCount: originalPosts.length,\r\n    processedCount: processedPosts.length,\r\n    customerPosts: customerPosts.length,\r\n    businessPosts: businessPosts.length,\r\n    planDistribution,\r\n    postsLost,\r\n    efficiency\r\n  };\r\n}\r\n\r\n/**\r\n * Validate that no posts are lost (should always be 100% with optimized algorithm)\r\n */\r\nexport function validateOptimizedAlgorithm(\r\n  originalPosts: UnifiedPost[],\r\n  processedPosts: UnifiedPost[]\r\n): {\r\n  isValid: boolean;\r\n  issues: string[];\r\n  efficiency: number;\r\n} {\r\n  const issues: string[] = [];\r\n  \r\n  if (originalPosts.length !== processedPosts.length) {\r\n    issues.push(`Post count mismatch: ${originalPosts.length} → ${processedPosts.length}`);\r\n  }\r\n\r\n  const originalIds = new Set(originalPosts.map(p => p.id));\r\n  const processedIds = new Set(processedPosts.map(p => p.id));\r\n  \r\n  const lostPosts: string[] = [];\r\n  originalIds.forEach(id => {\r\n    if (!processedIds.has(id)) {\r\n      lostPosts.push(id);\r\n    }\r\n  });\r\n\r\n  if (lostPosts.length > 0) {\r\n    issues.push(`Lost posts: ${lostPosts.join(', ')}`);\r\n  }\r\n\r\n  const efficiency = processedPosts.length / originalPosts.length;\r\n\r\n  return {\r\n    isValid: issues.length === 0,\r\n    issues,\r\n    efficiency\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAsBO,SAAS,uBACd,KAAoB,EACpB,UAAkC,CAAC,CAAC;IAEpC,MAAM,EACJ,kBAAkB,IAAI,EACtB,4BAA4B,IAAI,EACjC,GAAG;IAEJ,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;IAEjC,uCAAuC;IACvC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAChE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAEhE,wDAAwD;IACxD,MAAM,yBAAyB,8BAA8B;IAE7D,qDAAqD;IACrD,MAAM,yBAAyB,8BAA8B;IAE7D,mBAAmB;IACnB,MAAM,cAAc,oBAClB,wBACA,wBACA;IAGF,mCAAmC;IACnC,OAAO,kBAAkB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;AAC9D;AAEA;;CAEC,GACD,SAAS,8BAA8B,aAA4B;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;IAEzC,sCAAsC;IACtC,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAC5B,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;AAErE;AAEA;;;CAGC,GACD,SAAS,8BAA8B,aAA4B;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;IAEzC,sCAAsC;IACtC,MAAM,kBAAkB,IAAI;IAC5B,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,SAAS,GAAG;YACxC,gBAAgB,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QACxC;QACA,gBAAgB,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IAC5C;IAEA,8DAA8D;IAC9D,gBAAgB,OAAO,CAAC,CAAC,OAAO;QAC9B,gBAAgB,GAAG,CAAC,YAAY,MAAM,IAAI,CAAC,CAAC,GAAG,IAC7C,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,+EAA+E;IAC/E,MAAM,yBAAwC,EAAE;IAChD,MAAM,2BAA0C,EAAE;IAElD,gBAAgB,OAAO,CAAC,CAAC,OAAO;QAC9B,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,wCAAwC;YACxC,uBAAuB,IAAI,CAAC,KAAK,CAAC,EAAE;YAEpC,0CAA0C;YAC1C,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,yBAAyB,IAAI,IAAI,MAAM,KAAK,CAAC;YAC/C;QACF;IACF;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,uBAAuB,IAAI,CAAC,CAAC,GAAG;QAC7D,MAAM,QAAQ,EAAE,aAAa,IAAI;QACjC,MAAM,QAAQ,EAAE,aAAa,IAAI;QAEjC,MAAM,YAAY,0IAAA,CAAA,gBAAa,CAAC,MAAM,IAAI;QAC1C,MAAM,YAAY,0IAAA,CAAA,gBAAa,CAAC,MAAM,IAAI;QAE1C,8BAA8B;QAC9B,IAAI,cAAc,WAAW;YAC3B,OAAO,YAAY,WAAW,wBAAwB;QACxD;QAEA,iDAAiD;QACjD,OAAO,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAC1E;IAEA,0DAA0D;IAC1D,MAAM,sBAAsB,yBAAyB,IAAI,CAAC,CAAC,GAAG,IAC5D,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAGnE,qEAAqE;IACrE,OAAO;WAAI;WAA2B;KAAoB;AAC5D;AAEA;;;CAGC,GACD,SAAS,oBACP,aAA4B,EAC5B,aAA4B,EAC5B,yBAAkC;IAElC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,IAAI,2BAA2B;QAC7B,iDAAiD;QACjD,OAAO;eAAI;eAAkB;SAAc,CACxC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IACrF,OAAO;QACL,yEAAyE;QACzE,OAAO;eAAI;eAAkB;SAAc;IAC7C;AACF;AAMO,SAAS,uCACd,KAAoB,EACpB,UAAkC,CAAC,CAAC;IAEpC,MAAM,EACJ,kBAAkB,IAAI,EACtB,4BAA4B,IAAI,EACjC,GAAG;IAEJ,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;IAEjC,6BAA6B;IAC7B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAChE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAEhE,MAAM,yBAAyB,8BAA8B;IAC7D,MAAM,yBAAyB,8BAA8B;IAE7D,2BAA2B;IAC3B,MAAM,mBAAmB,sBACvB,wBACA,wBACA;IAGF,mCAAmC;IACnC,OAAO,kBAAkB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,oBAAoB;AACnE;AAEA;;CAEC,GACD,SAAS,sBACP,aAA4B,EAC5B,aAA4B,EAC5B,gBAAyB;IAEzB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,MAAM,SAAwB,EAAE;IAChC,IAAI,gBAAgB;IACpB,IAAI,gBAAgB;IAEpB,0DAA0D;IAC1D,MAAO,gBAAgB,cAAc,MAAM,IAAI,gBAAgB,cAAc,MAAM,CAAE;QACnF,MAAM,eAAe,aAAa,CAAC,cAAc;QACjD,MAAM,eAAe,aAAa,CAAC,cAAc;QAEjD,IAAI,CAAC,gBAAgB,cAAc;YACjC,2BAA2B;YAC3B,OAAO,IAAI,CAAC;YACZ;QACF,OAAO,IAAI,gBAAgB,CAAC,cAAc;YACxC,2BAA2B;YAC3B,OAAO,IAAI,CAAC;YACZ;QACF,OAAO,IAAI,gBAAgB,cAAc;YACvC,oEAAoE;YACpE,IAAI,kBAAkB;gBACpB,MAAM,eAAe,IAAI,KAAK,aAAa,UAAU,EAAE,OAAO;gBAC9D,MAAM,eAAe,IAAI,KAAK,aAAa,UAAU,EAAE,OAAO;gBAE9D,IAAI,gBAAgB,cAAc;oBAChC,OAAO,IAAI,CAAC;oBACZ;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF,OAAO;gBACL,wFAAwF;gBACxF,IAAI,OAAO,MAAM,GAAG,MAAM,GAAG;oBAC3B,OAAO,IAAI,CAAC;oBACZ;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,2BACd,aAA4B,EAC5B,cAA6B;IAU7B,MAAM,mBAA2C,CAAC;IAElD,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK;IACnE,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK;IAEnE,cAAc,OAAO,CAAC,CAAA;QACpB,MAAM,OAAO,KAAK,aAAa,IAAI;QACnC,gBAAgB,CAAC,KAAK,GAAG,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI;IAC3D;IAEA,MAAM,YAAY,cAAc,MAAM,GAAG,eAAe,MAAM;IAC9D,MAAM,aAAa,eAAe,MAAM,GAAG,cAAc,MAAM;IAE/D,OAAO;QACL,eAAe,cAAc,MAAM;QACnC,gBAAgB,eAAe,MAAM;QACrC,eAAe,cAAc,MAAM;QACnC,eAAe,cAAc,MAAM;QACnC;QACA;QACA;IACF;AACF;AAKO,SAAS,2BACd,aAA4B,EAC5B,cAA6B;IAM7B,MAAM,SAAmB,EAAE;IAE3B,IAAI,cAAc,MAAM,KAAK,eAAe,MAAM,EAAE;QAClD,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,cAAc,MAAM,CAAC,GAAG,EAAE,eAAe,MAAM,EAAE;IACvF;IAEA,MAAM,cAAc,IAAI,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IACvD,MAAM,eAAe,IAAI,IAAI,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAEzD,MAAM,YAAsB,EAAE;IAC9B,YAAY,OAAO,CAAC,CAAA;QAClB,IAAI,CAAC,aAAa,GAAG,CAAC,KAAK;YACzB,UAAU,IAAI,CAAC;QACjB;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,OAAO;IACnD;IAEA,MAAM,aAAa,eAAe,MAAM,GAAG,cAAc,MAAM;IAE/D,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/postCreationHandler.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Post Creation Handler - Manages immediate post visibility after creation\r\n * \r\n * Behavior:\r\n * 1. When user creates a post -> Show at top immediately (instant feedback)\r\n * 2. When user refreshes -> Apply normal algorithm (proper positioning)\r\n * \r\n * This provides excellent UX while maintaining algorithmic integrity\r\n */\r\n\r\nexport interface PostCreationState {\r\n  justCreatedPostId?: string;\r\n  sessionId?: string;\r\n  createdAt?: string;\r\n}\r\n\r\nexport interface FeedWithCreationState {\r\n  posts: UnifiedPost[];\r\n  hasJustCreatedPost: boolean;\r\n  justCreatedPost?: UnifiedPost;\r\n}\r\n\r\n/**\r\n * Handle feed display when user just created a post\r\n * Shows new post at top for immediate feedback\r\n */\r\nexport function handlePostCreationFeed(\r\n  algorithmicPosts: UnifiedPost[],\r\n  creationState: PostCreationState\r\n): FeedWithCreationState {\r\n  \r\n  if (!creationState.justCreatedPostId) {\r\n    // No recent post creation, return normal algorithmic feed\r\n    return {\r\n      posts: algorithmicPosts,\r\n      hasJustCreatedPost: false\r\n    };\r\n  }\r\n\r\n  // Find the just-created post in the algorithmic results\r\n  const justCreatedPost = algorithmicPosts.find(\r\n    post => post.id === creationState.justCreatedPostId\r\n  );\r\n\r\n  if (!justCreatedPost) {\r\n    // Post not found in current page, return normal feed\r\n    // (Post might be on a different page due to algorithm)\r\n    return {\r\n      posts: algorithmicPosts,\r\n      hasJustCreatedPost: false\r\n    };\r\n  }\r\n\r\n  // Remove the post from its algorithmic position\r\n  const otherPosts = algorithmicPosts.filter(\r\n    post => post.id !== creationState.justCreatedPostId\r\n  );\r\n\r\n  // Show just-created post at the top\r\n  return {\r\n    posts: [justCreatedPost, ...otherPosts],\r\n    hasJustCreatedPost: true,\r\n    justCreatedPost\r\n  };\r\n}\r\n\r\n/**\r\n * Create post creation state after successful post creation\r\n */\r\nexport function createPostCreationState(\r\n  postId: string,\r\n  sessionId?: string\r\n): PostCreationState {\r\n  return {\r\n    justCreatedPostId: postId,\r\n    sessionId: sessionId || generateSessionId(),\r\n    createdAt: new Date().toISOString()\r\n  };\r\n}\r\n\r\n/**\r\n * Check if post creation state is still valid (within session)\r\n */\r\nexport function isPostCreationStateValid(\r\n  creationState: PostCreationState,\r\n  currentSessionId?: string\r\n): boolean {\r\n  if (!creationState.justCreatedPostId) return false;\r\n  \r\n  // Check if it's the same session\r\n  if (creationState.sessionId && currentSessionId) {\r\n    return creationState.sessionId === currentSessionId;\r\n  }\r\n\r\n  // Check if creation was recent (within last 5 minutes as fallback)\r\n  if (creationState.createdAt) {\r\n    const createdTime = new Date(creationState.createdAt).getTime();\r\n    const now = new Date().getTime();\r\n    const fiveMinutes = 5 * 60 * 1000;\r\n    \r\n    return (now - createdTime) < fiveMinutes;\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\n/**\r\n * Clear post creation state (call on refresh or navigation)\r\n */\r\nexport function clearPostCreationState(): PostCreationState {\r\n  return {};\r\n}\r\n\r\n/**\r\n * Generate a simple session ID for tracking\r\n */\r\nfunction generateSessionId(): string {\r\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n}\r\n\r\n/**\r\n * Enhanced feed response that includes creation state information\r\n */\r\nexport interface EnhancedFeedResponse {\r\n  success: boolean;\r\n  message: string;\r\n  error?: string;\r\n  data?: {\r\n    items: UnifiedPost[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    hasJustCreatedPost: boolean;\r\n    justCreatedPost?: UnifiedPost;\r\n    creationState?: PostCreationState;\r\n  };\r\n}\r\n\r\n/**\r\n * Process feed with post creation handling\r\n */\r\nexport function processFeedWithCreationHandling(\r\n  algorithmicPosts: UnifiedPost[],\r\n  totalCount: number,\r\n  hasMore: boolean,\r\n  creationState?: PostCreationState\r\n): EnhancedFeedResponse {\r\n\r\n  if (!creationState || !creationState.justCreatedPostId) {\r\n    // No post creation state, return normal feed\r\n    return {\r\n      success: true,\r\n      message: 'Posts fetched successfully',\r\n      data: {\r\n        items: algorithmicPosts,\r\n        totalCount,\r\n        hasMore,\r\n        hasJustCreatedPost: false\r\n      }\r\n    };\r\n  }\r\n\r\n  // Handle post creation display\r\n  const feedWithCreation = handlePostCreationFeed(algorithmicPosts, creationState);\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Posts fetched successfully',\r\n    data: {\r\n      items: feedWithCreation.posts,\r\n      totalCount,\r\n      hasMore,\r\n      hasJustCreatedPost: feedWithCreation.hasJustCreatedPost,\r\n      justCreatedPost: feedWithCreation.justCreatedPost,\r\n      creationState\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Client-side helper to manage post creation state in localStorage/sessionStorage\r\n */\r\nexport const PostCreationStateManager = {\r\n  \r\n  /**\r\n   * Save post creation state to session storage\r\n   */\r\n  save(state: PostCreationState): void {\r\n    if (typeof window !== 'undefined') {\r\n      sessionStorage.setItem('post_creation_state', JSON.stringify(state));\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Load post creation state from session storage\r\n   */\r\n  load(): PostCreationState {\r\n    if (typeof window !== 'undefined') {\r\n      const stored = sessionStorage.getItem('post_creation_state');\r\n      if (stored) {\r\n        try {\r\n          return JSON.parse(stored);\r\n        } catch (e) {\r\n          console.warn('Failed to parse post creation state:', e);\r\n        }\r\n      }\r\n    }\r\n    return {};\r\n  },\r\n\r\n  /**\r\n   * Clear post creation state from session storage\r\n   */\r\n  clear(): void {\r\n    if (typeof window !== 'undefined') {\r\n      sessionStorage.removeItem('post_creation_state');\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Check if current state is valid and clear if not\r\n   */\r\n  validateAndClean(): PostCreationState {\r\n    const state = this.load();\r\n    const currentSessionId = this.getCurrentSessionId();\r\n    \r\n    if (!isPostCreationStateValid(state, currentSessionId)) {\r\n      this.clear();\r\n      return {};\r\n    }\r\n    \r\n    return state;\r\n  },\r\n\r\n  /**\r\n   * Get or create current session ID\r\n   */\r\n  getCurrentSessionId(): string {\r\n    if (typeof window !== 'undefined') {\r\n      let sessionId = sessionStorage.getItem('current_session_id');\r\n      if (!sessionId) {\r\n        sessionId = generateSessionId();\r\n        sessionStorage.setItem('current_session_id', sessionId);\r\n      }\r\n      return sessionId;\r\n    }\r\n    return generateSessionId();\r\n  }\r\n};\r\n\r\n/**\r\n * Hook-like function for React components to manage post creation state\r\n */\r\nexport function usePostCreationState() {\r\n  const load = () => PostCreationStateManager.validateAndClean();\r\n  const save = (state: PostCreationState) => PostCreationStateManager.save(state);\r\n  const clear = () => PostCreationStateManager.clear();\r\n  \r\n  return { load, save, clear };\r\n}\r\n\r\n/**\r\n * Utility to mark a post as just created (call after successful post creation)\r\n */\r\nexport function markPostAsJustCreated(postId: string): void {\r\n  const state = createPostCreationState(\r\n    postId, \r\n    PostCreationStateManager.getCurrentSessionId()\r\n  );\r\n  PostCreationStateManager.save(state);\r\n}\r\n\r\n/**\r\n * Utility to check if we should show the \"just posted\" indicator\r\n */\r\nexport function shouldShowJustPostedIndicator(\r\n  post: UnifiedPost, \r\n  creationState?: PostCreationState\r\n): boolean {\r\n  if (!creationState || !creationState.justCreatedPostId) return false;\r\n  return post.id === creationState.justCreatedPostId;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AA4BO,SAAS,uBACd,gBAA+B,EAC/B,aAAgC;IAGhC,IAAI,CAAC,cAAc,iBAAiB,EAAE;QACpC,0DAA0D;QAC1D,OAAO;YACL,OAAO;YACP,oBAAoB;QACtB;IACF;IAEA,wDAAwD;IACxD,MAAM,kBAAkB,iBAAiB,IAAI,CAC3C,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,iBAAiB;IAGrD,IAAI,CAAC,iBAAiB;QACpB,qDAAqD;QACrD,uDAAuD;QACvD,OAAO;YACL,OAAO;YACP,oBAAoB;QACtB;IACF;IAEA,gDAAgD;IAChD,MAAM,aAAa,iBAAiB,MAAM,CACxC,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,iBAAiB;IAGrD,oCAAoC;IACpC,OAAO;QACL,OAAO;YAAC;eAAoB;SAAW;QACvC,oBAAoB;QACpB;IACF;AACF;AAKO,SAAS,wBACd,MAAc,EACd,SAAkB;IAElB,OAAO;QACL,mBAAmB;QACnB,WAAW,aAAa;QACxB,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAKO,SAAS,yBACd,aAAgC,EAChC,gBAAyB;IAEzB,IAAI,CAAC,cAAc,iBAAiB,EAAE,OAAO;IAE7C,iCAAiC;IACjC,IAAI,cAAc,SAAS,IAAI,kBAAkB;QAC/C,OAAO,cAAc,SAAS,KAAK;IACrC;IAEA,mEAAmE;IACnE,IAAI,cAAc,SAAS,EAAE;QAC3B,MAAM,cAAc,IAAI,KAAK,cAAc,SAAS,EAAE,OAAO;QAC7D,MAAM,MAAM,IAAI,OAAO,OAAO;QAC9B,MAAM,cAAc,IAAI,KAAK;QAE7B,OAAO,AAAC,MAAM,cAAe;IAC/B;IAEA,OAAO;AACT;AAKO,SAAS;IACd,OAAO,CAAC;AACV;AAEA;;CAEC,GACD,SAAS;IACP,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAsBO,SAAS,gCACd,gBAA+B,EAC/B,UAAkB,EAClB,OAAgB,EAChB,aAAiC;IAGjC,IAAI,CAAC,iBAAiB,CAAC,cAAc,iBAAiB,EAAE;QACtD,6CAA6C;QAC7C,OAAO;YACL,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP;gBACA;gBACA,oBAAoB;YACtB;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,uBAAuB,kBAAkB;IAElE,OAAO;QACL,SAAS;QACT,SAAS;QACT,MAAM;YACJ,OAAO,iBAAiB,KAAK;YAC7B;YACA;YACA,oBAAoB,iBAAiB,kBAAkB;YACvD,iBAAiB,iBAAiB,eAAe;YACjD;QACF;IACF;AACF;AAKO,MAAM,2BAA2B;IAEtC;;GAEC,GACD,MAAK,KAAwB;QAC3B,wCAAmC;YACjC,eAAe,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD;QACE,wCAAmC;YACjC,MAAM,SAAS,eAAe,OAAO,CAAC;YACtC,IAAI,QAAQ;gBACV,IAAI;oBACF,OAAO,KAAK,KAAK,CAAC;gBACpB,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,wCAAwC;gBACvD;YACF;QACF;QACA,OAAO,CAAC;IACV;IAEA;;GAEC,GACD;QACE,wCAAmC;YACjC,eAAe,UAAU,CAAC;QAC5B;IACF;IAEA;;GAEC,GACD;QACE,MAAM,QAAQ,IAAI,CAAC,IAAI;QACvB,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;QAEjD,IAAI,CAAC,yBAAyB,OAAO,mBAAmB;YACtD,IAAI,CAAC,KAAK;YACV,OAAO,CAAC;QACV;QAEA,OAAO;IACT;IAEA;;GAEC,GACD;QACE,wCAAmC;YACjC,IAAI,YAAY,eAAe,OAAO,CAAC;YACvC,IAAI,CAAC,WAAW;gBACd,YAAY;gBACZ,eAAe,OAAO,CAAC,sBAAsB;YAC/C;YACA,OAAO;QACT;;IAEF;AACF;AAKO,SAAS;IACd,MAAM,OAAO,IAAM,yBAAyB,gBAAgB;IAC5D,MAAM,OAAO,CAAC,QAA6B,yBAAyB,IAAI,CAAC;IACzE,MAAM,QAAQ,IAAM,yBAAyB,KAAK;IAElD,OAAO;QAAE;QAAM;QAAM;IAAM;AAC7B;AAKO,SAAS,sBAAsB,MAAc;IAClD,MAAM,QAAQ,wBACZ,QACA,yBAAyB,mBAAmB;IAE9C,yBAAyB,IAAI,CAAC;AAChC;AAKO,SAAS,8BACd,IAAiB,EACjB,aAAiC;IAEjC,IAAI,CAAC,iBAAiB,CAAC,cAAc,iBAAiB,EAAE,OAAO;IAC/D,OAAO,KAAK,EAAE,KAAK,cAAc,iBAAiB;AACpD", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/unifiedFeed.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/client';\r\nimport { FeedQueryParams } from '@/lib/types/posts';\r\nimport { processOptimizedHybrid } from '@/lib/utils/feed/optimizedHybridAlgorithm';\r\nimport {\r\n  processFeedWithCreationHandling,\r\n  PostCreationState,\r\n  EnhancedFeedResponse\r\n} from '@/lib/utils/feed/postCreationHandler';\r\n\r\nexport interface UnifiedPost {\r\n  id: string;\r\n  post_source: 'business' | 'customer';\r\n  author_id: string;\r\n  content: string;\r\n  image_url: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  city_slug: string | null;\r\n  state_slug: string | null;\r\n  locality_slug: string | null;\r\n  pincode: string | null;\r\n  product_ids: string[];\r\n  mentioned_business_ids: string[];\r\n  author_name: string | null;\r\n  author_avatar: string | null;\r\n  business_slug: string | null; // Business slug for business posts, null for customer posts\r\n  phone: string | null; // Phone number for business posts, null for customer posts\r\n  whatsapp_number: string | null; // WhatsApp number for business posts, null for customer posts\r\n  business_plan: string | null; // Plan for business posts, null for customer posts\r\n}\r\n\r\nexport interface UnifiedFeedResponse {\r\n  success: boolean;\r\n  message: string;\r\n  error?: string;\r\n  data?: {\r\n    items: UnifiedPost[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Get unified feed posts (business + customer posts) with proper pagination\r\n * Supports post creation state for immediate feedback when user creates a post\r\n */\r\nexport async function getUnifiedFeedPosts(\r\n  params: FeedQueryParams,\r\n  creationState?: PostCreationState\r\n): Promise<EnhancedFeedResponse> {\r\n  const supabase = createClient();\r\n  const {\r\n    filter = 'smart',\r\n    page = 1,\r\n    limit = 10,\r\n    city_slug,\r\n    state_slug,\r\n    locality_slug,\r\n    pincode\r\n  } = params;\r\n\r\n  try {\r\n    // Get current user for smart and subscribed filters\r\n    const { data: { user } } = await supabase.auth.getUser();\r\n\r\n    // Build base query\r\n    let query = supabase\r\n      .from('unified_posts')\r\n      .select('*', { count: 'exact' });\r\n\r\n    // Apply filters based on filter type\r\n    switch (filter) {\r\n      case 'smart':\r\n        if (user) {\r\n          // Get user's subscribed businesses for smart feed\r\n          const { data: subscriptions } = await supabase\r\n            .from('subscriptions')\r\n            .select('business_profile_id')\r\n            .eq('user_id', user.id);\r\n\r\n          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];\r\n\r\n          // Try to get user's location from both customer and business profiles\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase\r\n              .from('customer_profiles')\r\n              .select('city_slug, state_slug, locality_slug, pincode')\r\n              .eq('id', user.id)\r\n              .single(),\r\n            supabase\r\n              .from('business_profiles')\r\n              .select('city_slug, state_slug, locality_slug, pincode')\r\n              .eq('id', user.id)\r\n              .single()\r\n          ]);\r\n\r\n          // Use whichever profile exists\r\n          const userProfile = customerProfile.data || businessProfile.data;\r\n\r\n          // Build smart feed conditions\r\n          const conditions = [];\r\n\r\n          // Subscribed businesses\r\n          if (subscribedBusinessIds.length > 0) {\r\n            conditions.push(`and(post_source.eq.business,author_id.in.(${subscribedBusinessIds.join(',')}))`);\r\n          }\r\n\r\n          // User's own posts (check both customer and business posts)\r\n          conditions.push(`and(post_source.eq.customer,author_id.eq.${user.id})`);\r\n          conditions.push(`and(post_source.eq.business,author_id.eq.${user.id})`);\r\n\r\n          // Local posts based on user location\r\n          if (userProfile?.locality_slug) {\r\n            conditions.push(`locality_slug.eq.${userProfile.locality_slug}`);\r\n          }\r\n          if (userProfile?.pincode) {\r\n            conditions.push(`pincode.eq.${userProfile.pincode}`);\r\n          }\r\n          if (userProfile?.city_slug) {\r\n            conditions.push(`city_slug.eq.${userProfile.city_slug}`);\r\n          }\r\n\r\n          if (conditions.length > 0) {\r\n            query = query.or(conditions.join(','));\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'subscribed':\r\n        if (user) {\r\n          const { data: subscriptions } = await supabase\r\n            .from('subscriptions')\r\n            .select('business_profile_id')\r\n            .eq('user_id', user.id);\r\n\r\n          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];\r\n\r\n          if (subscribedBusinessIds.length > 0) {\r\n            query = query\r\n              .eq('post_source', 'business')\r\n              .in('author_id', subscribedBusinessIds);\r\n          } else {\r\n            // No subscriptions, return empty result\r\n            return {\r\n              success: true,\r\n              message: 'No subscribed businesses found',\r\n              data: {\r\n                items: [],\r\n                totalCount: 0,\r\n                hasMore: false,\r\n                hasJustCreatedPost: false\r\n              }\r\n            };\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'locality':\r\n        if (locality_slug) {\r\n          query = query.eq('locality_slug', locality_slug);\r\n        } else if (user) {\r\n          // If no locality_slug provided, get user's locality from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('locality_slug').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('locality_slug').eq('id', user.id).single()\r\n          ]);\r\n          const userLocality = customerProfile.data?.locality_slug || businessProfile.data?.locality_slug;\r\n          if (userLocality) {\r\n            query = query.eq('locality_slug', userLocality);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'pincode':\r\n        if (pincode) {\r\n          query = query.eq('pincode', pincode);\r\n        } else if (user) {\r\n          // If no pincode provided, get user's pincode from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('pincode').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('pincode').eq('id', user.id).single()\r\n          ]);\r\n          const userPincode = customerProfile.data?.pincode || businessProfile.data?.pincode;\r\n          if (userPincode) {\r\n            query = query.eq('pincode', userPincode);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'city':\r\n        if (city_slug) {\r\n          query = query.eq('city_slug', city_slug);\r\n        } else if (user) {\r\n          // If no city_slug provided, get user's city from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('city_slug').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('city_slug').eq('id', user.id).single()\r\n          ]);\r\n          const userCity = customerProfile.data?.city_slug || businessProfile.data?.city_slug;\r\n          if (userCity) {\r\n            query = query.eq('city_slug', userCity);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'state':\r\n        if (state_slug) {\r\n          query = query.eq('state_slug', state_slug);\r\n        } else if (user) {\r\n          // If no state_slug provided, get user's state from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('state_slug').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('state_slug').eq('id', user.id).single()\r\n          ]);\r\n          const userState = customerProfile.data?.state_slug || businessProfile.data?.state_slug;\r\n          if (userState) {\r\n            query = query.eq('state_slug', userState);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'all':\r\n        // No additional filters for 'all'\r\n        break;\r\n    }\r\n\r\n    // Fetch exactly the target number of posts to prevent post loss\r\n    // Algorithm will arrange these posts optimally without losing any content\r\n    const from = (page - 1) * limit; // Standard pagination\r\n    const to = from + limit - 1;\r\n\r\n    // Execute query with chronological ordering (prioritization applied client-side)\r\n    const { data, error, count } = await query\r\n      .order('created_at', { ascending: false })\r\n      .range(from, to);\r\n\r\n    if (error) {\r\n      console.error('Error fetching unified feed posts:', error);\r\n      return {\r\n        success: false,\r\n        message: 'Failed to fetch posts',\r\n        error: error.message\r\n      };\r\n    }\r\n\r\n    // Apply optimized hybrid algorithm to ALL feed types\r\n    // Processes exactly the fetched posts without losing any content\r\n    // Business posts get plan prioritization, customer posts maintain chronological order\r\n    // Works with location filters (locality, pincode, city, state, all)\r\n    const prioritizedData = data ? processOptimizedHybrid(data, {\r\n      enableDiversity: true,\r\n      maintainChronologicalFlow: true\r\n    }) : [];\r\n\r\n    const totalCount = count || 0;\r\n    // Standard pagination logic - no posts lost\r\n    const hasMore = prioritizedData.length === limit && (from + limit) < totalCount;\r\n\r\n    // Handle post creation state for immediate feedback\r\n    return processFeedWithCreationHandling(\r\n      prioritizedData,\r\n      totalCount,\r\n      hasMore,\r\n      creationState\r\n    );\r\n\r\n  } catch (error) {\r\n    console.error('Unexpected error in getUnifiedFeedPosts:', error);\r\n    return {\r\n      success: false,\r\n      message: 'An unexpected error occurred',\r\n      error: error instanceof Error ? error.message : 'Unknown error'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get unified feed posts with author information populated\r\n * Author information is now included directly in the unified_posts view\r\n */\r\nexport async function getUnifiedFeedPostsWithAuthors(\r\n  params: FeedQueryParams,\r\n  creationState?: PostCreationState\r\n): Promise<EnhancedFeedResponse> {\r\n  // Since author information is now included in the unified_posts view,\r\n  // we can just return the result from getUnifiedFeedPosts directly\r\n  return await getUnifiedFeedPosts(params, creationState);\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;AA2CO,eAAe,oBACpB,MAAuB,EACvB,aAAiC;IAEjC,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EACJ,SAAS,OAAO,EAChB,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,SAAS,EACT,UAAU,EACV,aAAa,EACb,OAAO,EACR,GAAG;IAEJ,IAAI;QACF,oDAAoD;QACpD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEtD,mBAAmB;QACnB,IAAI,QAAQ,SACT,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ;QAEhC,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,IAAI,MAAM;oBACR,kDAAkD;oBAClD,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,iBACL,MAAM,CAAC,uBACP,EAAE,CAAC,WAAW,KAAK,EAAE;oBAExB,MAAM,wBAAwB,eAAe,IAAI,CAAA,IAAK,EAAE,mBAAmB,KAAK,EAAE;oBAElF,sEAAsE;oBACtE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,iDACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBACT,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,iDACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;qBACV;oBAED,+BAA+B;oBAC/B,MAAM,cAAc,gBAAgB,IAAI,IAAI,gBAAgB,IAAI;oBAEhE,8BAA8B;oBAC9B,MAAM,aAAa,EAAE;oBAErB,wBAAwB;oBACxB,IAAI,sBAAsB,MAAM,GAAG,GAAG;wBACpC,WAAW,IAAI,CAAC,CAAC,0CAA0C,EAAE,sBAAsB,IAAI,CAAC,KAAK,EAAE,CAAC;oBAClG;oBAEA,4DAA4D;oBAC5D,WAAW,IAAI,CAAC,CAAC,yCAAyC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBACtE,WAAW,IAAI,CAAC,CAAC,yCAAyC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBAEtE,qCAAqC;oBACrC,IAAI,aAAa,eAAe;wBAC9B,WAAW,IAAI,CAAC,CAAC,iBAAiB,EAAE,YAAY,aAAa,EAAE;oBACjE;oBACA,IAAI,aAAa,SAAS;wBACxB,WAAW,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY,OAAO,EAAE;oBACrD;oBACA,IAAI,aAAa,WAAW;wBAC1B,WAAW,IAAI,CAAC,CAAC,aAAa,EAAE,YAAY,SAAS,EAAE;oBACzD;oBAEA,IAAI,WAAW,MAAM,GAAG,GAAG;wBACzB,QAAQ,MAAM,EAAE,CAAC,WAAW,IAAI,CAAC;oBACnC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,MAAM;oBACR,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,iBACL,MAAM,CAAC,uBACP,EAAE,CAAC,WAAW,KAAK,EAAE;oBAExB,MAAM,wBAAwB,eAAe,IAAI,CAAA,IAAK,EAAE,mBAAmB,KAAK,EAAE;oBAElF,IAAI,sBAAsB,MAAM,GAAG,GAAG;wBACpC,QAAQ,MACL,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,aAAa;oBACrB,OAAO;wBACL,wCAAwC;wBACxC,OAAO;4BACL,SAAS;4BACT,SAAS;4BACT,MAAM;gCACJ,OAAO,EAAE;gCACT,YAAY;gCACZ,SAAS;gCACT,oBAAoB;4BACtB;wBACF;oBACF;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,eAAe;oBACjB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;gBACpC,OAAO,IAAI,MAAM;oBACf,uEAAuE;oBACvE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBACnF,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBACpF;oBACD,MAAM,eAAe,gBAAgB,IAAI,EAAE,iBAAiB,gBAAgB,IAAI,EAAE;oBAClF,IAAI,cAAc;wBAChB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;oBACpC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS;oBACX,QAAQ,MAAM,EAAE,CAAC,WAAW;gBAC9B,OAAO,IAAI,MAAM;oBACf,gEAAgE;oBAChE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,WAAW,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBAC7E,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,WAAW,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBAC9E;oBACD,MAAM,cAAc,gBAAgB,IAAI,EAAE,WAAW,gBAAgB,IAAI,EAAE;oBAC3E,IAAI,aAAa;wBACf,QAAQ,MAAM,EAAE,CAAC,WAAW;oBAC9B;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,WAAW;oBACb,QAAQ,MAAM,EAAE,CAAC,aAAa;gBAChC,OAAO,IAAI,MAAM;oBACf,+DAA+D;oBAC/D,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBAC/E,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBAChF;oBACD,MAAM,WAAW,gBAAgB,IAAI,EAAE,aAAa,gBAAgB,IAAI,EAAE;oBAC1E,IAAI,UAAU;wBACZ,QAAQ,MAAM,EAAE,CAAC,aAAa;oBAChC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,YAAY;oBACd,QAAQ,MAAM,EAAE,CAAC,cAAc;gBACjC,OAAO,IAAI,MAAM;oBACf,iEAAiE;oBACjE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBAChF,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBACjF;oBACD,MAAM,YAAY,gBAAgB,IAAI,EAAE,cAAc,gBAAgB,IAAI,EAAE;oBAC5E,IAAI,WAAW;wBACb,QAAQ,MAAM,EAAE,CAAC,cAAc;oBACjC;gBACF;gBACA;YAEF,KAAK;gBAEH;QACJ;QAEA,gEAAgE;QAChE,0EAA0E;QAC1E,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,sBAAsB;QACvD,MAAM,KAAK,OAAO,QAAQ;QAE1B,iFAAiF;QACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,MAClC,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,MAAM;QAEf,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,MAAM,OAAO;YACtB;QACF;QAEA,qDAAqD;QACrD,iEAAiE;QACjE,sFAAsF;QACtF,oEAAoE;QACpE,MAAM,kBAAkB,OAAO,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;YAC1D,iBAAiB;YACjB,2BAA2B;QAC7B,KAAK,EAAE;QAEP,MAAM,aAAa,SAAS;QAC5B,4CAA4C;QAC5C,MAAM,UAAU,gBAAgB,MAAM,KAAK,SAAS,AAAC,OAAO,QAAS;QAErE,oDAAoD;QACpD,OAAO,CAAA,GAAA,8IAAA,CAAA,kCAA+B,AAAD,EACnC,iBACA,YACA,SACA;IAGJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAMO,eAAe,+BACpB,MAAuB,EACvB,aAAiC;IAEjC,sEAAsE;IACtE,kEAAkE;IAClE,OAAO,MAAM,oBAAoB,QAAQ;AAC3C", "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/icons/WhatsAppIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n// Directly use React.SVGProps for type safety without an empty interface\r\nconst WhatsAppIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (\r\n  <svg\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    viewBox=\"0 0 24 24\"\r\n    fill=\"currentColor\" // Reverted to fill\r\n    {...props} // Spread any additional props like className, style, etc.\r\n  >\r\n    <path\r\n      d={\r\n        \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z\"\r\n      }\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport default WhatsAppIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,yEAAyE;AACzE,MAAM,eAAwD,CAAC,sBAC7D,6LAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK,eAAe,mBAAmB;;QACtC,GAAG,KAAK;kBAET,cAAA,6LAAC;YACC,GACE;;;;;;;;;;;KATF;uCAeS", "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/PostActions.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { MessageCircle, Phone, Heart, Share2 } from 'lucide-react';\r\nimport WhatsAppIcon from '@/app/components/icons/WhatsAppIcon';\r\n\r\ninterface PostActionsProps {\r\n  business: {\r\n    business_name: string | null;\r\n    whatsapp_number: string | null;\r\n    phone: string | null;\r\n  };\r\n  hasWhatsApp: boolean;\r\n  hasPhone: boolean;\r\n  _postId: string;\r\n  onShare?: () => void;\r\n}\r\n\r\nexport default function PostActions({ business, hasWhatsApp, hasPhone, _postId, onShare }: PostActionsProps) {\r\n  // Handle contact actions\r\n  const handleWhatsAppClick = () => {\r\n    if (hasWhatsApp) {\r\n      const whatsappNumber = business.whatsapp_number?.replace(/\\D/g, ''); // Remove non-digits\r\n      const message = `Hi ${business.business_name}, I saw your post and would like to know more.`;\r\n      window.open(`https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`, '_blank');\r\n    }\r\n  };\r\n\r\n  const handlePhoneClick = () => {\r\n    if (hasPhone) {\r\n      window.open(`tel:${business.phone}`, '_self');\r\n    }\r\n  };\r\n\r\n  const handleShareClick = () => {\r\n    if (onShare) {\r\n      onShare();\r\n    }\r\n  };\r\n\r\n  const _buttonVariants = {\r\n    hover: { scale: 1.02, transition: { duration: 0.2 } },\r\n    tap: { scale: 0.98, transition: { duration: 0.1 } }\r\n  };\r\n\r\n  // Only show buttons if they have valid contact info - match React Native behavior\r\n  const showWhatsApp = hasWhatsApp;\r\n  const showPhone = hasPhone;\r\n\r\n  // If neither button should be shown, don't render anything\r\n  if (!showWhatsApp && !showPhone) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-between w-full\">\r\n      {/* Left side - Social interaction icons */}\r\n      <div className=\"flex gap-4\">\r\n        <button\r\n          className=\"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors\"\r\n          title=\"Like\"\r\n          disabled\r\n        >\r\n          <Heart className=\"h-5 w-5\" />\r\n        </button>\r\n        <button\r\n          className=\"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors\"\r\n          title=\"Comment\"\r\n          disabled\r\n        >\r\n          <MessageCircle className=\"h-5 w-5\" />\r\n        </button>\r\n        <button\r\n          className=\"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors\"\r\n          title=\"Share\"\r\n          onClick={handleShareClick}\r\n        >\r\n          <Share2 className=\"h-5 w-5\" />\r\n        </button>\r\n      </div>\r\n\r\n      {/* Right side - Contact actions */}\r\n      <div className=\"flex gap-4\">\r\n        {showWhatsApp && (\r\n          <button\r\n            onClick={handleWhatsAppClick}\r\n            className=\"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors\"\r\n            title=\"WhatsApp\"\r\n          >\r\n            <WhatsAppIcon className=\"h-5 w-5\" />\r\n          </button>\r\n        )}\r\n\r\n        {showPhone && (\r\n          <button\r\n            onClick={handlePhoneClick}\r\n            className=\"p-1 text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 transition-colors\"\r\n            title=\"Call Now\"\r\n          >\r\n            <Phone className=\"h-5 w-5\" />\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AAHA;;;;AAiBe,SAAS,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAoB;IACzG,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,IAAI,aAAa;YACf,MAAM,iBAAiB,SAAS,eAAe,EAAE,QAAQ,OAAO,KAAK,oBAAoB;YACzF,MAAM,UAAU,CAAC,GAAG,EAAE,SAAS,aAAa,CAAC,8CAA8C,CAAC;YAC5F,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,eAAe,MAAM,EAAE,mBAAmB,UAAU,EAAE;QACrF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,UAAU;YACZ,OAAO,IAAI,CAAC,CAAC,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE;QACvC;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,SAAS;YACX;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAO;YAAE,OAAO;YAAM,YAAY;gBAAE,UAAU;YAAI;QAAE;QACpD,KAAK;YAAE,OAAO;YAAM,YAAY;gBAAE,UAAU;YAAI;QAAE;IACpD;IAEA,kFAAkF;IAClF,MAAM,eAAe;IACrB,MAAM,YAAY;IAElB,2DAA2D;IAC3D,IAAI,CAAC,gBAAgB,CAAC,WAAW;QAC/B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,QAAQ;kCAER,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,QAAQ;kCAER,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;kCAE3B,6LAAC;wBACC,WAAU;wBACV,OAAM;wBACN,SAAS;kCAET,cAAA,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKtB,6LAAC;gBAAI,WAAU;;oBACZ,8BACC,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,8IAAA,CAAA,UAAY;4BAAC,WAAU;;;;;;;;;;;oBAI3B,2BACC,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAM7B;KAvFwB", "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ProductListItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { NearbyProduct } from \"@/app/(main)/discover/actions/types\";\r\n\r\ninterface ProductListItemProps {\r\n  product: ProductServiceData | NearbyProduct;\r\n  isLink?: boolean;\r\n}\r\n\r\n// Helper to format currency\r\nconst formatCurrency = (amount: number | null | undefined) => {\r\n  if (amount === null || amount === undefined) return null;\r\n  return amount.toLocaleString(\"en-IN\", {\r\n    style: \"currency\",\r\n    currency: \"INR\",\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 2,\r\n  });\r\n};\r\n\r\n// Animation variants\r\nconst cardContainerVariants = {\r\n  hidden: { opacity: 0, y: 10 },\r\n  show: { opacity: 1, y: 0 },\r\n};\r\n\r\nconst discountBadgeVariants = {\r\n  initial: { scale: 0.9, opacity: 0 },\r\n  animate: {\r\n    scale: 1,\r\n    opacity: 1,\r\n    transition: {\r\n      duration: 0.5,\r\n      type: \"spring\",\r\n      stiffness: 400,\r\n      damping: 10,\r\n    },\r\n  },\r\n  hover: {\r\n    scale: 1.05,\r\n    rotate: -2,\r\n    transition: { type: \"spring\", stiffness: 500 },\r\n  },\r\n};\r\n\r\nexport default function ProductListItem({\r\n  product,\r\n  isLink = true,\r\n}: ProductListItemProps) {\r\n  // If isLink is true, we'll wrap the content in a link\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  const formattedBasePrice = formatCurrency(product.base_price);\r\n  const formattedDiscountedPrice = formatCurrency(product.discounted_price); // Format discounted price\r\n\r\n  // Determine final price and if there's a discount shown\r\n  let finalPrice = formattedBasePrice;\r\n  let priceToShowStrikethrough: string | null = null;\r\n  let discountPercentage = 0;\r\n\r\n  const hasDiscountedPrice =\r\n    typeof product.discounted_price === \"number\" &&\r\n    product.discounted_price > 0;\r\n  const hasBasePrice =\r\n    typeof product.base_price === \"number\" && product.base_price > 0;\r\n\r\n  if (\r\n    hasDiscountedPrice &&\r\n    hasBasePrice &&\r\n    product.discounted_price! < product.base_price!\r\n  ) {\r\n    // Scenario 1: Discounted price is valid and less than base price\r\n    finalPrice = formattedDiscountedPrice;\r\n    priceToShowStrikethrough = formattedBasePrice; // Strike through base price\r\n    discountPercentage = Math.round(\r\n      ((product.base_price! - product.discounted_price!) /\r\n        product.base_price!) *\r\n        100\r\n    );\r\n  } else {\r\n    // Scenario 2: No discount applicable, show base price\r\n    finalPrice = formattedBasePrice;\r\n    priceToShowStrikethrough = null;\r\n    discountPercentage = 0;\r\n  }\r\n\r\n  // Ensure finalPrice has a fallback if both prices are null/undefined\r\n  if (!finalPrice) {\r\n    finalPrice = \"Price unavailable\";\r\n  }\r\n\r\n  const showDiscountBadge = discountPercentage > 0;\r\n\r\n  // State for lazy loading images\r\n  const [isImageLoaded, setIsImageLoaded] = useState(false);\r\n\r\n  // Check if product is out of stock\r\n  const isOutOfStock = !product.is_available;\r\n\r\n  // Ensure we're not using business_id as a key\r\n  // Use the product's own ID for any keys needed\r\n  const content = (\r\n    <motion.div\r\n      variants={cardContainerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"show\"\r\n      className=\"w-full overflow-hidden\"\r\n    >\r\n      <div className=\"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg\">\r\n        <div className=\"relative w-full overflow-hidden rounded-lg\">\r\n          {/* Image container */}\r\n          <div className=\"relative w-full overflow-hidden rounded-t-xl\">\r\n            {/* Get the featured image from the images array if available, otherwise fall back to image_url */}\r\n            {/* Determine the image URL to use */}\r\n            {(() => {\r\n              // Get the image URL to display\r\n              let imageUrl = product.image_url;\r\n\r\n              // If product has images array and it's not empty, use the featured image\r\n              if (product.images && Array.isArray(product.images) && product.images.length > 0) {\r\n                const featuredIndex = typeof product.featured_image_index === 'number'\r\n                  ? Math.min(product.featured_image_index, product.images.length - 1)\r\n                  : 0;\r\n                imageUrl = product.images[featuredIndex];\r\n              }\r\n\r\n              if (imageUrl && !imageError) {\r\n                return (\r\n                  <div className=\"overflow-hidden\">\r\n                    {!isImageLoaded && (\r\n                      <Skeleton className=\"absolute inset-0 rounded-t-xl\" />\r\n                    )}\r\n                    <motion.div className=\"w-full\">\r\n                      <Image\r\n                        src={imageUrl}\r\n                        alt={product.name ?? \"Product image\"}\r\n                        width={500}\r\n                        height={750}\r\n                        className={`w-full aspect-square object-cover ${\r\n                          isOutOfStock\r\n                            ? \"filter grayscale opacity-70 transition-all duration-500\"\r\n                            : \"\"\r\n                        } ${\r\n                          isImageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n                        } max-w-full`}\r\n                        loading=\"lazy\"\r\n                        onError={() => setImageError(true)}\r\n                        onLoad={() => setIsImageLoaded(true)}\r\n                        quality={80}\r\n                        blurDataURL=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=\"\r\n                        placeholder=\"blur\"\r\n                        style={{ objectFit: \"cover\" }}\r\n                      />\r\n                    </motion.div>\r\n                  </div>\r\n                );\r\n              } else {\r\n                return (\r\n                  <div className=\"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl\">\r\n                    <svg\r\n                      className=\"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={1}\r\n                        d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                );\r\n              }\r\n            })()}\r\n\r\n            {/* Out of Stock Overlay */}\r\n            {isOutOfStock && (\r\n              <div className=\"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40\">\r\n                <div className=\"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground\">\r\n                  <span className=\"font-medium tracking-wide uppercase text-xs sm:text-sm\">\r\n                    Out of Stock\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Discount Badge Overlay */}\r\n            {showDiscountBadge && (\r\n              <AnimatePresence>\r\n                <motion.div\r\n                  key={`discount-badge-${product.id}`}\r\n                  variants={discountBadgeVariants}\r\n                  initial=\"initial\"\r\n                  animate=\"animate\"\r\n                  whileHover=\"hover\"\r\n                  className={cn(\r\n                    \"absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg\",\r\n                    \"bg-destructive\",\r\n                    \"text-destructive-foreground border border-destructive-foreground/20\",\r\n                    \"transform-gpu\"\r\n                  )}\r\n                >\r\n                  <div className=\"flex flex-col items-center justify-center\">\r\n                    <span className=\"text-[7px] sm:text-[9px] md:text-[10px] font-medium\">\r\n                      SAVE\r\n                    </span>\r\n                    <span className=\"text-[9px] sm:text-xs md:text-sm leading-none\">\r\n                      {discountPercentage}%\r\n                    </span>\r\n                  </div>\r\n                </motion.div>\r\n              </AnimatePresence>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content Section */}\r\n          <div className=\"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1\">\r\n            {/* Title */}\r\n            <p className=\"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden\">\r\n              {product.name ?? \"Unnamed Product\"}\r\n            </p>\r\n\r\n            {/* Description (optional) */}\r\n            {product.description && (\r\n              <p className=\"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate\">\r\n                {product.description}\r\n              </p>\r\n            )}\r\n\r\n            {/* Price and Badge Container */}\r\n            <div className=\"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1\">\r\n              {/* Price Group */}\r\n              <div className=\"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full\">\r\n                {finalPrice && (\r\n                  <p className=\"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full\">\r\n                    {finalPrice}\r\n                  </p>\r\n                )}\r\n                {priceToShowStrikethrough && (\r\n                  <p className=\"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500\">\r\n                    {priceToShowStrikethrough}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Product Type Badge removed as per instructions */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n\r\n  // If isLink is true, wrap the content in a link to the product detail page\r\n  if (isLink && \"business_slug\" in product && product.business_slug) {\r\n    return (\r\n      <Link\r\n        href={`/${product.business_slug}/product/${product.slug || product.id}`}\r\n        className=\"block h-full\"\r\n      >\r\n        {content}\r\n      </Link>\r\n    );\r\n  }\r\n\r\n  // Otherwise, just return the content\r\n  return content;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AACA;;;AARA;;;;;;;AAgBA,4BAA4B;AAC5B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,QAAQ,WAAW,WAAW,OAAO;IACpD,OAAO,OAAO,cAAc,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAEA,qBAAqB;AACrB,MAAM,wBAAwB;IAC5B,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;IAAE;AAC3B;AAEA,MAAM,wBAAwB;IAC5B,SAAS;QAAE,OAAO;QAAK,SAAS;IAAE;IAClC,SAAS;QACP,OAAO;QACP,SAAS;QACT,YAAY;YACV,UAAU;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,OAAO;QACL,OAAO;QACP,QAAQ,CAAC;QACT,YAAY;YAAE,MAAM;YAAU,WAAW;QAAI;IAC/C;AACF;AAEe,SAAS,gBAAgB,EACtC,OAAO,EACP,SAAS,IAAI,EACQ;;IACrB,sDAAsD;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,eAAe,QAAQ,UAAU;IAC5D,MAAM,2BAA2B,eAAe,QAAQ,gBAAgB,GAAG,0BAA0B;IAErG,wDAAwD;IACxD,IAAI,aAAa;IACjB,IAAI,2BAA0C;IAC9C,IAAI,qBAAqB;IAEzB,MAAM,qBACJ,OAAO,QAAQ,gBAAgB,KAAK,YACpC,QAAQ,gBAAgB,GAAG;IAC7B,MAAM,eACJ,OAAO,QAAQ,UAAU,KAAK,YAAY,QAAQ,UAAU,GAAG;IAEjE,IACE,sBACA,gBACA,QAAQ,gBAAgB,GAAI,QAAQ,UAAU,EAC9C;QACA,iEAAiE;QACjE,aAAa;QACb,2BAA2B,oBAAoB,4BAA4B;QAC3E,qBAAqB,KAAK,KAAK,CAC7B,AAAC,CAAC,QAAQ,UAAU,GAAI,QAAQ,gBAAgB,AAAC,IAC/C,QAAQ,UAAU,GAClB;IAEN,OAAO;QACL,sDAAsD;QACtD,aAAa;QACb,2BAA2B;QAC3B,qBAAqB;IACvB;IAEA,qEAAqE;IACrE,IAAI,CAAC,YAAY;QACf,aAAa;IACf;IAEA,MAAM,oBAAoB,qBAAqB;IAE/C,gCAAgC;IAChC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,MAAM,eAAe,CAAC,QAAQ,YAAY;IAE1C,8CAA8C;IAC9C,+CAA+C;IAC/C,MAAM,wBACJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BAGZ,CAAC;gCACA,+BAA+B;gCAC/B,IAAI,WAAW,QAAQ,SAAS;gCAEhC,yEAAyE;gCACzE,IAAI,QAAQ,MAAM,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;oCAChF,MAAM,gBAAgB,OAAO,QAAQ,oBAAoB,KAAK,WAC1D,KAAK,GAAG,CAAC,QAAQ,oBAAoB,EAAE,QAAQ,MAAM,CAAC,MAAM,GAAG,KAC/D;oCACJ,WAAW,QAAQ,MAAM,CAAC,cAAc;gCAC1C;gCAEA,IAAI,YAAY,CAAC,YAAY;oCAC3B,qBACE,6LAAC;wCAAI,WAAU;;4CACZ,CAAC,+BACA,6LAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DAEtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAAC,WAAU;0DACpB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAK,QAAQ,IAAI,IAAI;oDACrB,OAAO;oDACP,QAAQ;oDACR,WAAW,CAAC,kCAAkC,EAC5C,eACI,4DACA,GACL,CAAC,EACA,gBAAgB,gBAAgB,YACjC,WAAW,CAAC;oDACb,SAAQ;oDACR,SAAS,IAAM,cAAc;oDAC7B,QAAQ,IAAM,iBAAiB;oDAC/B,SAAS;oDACT,aAAY;oDACZ,aAAY;oDACZ,OAAO;wDAAE,WAAW;oDAAQ;;;;;;;;;;;;;;;;;gCAKtC,OAAO;oCACL,qBACE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;4CACR,OAAM;sDAEN,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;gCAKZ;4BACF,CAAC;4BAGA,8BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAyD;;;;;;;;;;;;;;;;4BAQ9E,mCACC,6LAAC,4LAAA,CAAA,kBAAe;0CACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,YAAW;oCACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qIACA,kBACA,uEACA;8CAGF,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAsD;;;;;;0DAGtE,6LAAC;gDAAK,WAAU;;oDACb;oDAAmB;;;;;;;;;;;;;mCAjBnB,CAAC,eAAe,EAAE,QAAQ,EAAE,EAAE;;;;;;;;;;;;;;;;kCA0B3C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAE,WAAU;0CACV,QAAQ,IAAI,IAAI;;;;;;4BAIlB,QAAQ,WAAW,kBAClB,6LAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;0CAKxB,6LAAC;gCAAI,WAAU;0CAEb,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,4BACC,6LAAC;4CAAE,WAAU;sDACV;;;;;;wCAGJ,0CACC,6LAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAanB,2EAA2E;IAC3E,IAAI,UAAU,mBAAmB,WAAW,QAAQ,aAAa,EAAE;QACjE,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;YACvE,WAAU;sBAET;;;;;;IAGP;IAEA,qCAAqC;IACrC,OAAO;AACT;GAjOwB;KAAA", "debugId": null}}, {"offset": {"line": 1447, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/products/fetchProductsByIds.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { ProductsServices } from \"../../../../dukancard-app/src/types/database/products-services\";\r\n\r\ntype FetchedProduct = Pick<\r\n  ProductsServices,\r\n  \"id\" | \"name\" | \"base_price\" | \"discounted_price\" | \"image_url\" | \"slug\"\r\n>;\r\n\r\n/**\r\n * Fetch products by their IDs using admin client to bypass RLS\r\n * This is used for displaying linked products in feed posts\r\n */\r\nexport async function fetchProductsByIds(productIds: string[]): Promise<{\r\n  success: boolean;\r\n  data?: FetchedProduct[];\r\n  error?: string;\r\n}> {\r\n  if (!productIds || productIds.length === 0) {\r\n    return {\r\n      success: true,\r\n      data: [],\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    const { data, error } = await supabaseAdmin\r\n      .from(\"products_services\")\r\n      .select(\"id, name, base_price, discounted_price, image_url, slug\")\r\n      .in(\"id\", productIds)\r\n      .eq(\"is_available\", true);\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching products by IDs:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in fetchProductsByIds:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAcsB,qBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/addressUtils.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/client';\r\n\r\nexport interface PostAddress {\r\n  locality: string;\r\n  city: string;\r\n  state: string;\r\n  pincode: string;\r\n}\r\n\r\n/**\r\n * Fetch real address data from pincodes table using post slugs\r\n */\r\nexport async function fetchPostAddress(\r\n  locality_slug?: string | null,\r\n  city_slug?: string | null,\r\n  state_slug?: string | null,\r\n  pincode?: string | null\r\n): Promise<PostAddress | null> {\r\n  if (!pincode) {\r\n    return null;\r\n  }\r\n\r\n  const supabase = createClient();\r\n\r\n  try {\r\n    // Build query conditions\r\n    let query = supabase\r\n      .from('pincodes')\r\n      .select('OfficeName, DivisionName, StateName, Pincode')\r\n      .eq('Pincode', pincode);\r\n\r\n    // Add additional filters if available\r\n    if (city_slug) {\r\n      query = query.eq('city_slug', city_slug);\r\n    }\r\n    if (state_slug) {\r\n      query = query.eq('state_slug', state_slug);\r\n    }\r\n    if (locality_slug) {\r\n      query = query.eq('locality_slug', locality_slug);\r\n    }\r\n\r\n    const { data, error } = await query.limit(1);\r\n\r\n    if (error) {\r\n      console.error('Error fetching address data:', error);\r\n      return null;\r\n    }\r\n\r\n    if (!data || data.length === 0) {\r\n      // Fallback: try with just pincode\r\n      const { data: fallbackData, error: fallbackError } = await supabase\r\n        .from('pincodes')\r\n        .select('OfficeName, DivisionName, StateName, Pincode')\r\n        .eq('Pincode', pincode)\r\n        .limit(1);\r\n\r\n      if (fallbackError || !fallbackData || fallbackData.length === 0) {\r\n        return null;\r\n      }\r\n\r\n      const record = fallbackData[0];\r\n      return {\r\n        locality: record.OfficeName || '',\r\n        city: record.DivisionName || '',\r\n        state: record.StateName || '',\r\n        pincode: record.Pincode || '',\r\n      };\r\n    }\r\n\r\n    const record = data[0];\r\n    return {\r\n      locality: record.OfficeName || '',\r\n      city: record.DivisionName || '',\r\n      state: record.StateName || '',\r\n      pincode: record.Pincode || '',\r\n    };\r\n  } catch (error) {\r\n    console.error('Error in fetchPostAddress:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Format address parts into a readable string\r\n */\r\nexport function formatAddressString(address: PostAddress): string {\r\n  const parts = [];\r\n  \r\n  if (address.locality) parts.push(address.locality);\r\n  if (address.city) parts.push(address.city);\r\n  if (address.state) parts.push(address.state);\r\n  if (address.pincode) parts.push(address.pincode);\r\n  \r\n  return parts.join(', ');\r\n}\r\n\r\n/**\r\n * Fallback function to format address from slugs (for when API fails)\r\n */\r\nexport function formatAddressFromSlugs(\r\n  locality_slug?: string | null,\r\n  city_slug?: string | null,\r\n  state_slug?: string | null,\r\n  pincode?: string | null\r\n): string {\r\n  const addressParts = [];\r\n  \r\n  if (locality_slug) {\r\n    addressParts.push(locality_slug.replace(/-/g, ' '));\r\n  }\r\n  if (city_slug) {\r\n    addressParts.push(city_slug.replace(/-/g, ' '));\r\n  }\r\n  if (state_slug) {\r\n    addressParts.push(state_slug.replace(/-/g, ' '));\r\n  }\r\n  if (pincode) {\r\n    addressParts.push(pincode);\r\n  }\r\n  \r\n  return addressParts.join(', ');\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAYO,eAAe,iBACpB,aAA6B,EAC7B,SAAyB,EACzB,UAA0B,EAC1B,OAAuB;IAEvB,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAE5B,IAAI;QACF,yBAAyB;QACzB,IAAI,QAAQ,SACT,IAAI,CAAC,YACL,MAAM,CAAC,gDACP,EAAE,CAAC,WAAW;QAEjB,sCAAsC;QACtC,IAAI,WAAW;YACb,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QACA,IAAI,YAAY;YACd,QAAQ,MAAM,EAAE,CAAC,cAAc;QACjC;QACA,IAAI,eAAe;YACjB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;QAEA,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;YAC9B,kCAAkC;YAClC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,YACL,MAAM,CAAC,gDACP,EAAE,CAAC,WAAW,SACd,KAAK,CAAC;YAET,IAAI,iBAAiB,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;gBAC/D,OAAO;YACT;YAEA,MAAM,SAAS,YAAY,CAAC,EAAE;YAC9B,OAAO;gBACL,UAAU,OAAO,UAAU,IAAI;gBAC/B,MAAM,OAAO,YAAY,IAAI;gBAC7B,OAAO,OAAO,SAAS,IAAI;gBAC3B,SAAS,OAAO,OAAO,IAAI;YAC7B;QACF;QAEA,MAAM,SAAS,IAAI,CAAC,EAAE;QACtB,OAAO;YACL,UAAU,OAAO,UAAU,IAAI;YAC/B,MAAM,OAAO,YAAY,IAAI;YAC7B,OAAO,OAAO,SAAS,IAAI;YAC3B,SAAS,OAAO,OAAO,IAAI;QAC7B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,QAAQ,EAAE;IAEhB,IAAI,QAAQ,QAAQ,EAAE,MAAM,IAAI,CAAC,QAAQ,QAAQ;IACjD,IAAI,QAAQ,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,IAAI;IACzC,IAAI,QAAQ,KAAK,EAAE,MAAM,IAAI,CAAC,QAAQ,KAAK;IAC3C,IAAI,QAAQ,OAAO,EAAE,MAAM,IAAI,CAAC,QAAQ,OAAO;IAE/C,OAAO,MAAM,IAAI,CAAC;AACpB;AAKO,SAAS,uBACd,aAA6B,EAC7B,SAAyB,EACzB,UAA0B,EAC1B,OAAuB;IAEvB,MAAM,eAAe,EAAE;IAEvB,IAAI,eAAe;QACjB,aAAa,IAAI,CAAC,cAAc,OAAO,CAAC,MAAM;IAChD;IACA,IAAI,WAAW;QACb,aAAa,IAAI,CAAC,UAAU,OAAO,CAAC,MAAM;IAC5C;IACA,IAAI,YAAY;QACd,aAAa,IAAI,CAAC,WAAW,OAAO,CAAC,MAAM;IAC7C;IACA,IAAI,SAAS;QACX,aAAa,IAAI,CAAC;IACpB;IAEA,OAAO,aAAa,IAAI,CAAC;AAC3B", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/hooks/usePostOwnership.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\n\r\nexport interface UsePostOwnershipProps {\r\n  postBusinessId?: string;\r\n}\r\n\r\nexport interface UsePostOwnershipReturn {\r\n  isOwner: boolean;\r\n  isLoading: boolean;\r\n  currentUserId: string | null;\r\n}\r\n\r\n/**\r\n * Hook to determine if the current user owns a specific post\r\n */\r\nexport function usePostOwnership({ postBusinessId }: UsePostOwnershipProps): UsePostOwnershipReturn {\r\n  const [isOwner, setIsOwner] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [currentUserId, setCurrentUserId] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const checkOwnership = async () => {\r\n      try {\r\n        const supabase = createClient();\r\n        const { data: { user }, error } = await supabase.auth.getUser();\r\n\r\n        if (error || !user) {\r\n          setIsOwner(false);\r\n          setCurrentUserId(null);\r\n          setIsLoading(false);\r\n          return;\r\n        }\r\n\r\n        setCurrentUserId(user.id);\r\n        \r\n        // Check if the current user's ID matches the post's business_id\r\n        if (postBusinessId && user.id === postBusinessId) {\r\n          setIsOwner(true);\r\n        } else {\r\n          setIsOwner(false);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking post ownership:\", error);\r\n        setIsOwner(false);\r\n        setCurrentUserId(null);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    checkOwnership();\r\n  }, [postBusinessId]);\r\n\r\n  return {\r\n    isOwner,\r\n    isLoading,\r\n    currentUserId,\r\n  };\r\n}\r\n\r\n/**\r\n * Simple utility function to check if user owns a post\r\n */\r\nexport function checkPostOwnership(currentUserId: string | null, postBusinessId: string | undefined): boolean {\r\n  return !!(currentUserId && postBusinessId && currentUserId === postBusinessId);\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;AAHA;;;AAkBO,SAAS,iBAAiB,EAAE,cAAc,EAAyB;;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;6DAAiB;oBACrB,IAAI;wBACF,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;wBAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;wBAE7D,IAAI,SAAS,CAAC,MAAM;4BAClB,WAAW;4BACX,iBAAiB;4BACjB,aAAa;4BACb;wBACF;wBAEA,iBAAiB,KAAK,EAAE;wBAExB,gEAAgE;wBAChE,IAAI,kBAAkB,KAAK,EAAE,KAAK,gBAAgB;4BAChD,WAAW;wBACb,OAAO;4BACL,WAAW;wBACb;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,WAAW;wBACX,iBAAiB;oBACnB,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;qCAAG;QAAC;KAAe;IAEnB,OAAO;QACL;QACA;QACA;IACF;AACF;GA3CgB;AAgDT,SAAS,mBAAmB,aAA4B,EAAE,cAAkC;IACjG,OAAO,CAAC,CAAC,CAAC,iBAAiB,kBAAkB,kBAAkB,cAAc;AAC/E", "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 1651, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/utils.ts"], "sourcesContent": ["/**\r\n * Utility functions for posts\r\n */\r\n\r\nimport { PostData } from '@/lib/types/posts';\r\nimport { UserProfile } from './types';\r\n\r\n/**\r\n * Calculate post score based on base score and time decay\r\n */\r\nexport function calculatePostScore(post: PostData, baseScore: number, _userProfile?: UserProfile): number {\r\n  const now = new Date();\r\n  const postDate = new Date(post.created_at);\r\n  const daysDiff = (now.getTime() - postDate.getTime()) / (1000 * 60 * 60 * 24);\r\n\r\n  let timeDecayFactor = 1.0;\r\n  if (daysDiff <= 1) {\r\n    timeDecayFactor = 1.0;\r\n  } else if (daysDiff <= 7) {\r\n    timeDecayFactor = 0.8;\r\n  } else if (daysDiff <= 28) {\r\n    timeDecayFactor = 0.6;\r\n  } else {\r\n    timeDecayFactor = 0.4;\r\n  }\r\n\r\n  return Math.round(baseScore * timeDecayFactor);\r\n}\r\n\r\n/**\r\n * Get the business post select query with business profile\r\n */\r\nexport function getBusinessPostSelectQuery(): string {\r\n  return `\r\n    *,\r\n    business_profiles!inner (\r\n      id,\r\n      business_name,\r\n      logo_url,\r\n      business_slug,\r\n      phone,\r\n      whatsapp_number,\r\n      city,\r\n      state\r\n    )\r\n  `;\r\n}\r\n\r\n/**\r\n * Build exclusion filter for business IDs\r\n */\r\nexport function buildBusinessIdExclusionFilter(businessIds: string[]): string {\r\n  return businessIds.length > 0 ? `(${businessIds.join(',')})` : 'null';\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAQM,SAAS,mBAAmB,IAAc,EAAE,SAAiB,EAAE,YAA0B;IAC9F,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,KAAK,KAAK,UAAU;IACzC,MAAM,WAAW,CAAC,IAAI,OAAO,KAAK,SAAS,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAE5E,IAAI,kBAAkB;IACtB,IAAI,YAAY,GAAG;QACjB,kBAAkB;IACpB,OAAO,IAAI,YAAY,GAAG;QACxB,kBAAkB;IACpB,OAAO,IAAI,YAAY,IAAI;QACzB,kBAAkB;IACpB,OAAO;QACL,kBAAkB;IACpB;IAEA,OAAO,KAAK,KAAK,CAAC,YAAY;AAChC;AAKO,SAAS;IACd,OAAO,CAAC;;;;;;;;;;;;EAYR,CAAC;AACH;AAKO,SAAS,+BAA+B,WAAqB;IAClE,OAAO,YAAY,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG;AACjE", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/index.ts"], "sourcesContent": ["/**\r\n * Main exports for posts actions\r\n * This file serves as the public API for all posts-related functionality\r\n */\r\n\r\n// CRUD operations\r\nexport { createPost, updatePost, updatePostContent, updatePostProducts, deletePost } from './crud';\r\n\r\n// Unified feed functionality (business + customer posts)\r\nexport { getUnifiedFeedPosts, getUnifiedFeedPostsWithAuthors } from './unifiedFeed';\r\nexport type { UnifiedPost, UnifiedFeedResponse } from './unifiedFeed';\r\n\r\n// Utility functions (if needed externally)\r\nexport { calculatePostScore } from './utils';\r\n\r\n// Types (if needed externally)\r\nexport type { UserProfile, PostWithScore } from './types';\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,kBAAkB;;AAGlB,yDAAyD;AACzD;AAGA,2CAA2C;AAC3C", "debugId": null}}, {"offset": {"line": 1732, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts.ts"], "sourcesContent": ["// This file has been refactored into smaller modules\r\n// Please use the new modular structure in lib/actions/posts/\r\n\r\nexport * from \"./posts/index\";\r\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,6DAA6D;;AAE7D", "debugId": null}}, {"offset": {"line": 1755, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/crud.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { PostFormData } from '@/lib/types/posts';\r\nimport { ActionResponse } from '@/lib/types/api';\r\nimport { deletePostMedia } from '@/lib/actions/shared/upload-post-media';\r\n\r\n/**\r\n * Create a new post\r\n */\r\nexport async function createPost(formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to create a post'\r\n    };\r\n  }\r\n\r\n  // Get the user's business profile\r\n  const { data: businessProfile, error: profileError } = await supabase\r\n    .from('business_profiles')\r\n    .select('id, city_slug, state_slug, locality_slug, pincode, logo_url')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError || !businessProfile) {\r\n    return {\r\n      success: false,\r\n      message: 'Business profile not found',\r\n      error: 'You must have a business profile to create a post'\r\n    };\r\n  }\r\n\r\n  // Prepare post data\r\n  const postData = {\r\n    business_id: user.id,\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    city_slug: businessProfile.city_slug,\r\n    state_slug: businessProfile.state_slug,\r\n    locality_slug: businessProfile.locality_slug,\r\n    pincode: businessProfile.pincode,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    author_avatar: businessProfile.logo_url\r\n  };\r\n\r\n  // Insert the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .insert(postData)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error creating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to create post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post created successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the content of an existing post (for inline editing)\r\n */\r\nexport async function updatePostContent(postId: string, content: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the content and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      content: content.trim(),\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post content:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the product_ids of an existing post (for inline editing)\r\n */\r\nexport async function updatePostProducts(postId: string, productIds: string[]): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the product_ids and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      product_ids: productIds,\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post products:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post products',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post products updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update an existing post (full update for form submissions)\r\n */\r\nexport async function updatePost(postId: string, formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Prepare update data\r\n  const updateData = {\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    updated_at: new Date().toISOString()\r\n  };\r\n\r\n  // Update the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update(updateData)\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Delete a post\r\n */\r\nexport async function deletePost(postId: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to delete a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user, get creation date for media deletion\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id, created_at, image_url')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to delete it'\r\n    };\r\n  }\r\n\r\n  // Always attempt to delete the post folder from storage\r\n  // This ensures we clean up any files that might exist, regardless of image_url status\r\n  try {\r\n    const mediaDeleteResult = await deletePostMedia(user.id, postId, existingPost.created_at);\r\n    if (!mediaDeleteResult.success && mediaDeleteResult.error) {\r\n      console.error('Error deleting post media:', mediaDeleteResult.error);\r\n      // Continue with post deletion even if media deletion fails\r\n    }\r\n  } catch (mediaError) {\r\n    console.error('Error deleting post media:', mediaError);\r\n    // Continue with post deletion even if media deletion fails\r\n  }\r\n\r\n  // Delete the post\r\n  const { error } = await supabase\r\n    .from('business_posts')\r\n    .delete()\r\n    .eq('id', postId);\r\n\r\n  if (error) {\r\n    console.error('Error deleting post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to delete post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post deleted successfully'\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAsNsB,aAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1771, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,6LAAC,yIAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,6LAAC,8HAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,6LAAC,8HAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,6LAAC,8HAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,6LAAC,8HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,6LAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;MAtBS;AAwBT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;;0BAEV,6LAAC,6MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,6LAAC,yIAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1976, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 2057, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/productActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { ProductsServicesRow } from \"@/types/database\";\r\n\r\n// Search business products for the current user\r\nexport async function searchBusinessProducts(query: string): Promise<{\r\n  success: boolean;\r\n  data?: ProductsServicesRow[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!query || query.trim().length < 2) {\r\n      return {\r\n        success: false,\r\n        error: \"Search query must be at least 2 characters long\",\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const {\r\n      data: { user },\r\n      error: authError,\r\n    } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Search products for the current user's business only\r\n    const { data, error } = await supabaseAdmin\r\n      .from(\"products_services\")\r\n      .select(\"*\")\r\n      .eq(\"business_id\", user.id) // Ensure user can only see their own products\r\n      .eq(\"is_available\", true)\r\n      .ilike(\"name\", `%${query.trim()}%`)\r\n      .order(\"name\", { ascending: true })\r\n      .limit(10); // Limit search results to 10 items\r\n\r\n    if (error) {\r\n      console.error(\"Error searching products:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to search products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in searchBusinessProducts:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get selected products by IDs for the current user\r\nexport async function getSelectedProducts(productIds: string[]): Promise<{\r\n  success: boolean;\r\n  data?: ProductsServicesRow[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!productIds || productIds.length === 0) {\r\n      return {\r\n        success: true,\r\n        data: [],\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const {\r\n      data: { user },\r\n      error: authError,\r\n    } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Use regular client - accessing user's own products with authentication\r\n    // Get products by IDs, but only for the current user's business\r\n    const { data, error } = await supabase\r\n      .from(\"products_services\")\r\n      .select(\"*\")\r\n      .in(\"id\", productIds)\r\n      .eq(\"business_id\", user.id) // Ensure user can only access their own products\r\n      .order(\"name\", { ascending: true });\r\n\r\n    if (error) {\r\n      console.error(\"Error getting selected products:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to get selected products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in getSelectedProducts:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAOsB,yBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2073, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/productActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { ProductsServicesRow } from \"@/types/database\";\r\n\r\n// Search business products for the current user\r\nexport async function searchBusinessProducts(query: string): Promise<{\r\n  success: boolean;\r\n  data?: ProductsServicesRow[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!query || query.trim().length < 2) {\r\n      return {\r\n        success: false,\r\n        error: \"Search query must be at least 2 characters long\",\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const {\r\n      data: { user },\r\n      error: authError,\r\n    } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Search products for the current user's business only\r\n    const { data, error } = await supabaseAdmin\r\n      .from(\"products_services\")\r\n      .select(\"*\")\r\n      .eq(\"business_id\", user.id) // Ensure user can only see their own products\r\n      .eq(\"is_available\", true)\r\n      .ilike(\"name\", `%${query.trim()}%`)\r\n      .order(\"name\", { ascending: true })\r\n      .limit(10); // Limit search results to 10 items\r\n\r\n    if (error) {\r\n      console.error(\"Error searching products:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to search products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in searchBusinessProducts:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n// Get selected products by IDs for the current user\r\nexport async function getSelectedProducts(productIds: string[]): Promise<{\r\n  success: boolean;\r\n  data?: ProductsServicesRow[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!productIds || productIds.length === 0) {\r\n      return {\r\n        success: true,\r\n        data: [],\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const {\r\n      data: { user },\r\n      error: authError,\r\n    } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Use regular client - accessing user's own products with authentication\r\n    // Get products by IDs, but only for the current user's business\r\n    const { data, error } = await supabase\r\n      .from(\"products_services\")\r\n      .select(\"*\")\r\n      .in(\"id\", productIds)\r\n      .eq(\"business_id\", user.id) // Ensure user can only access their own products\r\n      .order(\"name\", { ascending: true });\r\n\r\n    if (error) {\r\n      console.error(\"Error getting selected products:\", error);\r\n      return {\r\n        success: false,\r\n        error: \"Failed to get selected products\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || [],\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error in getSelectedProducts:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAsEsB,sBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2089, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/forms/ProductSelector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { Check, ChevronsUpDown, Loader2, X, Package, Search, GripVertical } from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent,\r\n} from '@dnd-kit/core';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  sortableKeyboardCoordinates,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport {\r\n  useSortable,\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from '@/components/ui/command';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { ProductData } from '@/lib/types/posts';\r\nimport { searchBusinessProducts, getSelectedProducts } from '@/lib/actions/shared/productActions';\r\n\r\ninterface ProductSelectorProps {\r\n  selectedProductIds: string[];\r\n  onProductsChange: (_productIds: string[]) => void;\r\n}\r\n\r\n// Sortable Product Item Component\r\ninterface SortableProductItemProps {\r\n  product: ProductData;\r\n  onRemove: (_productId: string) => void;\r\n  formatPrice: (_price: number | null) => string;\r\n}\r\n\r\nfunction SortableProductItem({ product, onRemove, formatPrice }: SortableProductItemProps) {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n    isDragging,\r\n  } = useSortable({ id: product.id });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n    opacity: isDragging ? 0.5 : 1,\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className=\"flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-muted/50 rounded-lg border min-h-[60px] sm:min-h-[68px]\"\r\n    >\r\n      {/* Drag Handle */}\r\n      <div\r\n        {...attributes}\r\n        {...listeners}\r\n        className=\"cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground shrink-0\"\r\n      >\r\n        <GripVertical className=\"h-4 w-4\" />\r\n      </div>\r\n\r\n      {/* Product Image */}\r\n      <div className=\"relative h-8 w-8 sm:h-10 sm:w-10 shrink-0 rounded-md overflow-hidden bg-background\">\r\n        {product.image_url ? (\r\n          <Image\r\n            src={product.image_url}\r\n            alt={product.name}\r\n            fill\r\n            className=\"object-cover\"\r\n            sizes=\"(max-width: 640px) 32px, 40px\"\r\n          />\r\n        ) : (\r\n          <div className=\"flex items-center justify-center h-full w-full\">\r\n            <Package className=\"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground\" />\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Product Details */}\r\n      <div className=\"flex-1 min-w-0 pr-1 sm:pr-2\">\r\n        {/* Product Name - Truncated to single line for consistent height */}\r\n        <div className=\"font-medium text-xs sm:text-sm leading-tight mb-1\">\r\n          <span className=\"line-clamp-1 break-words\">\r\n            {product.name}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Price - Single line layout */}\r\n        <div className=\"text-xs text-muted-foreground\">\r\n          {product.discounted_price ? (\r\n            <div className=\"flex items-center gap-1 flex-wrap\">\r\n              <span className=\"text-primary font-medium\">\r\n                {formatPrice(product.discounted_price)}\r\n              </span>\r\n              <span className=\"line-through text-xs\">\r\n                {formatPrice(product.base_price)}\r\n              </span>\r\n            </div>\r\n          ) : (\r\n            <span className=\"font-medium\">\r\n              {formatPrice(product.base_price)}\r\n            </span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Remove Button */}\r\n      <Button\r\n        variant=\"ghost\"\r\n        size=\"icon\"\r\n        className=\"h-6 w-6 sm:h-8 sm:w-8 shrink-0 hover:bg-destructive/10 hover:text-destructive\"\r\n        onClick={() => onRemove(product.id)}\r\n      >\r\n        <X className=\"h-3 w-3 sm:h-4 sm:w-4\" />\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function ProductSelector({\r\n  selectedProductIds,\r\n  onProductsChange,\r\n}: ProductSelectorProps) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchValue, setSearchValue] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [products, setProducts] = useState<ProductData[]>([]);\r\n  const [selectedProducts, setSelectedProducts] = useState<ProductData[]>([]);\r\n  const [hasSearched, setHasSearched] = useState(false);\r\n\r\n  // Debounce timer ref\r\n  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Drag and drop sensors\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor),\r\n    useSensor(KeyboardSensor, {\r\n      coordinateGetter: sortableKeyboardCoordinates,\r\n    })\r\n  );\r\n\r\n  // Handle drag end\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (active.id !== over?.id) {\r\n      const oldIndex = selectedProducts.findIndex((product) => product.id === active.id);\r\n      const newIndex = selectedProducts.findIndex((product) => product.id === over?.id);\r\n\r\n      if (oldIndex !== -1 && newIndex !== -1) {\r\n        const newSelectedProducts = arrayMove(selectedProducts, oldIndex, newIndex);\r\n        setSelectedProducts(newSelectedProducts);\r\n\r\n        // Update the product IDs array to maintain the new order\r\n        const newSelectedIds = newSelectedProducts.map(product => product.id);\r\n        onProductsChange(newSelectedIds);\r\n      }\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Load selected products details using server action\r\n  const loadSelectedProducts = useCallback(async () => {\r\n    if (selectedProductIds.length === 0) {\r\n      setSelectedProducts([]);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      const result = await getSelectedProducts(selectedProductIds);\r\n\r\n      if (result.success && result.data) {\r\n        // Maintain the order of selectedProductIds\r\n        const orderedProducts = selectedProductIds\r\n          .map(id => result.data?.find(product => product.id === id))\r\n          .filter(Boolean) as ProductData[];\r\n\r\n        setSelectedProducts(orderedProducts);\r\n      } else {\r\n        console.error('Error loading selected products:', result.error);\r\n        setSelectedProducts([]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading selected products:', error);\r\n      setSelectedProducts([]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [selectedProductIds]);\r\n\r\n  // Load selected products on mount\r\n  useEffect(() => {\r\n    if (selectedProductIds.length > 0) {\r\n      loadSelectedProducts();\r\n    }\r\n  }, [selectedProductIds, loadSelectedProducts]);\r\n\r\n  // Search products using server action\r\n  const searchProducts = async (query: string) => {\r\n    setIsLoading(true);\r\n    setHasSearched(false);\r\n    try {\r\n      const result = await searchBusinessProducts(query);\r\n\r\n      if (result.success && result.data) {\r\n        setProducts(result.data);\r\n      } else {\r\n        console.error('Error searching products:', result.error);\r\n        setProducts([]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error searching products:', error);\r\n      setProducts([]);\r\n    } finally {\r\n      setIsLoading(false);\r\n      setHasSearched(true);\r\n    }\r\n  };\r\n\r\n  // Handle search input change with debouncing\r\n  const handleSearchChange = (value: string) => {\r\n    setSearchValue(value);\r\n\r\n    // Clear existing timer\r\n    if (debounceTimerRef.current) {\r\n      clearTimeout(debounceTimerRef.current);\r\n    }\r\n\r\n    // Clear products if search is too short\r\n    if (value.length < 2) {\r\n      setProducts([]);\r\n      setHasSearched(false);\r\n      return;\r\n    }\r\n\r\n    // Set new timer for debounced search\r\n    debounceTimerRef.current = setTimeout(() => {\r\n      searchProducts(value);\r\n    }, 300); // 300ms debounce delay\r\n  };\r\n\r\n  // Cleanup debounce timer on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (debounceTimerRef.current) {\r\n        clearTimeout(debounceTimerRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Toggle product selection\r\n  const toggleProduct = (product: ProductData) => {\r\n    const isSelected = selectedProductIds.includes(product.id);\r\n    let newSelectedIds: string[];\r\n\r\n    if (isSelected) {\r\n      newSelectedIds = selectedProductIds.filter(id => id !== product.id);\r\n      setSelectedProducts(prev => prev.filter(p => p.id !== product.id));\r\n    } else {\r\n      // Check if maximum limit is reached\r\n      if (selectedProductIds.length >= 5) {\r\n        return; // Don't add more products if limit is reached\r\n      }\r\n      newSelectedIds = [...selectedProductIds, product.id];\r\n      setSelectedProducts(prev => [...prev, product]);\r\n    }\r\n\r\n    onProductsChange(newSelectedIds);\r\n  };\r\n\r\n  // Remove a selected product\r\n  const removeProduct = (productId: string) => {\r\n    const newSelectedIds = selectedProductIds.filter(id => id !== productId);\r\n    setSelectedProducts(prev => prev.filter(p => p.id !== productId));\r\n    onProductsChange(newSelectedIds);\r\n  };\r\n\r\n  // Format price display\r\n  const formatPrice = (price: number | null) => {\r\n    if (price === null) return 'N/A';\r\n    return `₹${price.toLocaleString('en-IN')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Product Search Combobox */}\r\n      <Popover open={open} onOpenChange={(newOpen) => {\r\n        setOpen(newOpen);\r\n        if (!newOpen) {\r\n          // Clear search when closing\r\n          setSearchValue('');\r\n          setProducts([]);\r\n          setHasSearched(false);\r\n        }\r\n      }}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            role=\"combobox\"\r\n            aria-expanded={open}\r\n            className=\"w-full justify-between h-auto min-h-[40px] px-3 py-2\"\r\n          >\r\n            <div className=\"flex items-center gap-2\">\r\n              <Search className=\"h-4 w-4 text-muted-foreground\" />\r\n              <span className=\"text-left text-muted-foreground\">\r\n                Search and add products...\r\n              </span>\r\n            </div>\r\n            <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent\r\n          className=\"p-0\"\r\n          align=\"start\"\r\n          sideOffset={4}\r\n          style={{ width: 'var(--radix-popover-trigger-width)' }}\r\n        >\r\n          <Command>\r\n            <CommandInput\r\n              placeholder=\"Search your products...\"\r\n              value={searchValue}\r\n              onValueChange={handleSearchChange}\r\n              className=\"h-9 border-0 focus:ring-0 focus:ring-offset-0\"\r\n            />\r\n            <CommandList className=\"max-h-[300px]\">\r\n              {/* Show loading state */}\r\n              {isLoading && (\r\n                <div className=\"flex items-center justify-center py-8\">\r\n                  <div className=\"flex flex-col items-center gap-2\">\r\n                    <Loader2 className=\"h-6 w-6 animate-spin text-primary\" />\r\n                    <span className=\"text-sm text-muted-foreground\">Searching products...</span>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Show empty state only when not loading and no products */}\r\n              {!isLoading && products.length === 0 && (\r\n                <CommandEmpty>\r\n                  <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n                    <Package className=\"h-8 w-8 text-muted-foreground mb-2\" />\r\n                    <span className=\"text-sm font-medium\">\r\n                      {searchValue.length < 2\r\n                        ? 'Type at least 2 characters to search'\r\n                        : hasSearched\r\n                        ? 'No products found'\r\n                        : ''}\r\n                    </span>\r\n                    {searchValue.length >= 2 && hasSearched && (\r\n                      <span className=\"text-xs text-muted-foreground mt-1\">\r\n                        Try a different search term\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </CommandEmpty>\r\n              )}\r\n\r\n              {/* Show products only when not loading and we have products */}\r\n              {!isLoading && products.length > 0 && (\r\n                <CommandGroup>\r\n                  {products.map((product) => (\r\n                  <CommandItem\r\n                    key={product.id}\r\n                    value={product.slug || product.id}\r\n                    onSelect={() => {\r\n                      // Only allow selection if not already at max limit or if already selected\r\n                      if (selectedProductIds.length < 5 || selectedProductIds.includes(product.id)) {\r\n                        toggleProduct(product);\r\n                        setOpen(false);\r\n                        setSearchValue('');\r\n                        setProducts([]);\r\n                        setHasSearched(false);\r\n                      }\r\n                    }}\r\n                    disabled={selectedProductIds.length >= 5 && !selectedProductIds.includes(product.id)}\r\n                    className={cn(\r\n                      \"flex items-center gap-3 p-3 cursor-pointer\",\r\n                      selectedProductIds.length >= 5 && !selectedProductIds.includes(product.id)\r\n                        ? \"opacity-50 cursor-not-allowed\"\r\n                        : \"\"\r\n                    )}\r\n                  >\r\n                    {/* Product Image */}\r\n                    <div className=\"relative h-10 w-10 shrink-0 rounded-md overflow-hidden bg-muted\">\r\n                      {product.image_url ? (\r\n                        <Image\r\n                          src={product.image_url}\r\n                          alt={product.name}\r\n                          fill\r\n                          className=\"object-cover\"\r\n                          sizes=\"40px\"\r\n                        />\r\n                      ) : (\r\n                        <div className=\"flex items-center justify-center h-full w-full\">\r\n                          <Package className=\"h-5 w-5 text-muted-foreground\" />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Product Details */}\r\n                    <div className=\"flex-1 min-w-0 pr-2\">\r\n                      <div className=\"font-medium text-sm truncate mb-1\">{product.name}</div>\r\n                      <div className=\"text-xs text-muted-foreground\">\r\n                        {product.discounted_price ? (\r\n                          <div className=\"flex items-center gap-1 flex-wrap\">\r\n                            <span className=\"text-primary font-medium\">\r\n                              {formatPrice(product.discounted_price)}\r\n                            </span>\r\n                            <span className=\"line-through text-xs\">\r\n                              {formatPrice(product.base_price)}\r\n                            </span>\r\n                          </div>\r\n                        ) : (\r\n                          <span className=\"font-medium\">\r\n                            {formatPrice(product.base_price)}\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Check Icon */}\r\n                    <Check\r\n                      className={cn(\r\n                        \"ml-auto h-4 w-4\",\r\n                        selectedProductIds.includes(product.id)\r\n                          ? \"opacity-100 text-primary\"\r\n                          : \"opacity-0\"\r\n                      )}\r\n                    />\r\n                    </CommandItem>\r\n                  ))}\r\n                </CommandGroup>\r\n              )}\r\n            </CommandList>\r\n          </Command>\r\n        </PopoverContent>\r\n      </Popover>\r\n\r\n      {/* Selected products with drag and drop */}\r\n      <div className=\"space-y-3\">\r\n        {/* Show loading state when loading selected products */}\r\n        {isLoading && selectedProductIds.length > 0 && selectedProducts.length === 0 && (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center gap-2 text-sm font-medium text-foreground\">\r\n              <Package className=\"h-4 w-4\" />\r\n              <span>Loading Selected Products...</span>\r\n            </div>\r\n            <div className=\"flex justify-center py-6\">\r\n              <div className=\"flex flex-col items-center gap-2\">\r\n                <Loader2 className=\"h-6 w-6 animate-spin text-[var(--brand-gold)]\" />\r\n                <span className=\"text-xs text-muted-foreground\">Fetching product details...</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Show selected products when loaded */}\r\n        {selectedProducts.length > 0 && (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center gap-2 text-sm font-medium text-foreground\">\r\n              <Package className=\"h-4 w-4\" />\r\n              <span>Selected Products</span>\r\n              <span className=\"text-xs text-muted-foreground font-normal\">\r\n                (Drag to reorder)\r\n              </span>\r\n            </div>\r\n            <DndContext\r\n              sensors={sensors}\r\n              collisionDetection={closestCenter}\r\n              onDragEnd={handleDragEnd}\r\n            >\r\n              <SortableContext\r\n                items={selectedProducts.map(p => p.id)}\r\n                strategy={verticalListSortingStrategy}\r\n              >\r\n                <div className=\"grid gap-2\">\r\n                  {selectedProducts.map((product) => (\r\n                    <SortableProductItem\r\n                      key={product.id}\r\n                      product={product}\r\n                      onRemove={removeProduct}\r\n                      formatPrice={formatPrice}\r\n                    />\r\n                  ))}\r\n                </div>\r\n              </SortableContext>\r\n            </DndContext>\r\n          </div>\r\n        )}\r\n\r\n        {/* Product limit indicator */}\r\n        <div className=\"flex justify-between items-center text-xs\">\r\n          <span className=\"text-muted-foreground\">\r\n            {selectedProductIds.length === 0\r\n              ? \"No products selected\"\r\n              : `${selectedProductIds.length} product${selectedProductIds.length !== 1 ? 's' : ''} selected`}\r\n          </span>\r\n          <div className=\"flex items-center gap-1\">\r\n            <span className={cn(\r\n              \"font-medium\",\r\n              selectedProductIds.length >= 5 ? \"text-destructive\" : \"text-muted-foreground\"\r\n            )}>\r\n              {selectedProductIds.length}/5\r\n            </span>\r\n            <span className=\"text-muted-foreground\">max</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Warning message when limit is reached */}\r\n        {selectedProductIds.length >= 5 && (\r\n          <div className=\"flex items-center gap-2 p-2 bg-destructive/10 text-destructive rounded-md\">\r\n            <Package className=\"h-4 w-4 shrink-0\" />\r\n            <span className=\"text-xs font-medium\">\r\n              Maximum limit of 5 products reached\r\n            </span>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AASA;AASA;AACA;AACA;AAQA;AAMA;AAEA;AAAA;;;AAzCA;;;;;;;;;;;;;AAuDA,SAAS,oBAAoB,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAA4B;;IACvF,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,QAAQ,EAAE;IAAC;IAEjC,MAAM,QAAQ;QACZ,WAAW,wKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;QACA,SAAS,aAAa,MAAM;IAC9B;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,OAAO;QACP,WAAU;;0BAGV,6LAAC;gBACE,GAAG,UAAU;gBACb,GAAG,SAAS;gBACb,WAAU;0BAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;0BAI1B,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,SAAS,iBAChB,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK,QAAQ,SAAS;oBACtB,KAAK,QAAQ,IAAI;oBACjB,IAAI;oBACJ,WAAU;oBACV,OAAM;;;;;yCAGR,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAMzB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCACb,QAAQ,IAAI;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,gBAAgB,iBACvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,YAAY,QAAQ,gBAAgB;;;;;;8CAEvC,6LAAC;oCAAK,WAAU;8CACb,YAAY,QAAQ,UAAU;;;;;;;;;;;iDAInC,6LAAC;4BAAK,WAAU;sCACb,YAAY,QAAQ,UAAU;;;;;;;;;;;;;;;;;0BAOvC,6LAAC,8HAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,SAAS,QAAQ,EAAE;0BAElC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;GAvFS;;QAQH,sKAAA,CAAA,cAAW;;;KARR;AAyFM,SAAS,gBAAgB,EACtC,kBAAkB,EAClB,gBAAgB,EACK;;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEvD,wBAAwB;IACxB,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,sKAAA,CAAA,8BAA2B;IAC/C;IAGF,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,OAAO,EAAE,KAAK,MAAM,IAAI;YAC1B,MAAM,WAAW,iBAAiB,SAAS,CAAC,CAAC,UAAY,QAAQ,EAAE,KAAK,OAAO,EAAE;YACjF,MAAM,WAAW,iBAAiB,SAAS,CAAC,CAAC,UAAY,QAAQ,EAAE,KAAK,MAAM;YAE9E,IAAI,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;gBACtC,MAAM,sBAAsB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,UAAU;gBAClE,oBAAoB;gBAEpB,yDAAyD;gBACzD,MAAM,iBAAiB,oBAAoB,GAAG,CAAC,CAAA,UAAW,QAAQ,EAAE;gBACpE,iBAAiB;YACnB;QACF;IACF;IAIA,qDAAqD;IACrD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACvC,IAAI,mBAAmB,MAAM,KAAK,GAAG;gBACnC,oBAAoB,EAAE;gBACtB;YACF;YAEA,aAAa;YACb,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,sBAAmB,AAAD,EAAE;gBAEzC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,2CAA2C;oBAC3C,MAAM,kBAAkB,mBACrB,GAAG;6FAAC,CAAA,KAAM,OAAO,IAAI,EAAE;qGAAK,CAAA,UAAW,QAAQ,EAAE,KAAK;;4FACtD,MAAM,CAAC;oBAEV,oBAAoB;gBACtB,OAAO;oBACL,QAAQ,KAAK,CAAC,oCAAoC,OAAO,KAAK;oBAC9D,oBAAoB,EAAE;gBACxB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,oBAAoB,EAAE;YACxB,SAAU;gBACR,aAAa;YACf;QACF;4DAAG;QAAC;KAAmB;IAEvB,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,mBAAmB,MAAM,GAAG,GAAG;gBACjC;YACF;QACF;oCAAG;QAAC;QAAoB;KAAqB;IAE7C,sCAAsC;IACtC,MAAM,iBAAiB,OAAO;QAC5B,aAAa;QACb,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,yBAAsB,AAAD,EAAE;YAE5C,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,YAAY,OAAO,IAAI;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B,OAAO,KAAK;gBACvD,YAAY,EAAE;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,YAAY,EAAE;QAChB,SAAU;YACR,aAAa;YACb,eAAe;QACjB;IACF;IAEA,6CAA6C;IAC7C,MAAM,qBAAqB,CAAC;QAC1B,eAAe;QAEf,uBAAuB;QACvB,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;QACvC;QAEA,wCAAwC;QACxC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,YAAY,EAAE;YACd,eAAe;YACf;QACF;QAEA,qCAAqC;QACrC,iBAAiB,OAAO,GAAG,WAAW;YACpC,eAAe;QACjB,GAAG,MAAM,uBAAuB;IAClC;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;6CAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;oCAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,mBAAmB,QAAQ,CAAC,QAAQ,EAAE;QACzD,IAAI;QAEJ,IAAI,YAAY;YACd,iBAAiB,mBAAmB,MAAM,CAAC,CAAA,KAAM,OAAO,QAAQ,EAAE;YAClE,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE;QAClE,OAAO;YACL,oCAAoC;YACpC,IAAI,mBAAmB,MAAM,IAAI,GAAG;gBAClC,QAAQ,8CAA8C;YACxD;YACA,iBAAiB;mBAAI;gBAAoB,QAAQ,EAAE;aAAC;YACpD,oBAAoB,CAAA,OAAQ;uBAAI;oBAAM;iBAAQ;QAChD;QAEA,iBAAiB;IACnB;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACrB,MAAM,iBAAiB,mBAAmB,MAAM,CAAC,CAAA,KAAM,OAAO;QAC9D,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,iBAAiB;IACnB;IAEA,uBAAuB;IACvB,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,MAAM,OAAO;QAC3B,OAAO,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,UAAU;IAC5C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,+HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc,CAAC;oBAClC,QAAQ;oBACR,IAAI,CAAC,SAAS;wBACZ,4BAA4B;wBAC5B,eAAe;wBACf,YAAY,EAAE;wBACd,eAAe;oBACjB;gBACF;;kCACE,6LAAC,+HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;4BACf,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAIpD,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,6LAAC,+HAAA,CAAA,iBAAc;wBACb,WAAU;wBACV,OAAM;wBACN,YAAY;wBACZ,OAAO;4BAAE,OAAO;wBAAqC;kCAErD,cAAA,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,eAAY;oCACX,aAAY;oCACZ,OAAO;oCACP,eAAe;oCACf,WAAU;;;;;;8CAEZ,6LAAC,+HAAA,CAAA,cAAW;oCAAC,WAAU;;wCAEpB,2BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAAgC;;;;;;;;;;;;;;;;;wCAMrD,CAAC,aAAa,SAAS,MAAM,KAAK,mBACjC,6LAAC,+HAAA,CAAA,eAAY;sDACX,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEACb,YAAY,MAAM,GAAG,IAClB,yCACA,cACA,sBACA;;;;;;oDAEL,YAAY,MAAM,IAAI,KAAK,6BAC1B,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;;;;;;wCAS5D,CAAC,aAAa,SAAS,MAAM,GAAG,mBAC/B,6LAAC,+HAAA,CAAA,eAAY;sDACV,SAAS,GAAG,CAAC,CAAC,wBACf,6LAAC,+HAAA,CAAA,cAAW;oDAEV,OAAO,QAAQ,IAAI,IAAI,QAAQ,EAAE;oDACjC,UAAU;wDACR,0EAA0E;wDAC1E,IAAI,mBAAmB,MAAM,GAAG,KAAK,mBAAmB,QAAQ,CAAC,QAAQ,EAAE,GAAG;4DAC5E,cAAc;4DACd,QAAQ;4DACR,eAAe;4DACf,YAAY,EAAE;4DACd,eAAe;wDACjB;oDACF;oDACA,UAAU,mBAAmB,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,CAAC,QAAQ,EAAE;oDACnF,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8CACA,mBAAmB,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,CAAC,QAAQ,EAAE,IACrE,kCACA;;sEAIN,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,SAAS,iBAChB,6LAAC,gIAAA,CAAA,UAAK;gEACJ,KAAK,QAAQ,SAAS;gEACtB,KAAK,QAAQ,IAAI;gEACjB,IAAI;gEACJ,WAAU;gEACV,OAAM;;;;;qFAGR,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAMzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC,QAAQ,IAAI;;;;;;8EAChE,6LAAC;oEAAI,WAAU;8EACZ,QAAQ,gBAAgB,iBACvB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FACb,YAAY,QAAQ,gBAAgB;;;;;;0FAEvC,6LAAC;gFAAK,WAAU;0FACb,YAAY,QAAQ,UAAU;;;;;;;;;;;6FAInC,6LAAC;wEAAK,WAAU;kFACb,YAAY,QAAQ,UAAU;;;;;;;;;;;;;;;;;sEAOvC,6LAAC,uMAAA,CAAA,QAAK;4DACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mBACA,mBAAmB,QAAQ,CAAC,QAAQ,EAAE,IAClC,6BACA;;;;;;;mDAhEH,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA6E7B,6LAAC;gBAAI,WAAU;;oBAEZ,aAAa,mBAAmB,MAAM,GAAG,KAAK,iBAAiB,MAAM,KAAK,mBACzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;oBAOvD,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;0CAI9D,6LAAC,8JAAA,CAAA,aAAU;gCACT,SAAS;gCACT,oBAAoB,8JAAA,CAAA,gBAAa;gCACjC,WAAW;0CAEX,cAAA,6LAAC,sKAAA,CAAA,kBAAe;oCACd,OAAO,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;oCACrC,UAAU,sKAAA,CAAA,8BAA2B;8CAErC,cAAA,6LAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;gDAEC,SAAS;gDACT,UAAU;gDACV,aAAa;+CAHR,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAa7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,mBAAmB,MAAM,KAAK,IAC3B,yBACA,GAAG,mBAAmB,MAAM,CAAC,QAAQ,EAAE,mBAAmB,MAAM,KAAK,IAAI,MAAM,GAAG,SAAS,CAAC;;;;;;0CAElG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAChB,eACA,mBAAmB,MAAM,IAAI,IAAI,qBAAqB;;4CAErD,mBAAmB,MAAM;4CAAC;;;;;;;kDAE7B,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;oBAK3C,mBAAmB,MAAM,IAAI,mBAC5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;IAlZwB;;QAeN,8JAAA,CAAA,aAAU;;;MAfJ", "debugId": null}}, {"offset": {"line": 2976, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/editors/InlinePostAndProductEditor.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Loader2, Check, X, Package } from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\nimport { updatePost } from \"@/lib/actions/posts\";\r\nimport ProductSelector from \"@/components/feed/shared/forms/ProductSelector\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\nexport interface InlinePostAndProductEditorProps {\r\n  postId: string;\r\n  initialContent: string;\r\n  initialProductIds: string[];\r\n  initialImageUrl?: string | null;\r\n  onSave?: (_newContent: string, _newProductIds: string[]) => void;\r\n  onCancel?: () => void;\r\n  className?: string;\r\n}\r\n\r\nexport default function InlinePostAndProductEditor({\r\n  postId,\r\n  initialContent,\r\n  initialProductIds,\r\n  initialImageUrl,\r\n  onSave,\r\n  onCancel,\r\n  className = \"\",\r\n}: InlinePostAndProductEditorProps) {\r\n  const [content, setContent] = useState(initialContent);\r\n  const [productIds, setProductIds] = useState<string[]>(initialProductIds);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [charCount, setCharCount] = useState(initialContent.length);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  const maxChars = 2000;\r\n  const isOverLimit = charCount > maxChars;\r\n  const hasContentChanges = content.trim() !== initialContent.trim();\r\n  const hasProductChanges = JSON.stringify(productIds.sort()) !== JSON.stringify(initialProductIds.sort());\r\n  const hasChanges = hasContentChanges || hasProductChanges;\r\n\r\n  // Focus textarea when component mounts\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.focus();\r\n      // Set cursor to end of text\r\n      textareaRef.current.setSelectionRange(content.length, content.length);\r\n    }\r\n  }, [content.length]);\r\n\r\n  // Handle content change\r\n  const handleContentChange = (value: string) => {\r\n    setContent(value);\r\n    setCharCount(value.length);\r\n  };\r\n\r\n  // Handle save\r\n  const handleSave = async () => {\r\n    if (!hasChanges) {\r\n      onCancel?.();\r\n      return;\r\n    }\r\n\r\n    if (isOverLimit) {\r\n      toast.error(\"Content too long\", {\r\n        description: `Please reduce content to ${maxChars} characters or less.`\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (content.trim().length === 0 && productIds.length === 0) {\r\n      toast.error(\"Content or products required\", {\r\n        description: \"Post must have either content or linked products.\"\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const result = await updatePost(postId, {\r\n        content: content.trim(),\r\n        product_ids: productIds,\r\n        image_url: initialImageUrl,\r\n        mentioned_business_ids: []\r\n      });\r\n\r\n      if (result.success) {\r\n        toast.success(\"Post updated successfully\");\r\n        onSave?.(content.trim(), productIds);\r\n      } else {\r\n        toast.error(\"Failed to update post\", {\r\n          description: result.error || \"Please try again.\"\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating post:\", error);\r\n      toast.error(\"An unexpected error occurred\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle cancel\r\n  const handleCancel = () => {\r\n    setContent(initialContent);\r\n    setCharCount(initialContent.length);\r\n    setProductIds(initialProductIds);\r\n    onCancel?.();\r\n  };\r\n\r\n  // Handle keyboard shortcuts\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Escape') {\r\n      handleCancel();\r\n    } else if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {\r\n      e.preventDefault();\r\n      handleSave();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-4 ${className}`}>\r\n      {/* Content Editor */}\r\n      <div className=\"space-y-3\">\r\n        <div className=\"relative\">\r\n          <Textarea\r\n            ref={textareaRef}\r\n            value={content}\r\n            onChange={(e) => handleContentChange(e.target.value)}\r\n            onKeyDown={handleKeyDown}\r\n            placeholder=\"What's on your mind?\"\r\n            className={`min-h-[100px] resize-none ${\r\n              isOverLimit ? \"border-destructive focus:border-destructive\" : \"\"\r\n            }`}\r\n            disabled={isLoading}\r\n          />\r\n\r\n          {/* Character count */}\r\n          <div className={`absolute bottom-2 right-2 text-xs ${\r\n            isOverLimit ? \"text-destructive\" : \"text-muted-foreground\"\r\n          }`}>\r\n            {charCount}/{maxChars}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Product Selector */}\r\n      <div className=\"space-y-2\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Package className=\"h-4 w-4 text-muted-foreground\" />\r\n          <span className=\"text-sm font-medium text-muted-foreground\">\r\n            Linked Products ({productIds.length})\r\n          </span>\r\n        </div>\r\n        <ProductSelector\r\n          selectedProductIds={productIds}\r\n          onProductsChange={setProductIds}\r\n        />\r\n      </div>\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex items-center justify-end gap-2 pt-2\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={handleCancel}\r\n          disabled={isLoading}\r\n          className=\"text-muted-foreground hover:text-foreground\"\r\n        >\r\n          <X className=\"h-4 w-4 mr-1\" />\r\n          Cancel\r\n        </Button>\r\n\r\n        <motion.div\r\n          whileHover={{ scale: hasChanges && !isLoading ? 1.02 : 1 }}\r\n          whileTap={{ scale: hasChanges && !isLoading ? 0.98 : 1 }}\r\n        >\r\n          <Button\r\n            size=\"sm\"\r\n            onClick={handleSave}\r\n            disabled={!hasChanges || isOverLimit || isLoading}\r\n            className=\"relative overflow-hidden\"\r\n            style={{\r\n              background: hasChanges && !isLoading\r\n                ? 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\r\n                : undefined,\r\n              boxShadow: hasChanges && !isLoading\r\n                ? '0 4px 20px rgba(59, 130, 246, 0.3)'\r\n                : '0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)'\r\n            }}\r\n          >\r\n            <AnimatePresence mode=\"wait\">\r\n              {isLoading ? (\r\n                <motion.div\r\n                  key=\"saving\"\r\n                  initial={{ opacity: 0, x: -10 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  exit={{ opacity: 0, x: 10 }}\r\n                  className=\"flex items-center justify-center\"\r\n                >\r\n                  <Loader2 className=\"h-4 w-4 mr-1 animate-spin\" />\r\n                  Saving...\r\n                </motion.div>\r\n              ) : (\r\n                <motion.div\r\n                  key=\"save\"\r\n                  initial={{ opacity: 0, x: -10 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  exit={{ opacity: 0, x: 10 }}\r\n                  className=\"flex items-center justify-center\"\r\n                >\r\n                  <Check className=\"h-4 w-4 mr-1\" />\r\n                  Save\r\n                </motion.div>\r\n              )}\r\n            </AnimatePresence>\r\n          </Button>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;;;AATA;;;;;;;;;AAqBe,SAAS,2BAA2B,EACjD,MAAM,EACN,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,MAAM,EACN,QAAQ,EACR,YAAY,EAAE,EACkB;;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,MAAM;IAChE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,MAAM,WAAW;IACjB,MAAM,cAAc,YAAY;IAChC,MAAM,oBAAoB,QAAQ,IAAI,OAAO,eAAe,IAAI;IAChE,MAAM,oBAAoB,KAAK,SAAS,CAAC,WAAW,IAAI,QAAQ,KAAK,SAAS,CAAC,kBAAkB,IAAI;IACrG,MAAM,aAAa,qBAAqB;IAExC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK;gBACzB,4BAA4B;gBAC5B,YAAY,OAAO,CAAC,iBAAiB,CAAC,QAAQ,MAAM,EAAE,QAAQ,MAAM;YACtE;QACF;+CAAG;QAAC,QAAQ,MAAM;KAAC;IAEnB,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,aAAa,MAAM,MAAM;IAC3B;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,IAAI,CAAC,YAAY;YACf;YACA;QACF;QAEA,IAAI,aAAa;YACf,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,oBAAoB;gBAC9B,aAAa,CAAC,yBAAyB,EAAE,SAAS,oBAAoB,CAAC;YACzE;YACA;QACF;QAEA,IAAI,QAAQ,IAAI,GAAG,MAAM,KAAK,KAAK,WAAW,MAAM,KAAK,GAAG;YAC1D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,aAAa;YACf;YACA;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;gBACtC,SAAS,QAAQ,IAAI;gBACrB,aAAa;gBACb,WAAW;gBACX,wBAAwB,EAAE;YAC5B;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,SAAS,QAAQ,IAAI,IAAI;YAC3B,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aAAa,OAAO,KAAK,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,WAAW;QACX,aAAa,eAAe,MAAM;QAClC,cAAc;QACd;IACF;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,GAAG;YACxD,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,WAAQ;4BACP,KAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BACnD,WAAW;4BACX,aAAY;4BACZ,WAAW,CAAC,0BAA0B,EACpC,cAAc,gDAAgD,IAC9D;4BACF,UAAU;;;;;;sCAIZ,6LAAC;4BAAI,WAAW,CAAC,kCAAkC,EACjD,cAAc,qBAAqB,yBACnC;;gCACC;gCAAU;gCAAE;;;;;;;;;;;;;;;;;;0BAMnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAK,WAAU;;oCAA4C;oCACxC,WAAW,MAAM;oCAAC;;;;;;;;;;;;;kCAGxC,6LAAC,4JAAA,CAAA,UAAe;wBACd,oBAAoB;wBACpB,kBAAkB;;;;;;;;;;;;0BAKtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO,cAAc,CAAC,YAAY,OAAO;wBAAE;wBACzD,UAAU;4BAAE,OAAO,cAAc,CAAC,YAAY,OAAO;wBAAE;kCAEvD,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS;4BACT,UAAU,CAAC,cAAc,eAAe;4BACxC,WAAU;4BACV,OAAO;gCACL,YAAY,cAAc,CAAC,YACvB,sDACA;gCACJ,WAAW,cAAc,CAAC,YACtB,uCACA;4BACN;sCAEA,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACnB,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC1B,WAAU;;sDAEV,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;mCAN7C;;;;yDAUN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC1B,WAAU;;sDAEV,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;mCAN9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBtB;GA1MwB;KAAA", "debugId": null}}, {"offset": {"line": 3319, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/crud.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { PostFormData } from '@/lib/types/posts';\r\nimport { ActionResponse } from '@/lib/types/api';\r\nimport { deletePostMedia } from '@/lib/actions/shared/upload-post-media';\r\n\r\n/**\r\n * Create a new post\r\n */\r\nexport async function createPost(formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to create a post'\r\n    };\r\n  }\r\n\r\n  // Get the user's business profile\r\n  const { data: businessProfile, error: profileError } = await supabase\r\n    .from('business_profiles')\r\n    .select('id, city_slug, state_slug, locality_slug, pincode, logo_url')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError || !businessProfile) {\r\n    return {\r\n      success: false,\r\n      message: 'Business profile not found',\r\n      error: 'You must have a business profile to create a post'\r\n    };\r\n  }\r\n\r\n  // Prepare post data\r\n  const postData = {\r\n    business_id: user.id,\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    city_slug: businessProfile.city_slug,\r\n    state_slug: businessProfile.state_slug,\r\n    locality_slug: businessProfile.locality_slug,\r\n    pincode: businessProfile.pincode,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    author_avatar: businessProfile.logo_url\r\n  };\r\n\r\n  // Insert the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .insert(postData)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error creating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to create post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post created successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the content of an existing post (for inline editing)\r\n */\r\nexport async function updatePostContent(postId: string, content: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the content and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      content: content.trim(),\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post content:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the product_ids of an existing post (for inline editing)\r\n */\r\nexport async function updatePostProducts(postId: string, productIds: string[]): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the product_ids and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      product_ids: productIds,\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post products:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post products',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post products updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update an existing post (full update for form submissions)\r\n */\r\nexport async function updatePost(postId: string, formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Prepare update data\r\n  const updateData = {\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    updated_at: new Date().toISOString()\r\n  };\r\n\r\n  // Update the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update(updateData)\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Delete a post\r\n */\r\nexport async function deletePost(postId: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to delete a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user, get creation date for media deletion\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id, created_at, image_url')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to delete it'\r\n    };\r\n  }\r\n\r\n  // Always attempt to delete the post folder from storage\r\n  // This ensures we clean up any files that might exist, regardless of image_url status\r\n  try {\r\n    const mediaDeleteResult = await deletePostMedia(user.id, postId, existingPost.created_at);\r\n    if (!mediaDeleteResult.success && mediaDeleteResult.error) {\r\n      console.error('Error deleting post media:', mediaDeleteResult.error);\r\n      // Continue with post deletion even if media deletion fails\r\n    }\r\n  } catch (mediaError) {\r\n    console.error('Error deleting post media:', mediaError);\r\n    // Continue with post deletion even if media deletion fails\r\n  }\r\n\r\n  // Delete the post\r\n  const { error } = await supabase\r\n    .from('business_posts')\r\n    .delete()\r\n    .eq('id', postId);\r\n\r\n  if (error) {\r\n    console.error('Error deleting post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to delete post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post deleted successfully'\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA6RsB,aAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3335, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/dialogs/PostDeleteDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogDescription,\r\n  <PERSON><PERSON><PERSON><PERSON>er,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Loader2, Trash2 } from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\nimport { deletePost } from \"@/lib/actions/posts\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\nexport interface PostDeleteDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (_open: boolean) => void;\r\n  postId: string;\r\n  postContent: string;\r\n  onDeleteSuccess?: () => void;\r\n}\r\n\r\nexport default function PostDeleteDialog({\r\n  isOpen,\r\n  onOpenChange,\r\n  postId,\r\n  postContent: _postContent,\r\n  onDeleteSuccess,\r\n}: PostDeleteDialogProps) {\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n\r\n  const handleDelete = async () => {\r\n    setIsDeleting(true);\r\n\r\n    try {\r\n      const result = await deletePost(postId);\r\n\r\n      if (result.success) {\r\n        toast.success(\"Post deleted successfully\", {\r\n          description: \"Your post and associated media have been removed.\"\r\n        });\r\n        onDeleteSuccess?.();\r\n        onOpenChange(false);\r\n      } else {\r\n        toast.error(\"Failed to delete post\", {\r\n          description: result.error || \"Please try again.\"\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting post:\", error);\r\n      toast.error(\"An unexpected error occurred\", {\r\n        description: \"Please try again later.\"\r\n      });\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader className=\"text-center pb-2\">\r\n          <div className=\"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20\">\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.1, type: \"spring\", stiffness: 200 }}\r\n            >\r\n              <Trash2 className=\"h-8 w-8 text-red-500\" />\r\n            </motion.div>\r\n          </div>\r\n          <DialogTitle className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n            Delete this post?\r\n          </DialogTitle>\r\n          <DialogDescription className=\"text-gray-500 dark:text-gray-400 mt-2\">\r\n            This action cannot be undone.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <DialogFooter className=\"flex flex-col-reverse sm:flex-row gap-3 pt-4\">\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            onClick={() => onOpenChange(false)}\r\n            disabled={isDeleting}\r\n            className=\"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n\r\n          <motion.div\r\n            whileHover={{ scale: 1.02 }}\r\n            whileTap={{ scale: 0.98 }}\r\n            className=\"flex-1\"\r\n          >\r\n            <Button\r\n              type=\"button\"\r\n              onClick={handleDelete}\r\n              disabled={isDeleting}\r\n              className={`\r\n                w-full relative overflow-hidden\r\n                bg-gradient-to-r from-red-500 to-red-600\r\n                hover:from-red-600 hover:to-red-700\r\n                text-white font-medium\r\n                shadow-lg hover:shadow-xl\r\n                transition-all duration-300\r\n                before:absolute before:inset-0\r\n                before:bg-gradient-to-r before:from-red-400 before:to-red-500\r\n                before:opacity-0 hover:before:opacity-20\r\n                before:transition-opacity before:duration-300\r\n                ${isDeleting ? 'cursor-not-allowed opacity-80' : ''}\r\n              `}\r\n              style={{\r\n                boxShadow: isDeleting\r\n                  ? '0 4px 20px rgba(239, 68, 68, 0.3)'\r\n                  : '0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)'\r\n              }}\r\n            >\r\n              <AnimatePresence mode=\"wait\">\r\n                {isDeleting ? (\r\n                  <motion.div\r\n                    key=\"deleting\"\r\n                    initial={{ opacity: 0, x: -10 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: 10 }}\r\n                    className=\"flex items-center justify-center\"\r\n                  >\r\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                    Deleting...\r\n                  </motion.div>\r\n                ) : (\r\n                  <motion.div\r\n                    key=\"delete\"\r\n                    initial={{ opacity: 0, x: -10 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: 10 }}\r\n                    className=\"flex items-center justify-center\"\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                    Delete Post\r\n                  </motion.div>\r\n                )}\r\n              </AnimatePresence>\r\n            </Button>\r\n          </motion.div>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n\r\n// Trigger button component for easier usage\r\nexport interface PostDeleteButtonProps {\r\n  postId: string;\r\n  postContent: string;\r\n  onDeleteSuccess?: () => void;\r\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\";\r\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\";\r\n  className?: string;\r\n}\r\n\r\nexport function PostDeleteButton({\r\n  postId,\r\n  postContent,\r\n  onDeleteSuccess,\r\n  variant = \"ghost\",\r\n  size = \"sm\",\r\n  className = \"\",\r\n}: PostDeleteButtonProps) {\r\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\r\n\r\n  return (\r\n    <>\r\n      <Button\r\n        variant={variant}\r\n        size={size}\r\n        onClick={() => setIsDialogOpen(true)}\r\n        className={className}\r\n      >\r\n        <Trash2 className=\"h-4 w-4\" />\r\n      </Button>\r\n\r\n      <PostDeleteDialog\r\n        isOpen={isDialogOpen}\r\n        onOpenChange={setIsDialogOpen}\r\n        postId={postId}\r\n        postContent={postContent}\r\n        onDeleteSuccess={onDeleteSuccess}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAQA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;;;AAfA;;;;;;;;AAyBe,SAAS,iBAAiB,EACvC,MAAM,EACN,YAAY,EACZ,MAAM,EACN,aAAa,YAAY,EACzB,eAAe,EACO;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,cAAc;QAEd,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE;YAEhC,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,6BAA6B;oBACzC,aAAa;gBACf;gBACA;gBACA,aAAa;YACf,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aAAa,OAAO,KAAK,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,aAAa;YACf;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO;oCAAK,MAAM;oCAAU,WAAW;gCAAI;0CAEzD,cAAA,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGtB,6LAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;sCAAyD;;;;;;sCAGhF,6LAAC,8HAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAwC;;;;;;;;;;;;8BAKvE,6LAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;4BACV,WAAU;sCACX;;;;;;sCAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC;;;;;;;;;;;gBAWV,EAAE,aAAa,kCAAkC,GAAG;cACtD,CAAC;gCACD,OAAO;oCACL,WAAW,aACP,sCACA;gCACN;0CAEA,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oCAAC,MAAK;8CACnB,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,WAAU;;0DAEV,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uCAN7C;;;;6DAUN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;uCAN/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBxB;GA/HwB;KAAA;AA2IjB,SAAS,iBAAiB,EAC/B,MAAM,EACN,WAAW,EACX,eAAe,EACf,UAAU,OAAO,EACjB,OAAO,IAAI,EACX,YAAY,EAAE,EACQ;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE;;0BACE,6LAAC,8HAAA,CAAA,SAAM;gBACL,SAAS;gBACT,MAAM;gBACN,SAAS,IAAM,gBAAgB;gBAC/B,WAAW;0BAEX,cAAA,6LAAC,6MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAGpB,6LAAC;gBACC,QAAQ;gBACR,cAAc;gBACd,QAAQ;gBACR,aAAa;gBACb,iBAAiB;;;;;;;;AAIzB;IA9BgB;MAAA", "debugId": null}}, {"offset": {"line": 3636, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/postUrl.ts"], "sourcesContent": ["/**\r\n * Utility functions for post URL generation and handling\r\n */\r\n\r\n/**\r\n * Generate a complete URL for a single post page\r\n * @param postId - The ID of the post\r\n * @returns Complete URL for the post\r\n */\r\nexport function generatePostUrl(postId: string): string {\r\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in';\r\n  return `${baseUrl}/post/${postId}`;\r\n}\r\n\r\n/**\r\n * Generate a relative path for a single post page\r\n * @param postId - The ID of the post\r\n * @returns Relative path for the post\r\n */\r\nexport function generatePostPath(postId: string): string {\r\n  return `/post/${postId}`;\r\n}\r\n\r\n/**\r\n * Extract post ID from a post URL\r\n * @param url - The post URL\r\n * @returns Post ID if valid, null otherwise\r\n */\r\nexport function extractPostIdFromUrl(url: string): string | null {\r\n  try {\r\n    const urlObj = new URL(url);\r\n    const pathParts = urlObj.pathname.split('/');\r\n    \r\n    // Expected format: /post/[postId]\r\n    if (pathParts.length >= 3 && pathParts[1] === 'post') {\r\n      const postId = pathParts[2];\r\n      return postId && postId.trim() !== '' ? postId : null;\r\n    }\r\n    \r\n    return null;\r\n  } catch (error) {\r\n    console.error('Error extracting post ID from URL:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Validate if a string is a valid post ID format\r\n * @param postId - The post ID to validate\r\n * @returns True if valid, false otherwise\r\n */\r\nexport function isValidPostId(postId: string): boolean {\r\n  if (!postId || typeof postId !== 'string') {\r\n    return false;\r\n  }\r\n  \r\n  // Basic validation - should be a non-empty string\r\n  // You can add more specific validation based on your post ID format\r\n  return postId.trim().length > 0;\r\n}\r\n\r\n/**\r\n * Generate sharing text for social media\r\n * @param postId - The ID of the post\r\n * @param authorName - Optional author name\r\n * @returns Formatted sharing text\r\n */\r\nexport function generateSharingText(postId: string, authorName?: string): string {\r\n  const postUrl = generatePostUrl(postId);\r\n  \r\n  if (authorName) {\r\n    return `Check out this post by ${authorName} on Dukancard: ${postUrl}`;\r\n  }\r\n  \r\n  return `Check out this post on Dukancard: ${postUrl}`;\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;CAIC;;;;;;;AAEiB;AADX,SAAS,gBAAgB,MAAc;IAC5C,MAAM,UAAU,6DAAoC;IACpD,OAAO,GAAG,QAAQ,MAAM,EAAE,QAAQ;AACpC;AAOO,SAAS,iBAAiB,MAAc;IAC7C,OAAO,CAAC,MAAM,EAAE,QAAQ;AAC1B;AAOO,SAAS,qBAAqB,GAAW;IAC9C,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC;QAExC,kCAAkC;QAClC,IAAI,UAAU,MAAM,IAAI,KAAK,SAAS,CAAC,EAAE,KAAK,QAAQ;YACpD,MAAM,SAAS,SAAS,CAAC,EAAE;YAC3B,OAAO,UAAU,OAAO,IAAI,OAAO,KAAK,SAAS;QACnD;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAOO,SAAS,cAAc,MAAc;IAC1C,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,OAAO;IACT;IAEA,kDAAkD;IAClD,oEAAoE;IACpE,OAAO,OAAO,IAAI,GAAG,MAAM,GAAG;AAChC;AAQO,SAAS,oBAAoB,MAAc,EAAE,UAAmB;IACrE,MAAM,UAAU,gBAAgB;IAEhC,IAAI,YAAY;QACd,OAAO,CAAC,uBAAuB,EAAE,WAAW,eAAe,EAAE,SAAS;IACxE;IAEA,OAAO,CAAC,kCAAkC,EAAE,SAAS;AACvD", "debugId": null}}, {"offset": {"line": 3696, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/ModernPostCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { motion } from 'framer-motion';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { PostWithBusinessProfile, ProductData } from '@/lib/types/posts';\r\nimport PostActions from './PostActions';\r\nimport ProductListItem from '@/app/components/ProductListItem';\r\nimport { fetchProductsByIds } from '@/lib/actions/products/fetchProductsByIds';\r\nimport { Loader2 } from 'lucide-react';\r\nimport { MapPin, MoreVertical, Edit3, Trash2, Package, Share2, User } from 'lucide-react';\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { cn } from '@/lib/utils';\r\nimport { fetchPostAddress, formatAddressString, formatAddressFromSlugs, PostAddress } from '@/lib/utils/addressUtils';\r\nimport { usePostOwnership } from '@/components/feed/shared/hooks/usePostOwnership';\r\nimport InlinePostAndProductEditor from '@/components/feed/shared/editors/InlinePostAndProductEditor';\r\nimport PostDeleteDialog from '@/components/feed/shared/dialogs/PostDeleteDialog';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Button } from '@/components/ui/button';\r\nimport { generatePostPath, generatePostUrl } from '@/lib/utils/postUrl';\r\nimport { toast } from 'sonner';\r\n\r\ninterface ModernPostCardProps {\r\n  post: PostWithBusinessProfile;\r\n  index?: number;\r\n  onPostUpdate?: (_postId: string, _newContent: string) => void;\r\n  onPostDelete?: (_postId: string) => void;\r\n  onProductsUpdate?: (_postId: string, _newProductIds: string[]) => void;\r\n  showActualAspectRatio?: boolean; // For single post pages\r\n  disablePostClick?: boolean; // Disable post clicking for single post pages\r\n  enableImageFullscreen?: boolean; // Enable full-screen image viewing\r\n}\r\n\r\nexport default function ModernPostCard({\r\n  post,\r\n  index = 0,\r\n  onPostUpdate,\r\n  onPostDelete,\r\n  onProductsUpdate,\r\n  showActualAspectRatio = false,\r\n  disablePostClick = false,\r\n  enableImageFullscreen = false\r\n}: ModernPostCardProps) {\r\n  const [imageLoading, setImageLoading] = useState(true);\r\n  const [imageError, setImageError] = useState(false);\r\n  const [address, setAddress] = useState<PostAddress | null>(null);\r\n  const [addressLoading, setAddressLoading] = useState(true);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const [currentContent, setCurrentContent] = useState(post.content);\r\n  const [currentProductIds, setCurrentProductIds] = useState<string[]>(post.product_ids || []);\r\n  const [linkedProducts, setLinkedProducts] = useState<ProductData[]>([]);\r\n  const [isLoadingProducts, setIsLoadingProducts] = useState(false);\r\n  const [showFullscreenImage, setShowFullscreenImage] = useState(false);\r\n  const productsScrollRef = useRef<HTMLDivElement>(null);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [startX, setStartX] = useState(0);\r\n  const [scrollLeft, setScrollLeft] = useState(0);\r\n  const [dragDistance, setDragDistance] = useState(0);\r\n\r\n  // Check if current user owns this post\r\n  const { isOwner, isLoading: ownershipLoading } = usePostOwnership({\r\n    postBusinessId: post.business_id\r\n  });\r\n\r\n  // Get business profile data\r\n  const business = post.business_profiles;\r\n\r\n  // Handle edit save (both content and products)\r\n  const handleEditSave = (newContent: string, newProductIds: string[]) => {\r\n    setIsEditing(false);\r\n    setCurrentContent(newContent);\r\n    setCurrentProductIds(newProductIds);\r\n    onPostUpdate?.(post.id, newContent);\r\n    onProductsUpdate?.(post.id, newProductIds);\r\n  };\r\n\r\n  // Handle edit cancel\r\n  const handleEditCancel = () => {\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle delete success\r\n  const handleDeleteSuccess = () => {\r\n    setShowDeleteDialog(false);\r\n    onPostDelete?.(post.id);\r\n  };\r\n\r\n  const handleShare = async () => {\r\n    try {\r\n      const postUrl = generatePostUrl(post.id);\r\n\r\n      // Try to use native Web Share API if available\r\n      if (navigator.share) {\r\n        await navigator.share({\r\n          title: 'Check out this post on Dukancard',\r\n          url: postUrl,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // Fallback to clipboard API\r\n      await navigator.clipboard.writeText(postUrl);\r\n      toast.success('Post link copied to clipboard!');\r\n\r\n    } catch (error) {\r\n      console.error('Error sharing post:', error);\r\n      toast.error('Failed to share post');\r\n    }\r\n  };\r\n\r\n  // Fetch linked products\r\n  useEffect(() => {\r\n    if (currentProductIds.length === 0) {\r\n      setLinkedProducts([]);\r\n      setIsLoadingProducts(false);\r\n      return;\r\n    }\r\n\r\n    const fetchProducts = async () => {\r\n      setIsLoadingProducts(true);\r\n      try {\r\n        const result = await fetchProductsByIds(currentProductIds);\r\n\r\n        if (!result.success) {\r\n          console.error('Error fetching products:', result.error);\r\n          setLinkedProducts([]);\r\n          return;\r\n        }\r\n\r\n        // Maintain the order of products as specified in currentProductIds array\r\n        const orderedProducts = currentProductIds\r\n          .map(id => result.data?.find(product => product.id === id))\r\n          .filter(Boolean) as ProductData[];\r\n\r\n        setLinkedProducts(orderedProducts);\r\n      } catch (err) {\r\n        console.error('Error fetching products:', err);\r\n        setLinkedProducts([]);\r\n      } finally {\r\n        setIsLoadingProducts(false);\r\n      }\r\n    };\r\n\r\n    fetchProducts();\r\n  }, [currentProductIds]);\r\n\r\n  // Add drag-to-scroll functionality for products container\r\n  useEffect(() => {\r\n    const container = productsScrollRef.current;\r\n    if (!container) return;\r\n\r\n    const handleMouseDown = (e: MouseEvent) => {\r\n      if (container.scrollWidth <= container.clientWidth) return; // No overflow, no need to scroll\r\n\r\n      setIsDragging(true);\r\n      setStartX(e.pageX - container.offsetLeft);\r\n      setScrollLeft(container.scrollLeft);\r\n      setDragDistance(0);\r\n      container.style.cursor = 'grabbing';\r\n      e.preventDefault();\r\n    };\r\n\r\n    const handleMouseMove = (e: MouseEvent) => {\r\n      if (!isDragging || container.scrollWidth <= container.clientWidth) return;\r\n\r\n      e.preventDefault();\r\n      const x = e.pageX - container.offsetLeft;\r\n      const walk = (x - startX) * 2; // Scroll speed multiplier\r\n      const newDragDistance = Math.abs(x - startX);\r\n      setDragDistance(newDragDistance);\r\n      container.scrollLeft = scrollLeft - walk;\r\n    };\r\n\r\n    const handleMouseUp = () => {\r\n      setIsDragging(false);\r\n      container.style.cursor = container.scrollWidth > container.clientWidth ? 'grab' : 'default';\r\n    };\r\n\r\n    const handleMouseLeave = () => {\r\n      setIsDragging(false);\r\n      container.style.cursor = container.scrollWidth > container.clientWidth ? 'grab' : 'default';\r\n    };\r\n\r\n    // Set initial cursor\r\n    container.style.cursor = container.scrollWidth > container.clientWidth ? 'grab' : 'default';\r\n\r\n    container.addEventListener('mousedown', handleMouseDown);\r\n    document.addEventListener('mousemove', handleMouseMove);\r\n    document.addEventListener('mouseup', handleMouseUp);\r\n    container.addEventListener('mouseleave', handleMouseLeave);\r\n\r\n    return () => {\r\n      container.removeEventListener('mousedown', handleMouseDown);\r\n      document.removeEventListener('mousemove', handleMouseMove);\r\n      document.removeEventListener('mouseup', handleMouseUp);\r\n      container.removeEventListener('mouseleave', handleMouseLeave);\r\n    };\r\n  }, [linkedProducts, isDragging, startX, scrollLeft, dragDistance]);\r\n\r\n  // Handle product click - only navigate if it wasn't a drag\r\n  const handleProductClick = (e: React.MouseEvent, productUrl: string) => {\r\n    if (dragDistance > 5) { // If dragged more than 5px, don't navigate\r\n      e.preventDefault();\r\n      return;\r\n    }\r\n    // Allow normal navigation for clicks\r\n    window.open(productUrl, '_blank', 'noopener,noreferrer');\r\n  };\r\n\r\n  // Fetch real address data - moved before conditional return\r\n  useEffect(() => {\r\n    if (!business) return; // Early return if no business data\r\n\r\n    const loadAddress = async () => {\r\n      setAddressLoading(true);\r\n      try {\r\n        const addressData = await fetchPostAddress(\r\n          post.locality_slug,\r\n          post.city_slug,\r\n          post.state_slug,\r\n          post.pincode\r\n        );\r\n        setAddress(addressData);\r\n      } catch (error) {\r\n        console.error('Error fetching address:', error);\r\n        setAddress(null);\r\n      } finally {\r\n        setAddressLoading(false);\r\n      }\r\n    };\r\n\r\n    loadAddress();\r\n  }, [business, post.locality_slug, post.city_slug, post.state_slug, post.pincode]);\r\n\r\n  if (!business) {\r\n    return null; // Skip rendering if business profile is missing\r\n  }\r\n\r\n  // Format the post date\r\n  const formattedDate = formatDistanceToNow(new Date(post.created_at), { addSuffix: true });\r\n\r\n  // Avatar fallback now uses User icon instead of initials\r\n\r\n  // Format address display\r\n  const getDisplayAddress = () => {\r\n    if (addressLoading) {\r\n      return 'Loading address...';\r\n    }\r\n\r\n    if (address) {\r\n      return formatAddressString(address);\r\n    }\r\n\r\n    // Fallback to slug-based formatting\r\n    return formatAddressFromSlugs(\r\n      post.locality_slug,\r\n      post.city_slug,\r\n      post.state_slug,\r\n      post.pincode\r\n    );\r\n  };\r\n\r\n  // Animation variants\r\n  const cardVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.4,\r\n        delay: index * 0.1,\r\n        ease: \"easeOut\"\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={cardVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className={cn(\r\n        \"bg-white dark:bg-black\",\r\n        \"overflow-hidden mb-4 md:mb-6\",\r\n        // Only show card styling on desktop\r\n        \"md:rounded-xl md:border md:border-neutral-200 md:dark:border-neutral-800 md:shadow-sm md:hover:shadow-md md:transition-all md:duration-300\"\r\n      )}\r\n    >\r\n      {/* Post Header */}\r\n      <div className=\"p-4 pb-2\">\r\n        {/* Business Info and Time */}\r\n        <div className=\"flex items-center justify-between mb-3\">\r\n          {business.business_slug ? (\r\n            <Link\r\n              href={`/${business.business_slug}`}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"flex items-center space-x-3 flex-1 min-w-0 group\"\r\n            >\r\n              <Avatar className=\"h-12 w-12 border-2 border-[var(--brand-gold)]/30 transition-transform group-hover:scale-105\">\r\n                <AvatarImage\r\n                  src={business.logo_url || ''}\r\n                  alt={business.business_name || 'Business'}\r\n                  className=\"object-cover\"\r\n                />\r\n                <AvatarFallback className=\"bg-muted text-foreground border border-[var(--brand-gold)]/30\">\r\n                  <User className=\"h-6 w-6\" />\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"flex-1 min-w-0\">\r\n                <h3 className=\"font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate group-hover:text-[var(--brand-gold)] transition-colors\">\r\n                  {business.business_name}\r\n                </h3>\r\n                {business.business_slug && (\r\n                  <div className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n                    @{business.business_slug}\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-neutral-400 dark:text-neutral-500 mt-1\">\r\n                  {formattedDate}\r\n                </div>\r\n              </div>\r\n            </Link>\r\n          ) : (\r\n            <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\r\n              <Avatar className=\"h-12 w-12 border-2 border-[var(--brand-gold)]/30\">\r\n                <AvatarImage\r\n                  src={business.logo_url || ''}\r\n                  alt={business.business_name || 'Customer'}\r\n                  className=\"object-cover\"\r\n                />\r\n                <AvatarFallback className=\"bg-muted text-foreground border border-[var(--brand-gold)]/30\">\r\n                  <User className=\"h-6 w-6\" />\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"flex-1 min-w-0\">\r\n                <h3 className=\"font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate\">\r\n                  {business.business_name}\r\n                </h3>\r\n                <div className=\"text-xs text-neutral-400 dark:text-neutral-500 mt-1\">\r\n                  {formattedDate}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          {/* Three-dot menu - show for all posts */}\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-full\"\r\n              >\r\n                <MoreVertical className=\"h-5 w-5 text-neutral-500 dark:text-neutral-400\" />\r\n                <span className=\"sr-only\">Open post menu</span>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-48\">\r\n              {/* Share option - available for all posts */}\r\n              <DropdownMenuItem\r\n                onClick={handleShare}\r\n                className=\"cursor-pointer\"\r\n              >\r\n                <Share2 className=\"h-4 w-4 mr-2\" />\r\n                Share post\r\n              </DropdownMenuItem>\r\n\r\n              {/* Owner-only options */}\r\n              {isOwner && !ownershipLoading && (\r\n                <>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem\r\n                    onClick={() => setIsEditing(true)}\r\n                    className=\"cursor-pointer\"\r\n                  >\r\n                    <Edit3 className=\"h-4 w-4 mr-2\" />\r\n                    Edit post\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem\r\n                    onClick={() => setShowDeleteDialog(true)}\r\n                    className=\"text-destructive focus:text-destructive cursor-pointer\"\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                    Delete post\r\n                  </DropdownMenuItem>\r\n                </>\r\n              )}\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n\r\n        {/* Address */}\r\n        <div className=\"flex items-center text-sm text-neutral-500 dark:text-neutral-400\">\r\n          <MapPin className=\"h-3.5 w-3.5 mr-1 flex-shrink-0\" />\r\n          <span>{getDisplayAddress()}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Post Content */}\r\n      <div className=\"px-4 pb-3\">\r\n        {isEditing ? (\r\n          <InlinePostAndProductEditor\r\n            postId={post.id}\r\n            initialContent={currentContent}\r\n            initialProductIds={currentProductIds}\r\n            initialImageUrl={post.image_url}\r\n            onSave={handleEditSave}\r\n            onCancel={handleEditCancel}\r\n          />\r\n        ) : disablePostClick ? (\r\n          <p className=\"text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line\">\r\n            {currentContent}\r\n          </p>\r\n        ) : (\r\n          <Link\r\n            href={generatePostPath(post.id)}\r\n            className=\"block cursor-pointer\"\r\n          >\r\n            <p className=\"text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line\">\r\n              {currentContent}\r\n            </p>\r\n          </Link>\r\n        )}\r\n      </div>\r\n\r\n      {/* Post Image */}\r\n      {post.image_url && (\r\n        disablePostClick ? (\r\n          <div\r\n            className={cn(\r\n              \"relative w-full transition-opacity duration-200\",\r\n              enableImageFullscreen ? \"cursor-pointer hover:opacity-95\" : \"\"\r\n            )}\r\n            onClick={enableImageFullscreen ? () => setShowFullscreenImage(true) : undefined}\r\n          >\r\n            {showActualAspectRatio ? (\r\n              <div className=\"relative w-full bg-neutral-100 dark:bg-neutral-800\">\r\n                <Image\r\n                  src={post.image_url}\r\n                  alt=\"Post image\"\r\n                  width={800}\r\n                  height={600}\r\n                  className={cn(\r\n                    \"w-full h-auto object-contain transition-all duration-300\",\r\n                    imageLoading && \"blur-sm scale-105\",\r\n                    imageError && \"hidden\"\r\n                  )}\r\n                  onLoad={() => setImageLoading(false)}\r\n                  onError={() => {\r\n                    setImageError(true);\r\n                    setImageLoading(false);\r\n                  }}\r\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                  priority={index < 3}\r\n                />\r\n                {imageLoading && (\r\n                  <div className=\"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse\" />\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <div className=\"relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800\">\r\n                <Image\r\n                  src={post.image_url}\r\n                  alt=\"Post image\"\r\n                  fill\r\n                  className={cn(\r\n                    \"object-cover transition-all duration-300\",\r\n                    imageLoading && \"blur-sm scale-105\",\r\n                    imageError && \"hidden\"\r\n                  )}\r\n                  onLoad={() => setImageLoading(false)}\r\n                  onError={() => {\r\n                    setImageError(true);\r\n                    setImageLoading(false);\r\n                  }}\r\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                  priority={index < 3}\r\n                />\r\n                {imageLoading && (\r\n                  <div className=\"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse\" />\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <Link href={generatePostPath(post.id)} className=\"block\">\r\n            <div className=\"relative w-full cursor-pointer hover:opacity-95 transition-opacity duration-200\">\r\n              {showActualAspectRatio ? (\r\n                <div className=\"relative w-full bg-neutral-100 dark:bg-neutral-800\">\r\n                  <Image\r\n                    src={post.image_url}\r\n                    alt=\"Post image\"\r\n                    width={800}\r\n                    height={600}\r\n                    className={cn(\r\n                      \"w-full h-auto object-contain transition-all duration-300\",\r\n                      imageLoading && \"blur-sm scale-105\",\r\n                      imageError && \"hidden\"\r\n                    )}\r\n                    onLoad={() => setImageLoading(false)}\r\n                    onError={() => {\r\n                      setImageError(true);\r\n                      setImageLoading(false);\r\n                    }}\r\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                    priority={index < 3}\r\n                  />\r\n                  {imageLoading && (\r\n                    <div className=\"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse\" />\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800\">\r\n                  <Image\r\n                    src={post.image_url}\r\n                    alt=\"Post image\"\r\n                    fill\r\n                    className={cn(\r\n                      \"object-cover transition-all duration-300\",\r\n                      imageLoading && \"blur-sm scale-105\",\r\n                      imageError && \"hidden\"\r\n                    )}\r\n                    onLoad={() => setImageLoading(false)}\r\n                    onError={() => {\r\n                      setImageError(true);\r\n                      setImageLoading(false);\r\n                    }}\r\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                    priority={index < 3}\r\n                  />\r\n                  {imageLoading && (\r\n                    <div className=\"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse\" />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </Link>\r\n        )\r\n      )}\r\n\r\n      {/* Linked Products */}\r\n      {currentProductIds.length > 0 && !isEditing && (\r\n        <div className=\"px-4 pt-4\">\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"p-1.5 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-lg\">\r\n                <Package className=\"h-4 w-4 text-[var(--brand-gold)]\" />\r\n              </div>\r\n              <h4 className=\"text-sm font-semibold text-neutral-900 dark:text-neutral-100\">\r\n                Featured Products ({linkedProducts.length})\r\n              </h4>\r\n            </div>\r\n\r\n              {isLoadingProducts ? (\r\n                <div className=\"flex justify-center py-8\">\r\n                  <div className=\"flex flex-col items-center gap-2\">\r\n                    <Loader2 className=\"h-6 w-6 animate-spin text-[var(--brand-gold)]\" />\r\n                    <span className=\"text-xs text-muted-foreground\">Loading products...</span>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <motion.div\r\n                  ref={productsScrollRef}\r\n                  className=\"flex gap-3 overflow-x-auto scrollbar-hide pb-2\"\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.3 }}\r\n                  style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\r\n                >\r\n                  {linkedProducts.map((product) => (\r\n                    <motion.div\r\n                      key={product.id}\r\n                      className=\"flex-shrink-0 w-40\"\r\n                      initial={{ opacity: 0, scale: 0.9 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      transition={{ duration: 0.2 }}\r\n                    >\r\n                      <div\r\n                        className=\"block h-full cursor-pointer\"\r\n                        onClick={(e) => {\r\n                          if (business.business_slug) {\r\n                            handleProductClick(e, `/${business.business_slug}/product/${product.slug || product.id}`);\r\n                          }\r\n                        }}\r\n                      >\r\n                        <ProductListItem\r\n                          product={{\r\n                            ...product,\r\n                            // Convert to ProductServiceData format\r\n                            description: undefined,\r\n                            product_type: 'physical' as const,\r\n                            base_price: product.base_price || 0,\r\n                            slug: product.slug || undefined,\r\n                            is_available: true,\r\n                            images: product.image_url ? [product.image_url] : undefined,\r\n                            featured_image_index: 0,\r\n                            created_at: new Date(),\r\n                            updated_at: new Date(),\r\n                          }}\r\n                          isLink={false}\r\n                        />\r\n                      </div>\r\n                    </motion.div>\r\n                  ))}\r\n                </motion.div>\r\n              )}\r\n            </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Post Actions - Full Width */}\r\n      <div className=\"p-4 pt-3\">\r\n        <PostActions\r\n          business={business}\r\n          hasWhatsApp={!!(business.whatsapp_number && business.whatsapp_number.trim() !== '')}\r\n          hasPhone={!!(business.phone && business.phone.trim() !== '')}\r\n          _postId={post.id}\r\n          onShare={handleShare}\r\n        />\r\n      </div>\r\n\r\n      {/* Delete Dialog */}\r\n      <PostDeleteDialog\r\n        isOpen={showDeleteDialog}\r\n        onOpenChange={setShowDeleteDialog}\r\n        postId={post.id}\r\n        postContent={currentContent}\r\n        onDeleteSuccess={handleDeleteSuccess}\r\n      />\r\n\r\n      {/* Full-screen Image Modal */}\r\n      {enableImageFullscreen && showFullscreenImage && post.image_url && (\r\n        <div\r\n          className=\"fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4\"\r\n          onClick={() => setShowFullscreenImage(false)}\r\n        >\r\n          <div className=\"relative max-w-full max-h-full\">\r\n            <button\r\n              onClick={() => setShowFullscreenImage(false)}\r\n              className=\"absolute top-4 right-4 z-10 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors\"\r\n            >\r\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n            <Image\r\n              src={post.image_url}\r\n              alt=\"Post image fullscreen\"\r\n              width={1200}\r\n              height={800}\r\n              className=\"max-w-full max-h-full object-contain\"\r\n              onClick={(e) => e.stopPropagation()}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;;;AA5BA;;;;;;;;;;;;;;;;;;;;;AAyCe,SAAS,eAAe,EACrC,IAAI,EACJ,QAAQ,CAAC,EACT,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,wBAAwB,KAAK,EAC7B,mBAAmB,KAAK,EACxB,wBAAwB,KAAK,EACT;;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,OAAO;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,KAAK,WAAW,IAAI,EAAE;IAC3F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,uCAAuC;IACvC,MAAM,EAAE,OAAO,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,mBAAgB,AAAD,EAAE;QAChE,gBAAgB,KAAK,WAAW;IAClC;IAEA,4BAA4B;IAC5B,MAAM,WAAW,KAAK,iBAAiB;IAEvC,+CAA+C;IAC/C,MAAM,iBAAiB,CAAC,YAAoB;QAC1C,aAAa;QACb,kBAAkB;QAClB,qBAAqB;QACrB,eAAe,KAAK,EAAE,EAAE;QACxB,mBAAmB,KAAK,EAAE,EAAE;IAC9B;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,aAAa;IACf;IAEA,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,oBAAoB;QACpB,eAAe,KAAK,EAAE;IACxB;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;YAEvC,+CAA+C;YAC/C,IAAI,UAAU,KAAK,EAAE;gBACnB,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO;oBACP,KAAK;gBACP;gBACA;YACF;YAEA,4BAA4B;YAC5B,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAEhB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,kBAAkB,MAAM,KAAK,GAAG;gBAClC,kBAAkB,EAAE;gBACpB,qBAAqB;gBACrB;YACF;YAEA,MAAM;0DAAgB;oBACpB,qBAAqB;oBACrB,IAAI;wBACF,MAAM,SAAS,MAAM,CAAA,GAAA,qKAAA,CAAA,qBAAkB,AAAD,EAAE;wBAExC,IAAI,CAAC,OAAO,OAAO,EAAE;4BACnB,QAAQ,KAAK,CAAC,4BAA4B,OAAO,KAAK;4BACtD,kBAAkB,EAAE;4BACpB;wBACF;wBAEA,yEAAyE;wBACzE,MAAM,kBAAkB,kBACrB,GAAG;sFAAC,CAAA,KAAM,OAAO,IAAI,EAAE;8FAAK,CAAA,UAAW,QAAQ,EAAE,KAAK;;qFACtD,MAAM,CAAC;wBAEV,kBAAkB;oBACpB,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,kBAAkB,EAAE;oBACtB,SAAU;wBACR,qBAAqB;oBACvB;gBACF;;YAEA;QACF;mCAAG;QAAC;KAAkB;IAEtB,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,YAAY,kBAAkB,OAAO;YAC3C,IAAI,CAAC,WAAW;YAEhB,MAAM;4DAAkB,CAAC;oBACvB,IAAI,UAAU,WAAW,IAAI,UAAU,WAAW,EAAE,QAAQ,iCAAiC;oBAE7F,cAAc;oBACd,UAAU,EAAE,KAAK,GAAG,UAAU,UAAU;oBACxC,cAAc,UAAU,UAAU;oBAClC,gBAAgB;oBAChB,UAAU,KAAK,CAAC,MAAM,GAAG;oBACzB,EAAE,cAAc;gBAClB;;YAEA,MAAM;4DAAkB,CAAC;oBACvB,IAAI,CAAC,cAAc,UAAU,WAAW,IAAI,UAAU,WAAW,EAAE;oBAEnE,EAAE,cAAc;oBAChB,MAAM,IAAI,EAAE,KAAK,GAAG,UAAU,UAAU;oBACxC,MAAM,OAAO,CAAC,IAAI,MAAM,IAAI,GAAG,0BAA0B;oBACzD,MAAM,kBAAkB,KAAK,GAAG,CAAC,IAAI;oBACrC,gBAAgB;oBAChB,UAAU,UAAU,GAAG,aAAa;gBACtC;;YAEA,MAAM;0DAAgB;oBACpB,cAAc;oBACd,UAAU,KAAK,CAAC,MAAM,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG,SAAS;gBACpF;;YAEA,MAAM;6DAAmB;oBACvB,cAAc;oBACd,UAAU,KAAK,CAAC,MAAM,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG,SAAS;gBACpF;;YAEA,qBAAqB;YACrB,UAAU,KAAK,CAAC,MAAM,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG,SAAS;YAElF,UAAU,gBAAgB,CAAC,aAAa;YACxC,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;YACrC,UAAU,gBAAgB,CAAC,cAAc;YAEzC;4CAAO;oBACL,UAAU,mBAAmB,CAAC,aAAa;oBAC3C,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,WAAW;oBACxC,UAAU,mBAAmB,CAAC,cAAc;gBAC9C;;QACF;mCAAG;QAAC;QAAgB;QAAY;QAAQ;QAAY;KAAa;IAEjE,2DAA2D;IAC3D,MAAM,qBAAqB,CAAC,GAAqB;QAC/C,IAAI,eAAe,GAAG;YACpB,EAAE,cAAc;YAChB;QACF;QACA,qCAAqC;QACrC,OAAO,IAAI,CAAC,YAAY,UAAU;IACpC;IAEA,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,UAAU,QAAQ,mCAAmC;YAE1D,MAAM;wDAAc;oBAClB,kBAAkB;oBAClB,IAAI;wBACF,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EACvC,KAAK,aAAa,EAClB,KAAK,SAAS,EACd,KAAK,UAAU,EACf,KAAK,OAAO;wBAEd,WAAW;oBACb,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,WAAW;oBACb,SAAU;wBACR,kBAAkB;oBACpB;gBACF;;YAEA;QACF;mCAAG;QAAC;QAAU,KAAK,aAAa;QAAE,KAAK,SAAS;QAAE,KAAK,UAAU;QAAE,KAAK,OAAO;KAAC;IAEhF,IAAI,CAAC,UAAU;QACb,OAAO,MAAM,gDAAgD;IAC/D;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,sJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,KAAK,UAAU,GAAG;QAAE,WAAW;IAAK;IAEvF,yDAAyD;IAEzD,yBAAyB;IACzB,MAAM,oBAAoB;QACxB,IAAI,gBAAgB;YAClB,OAAO;QACT;QAEA,IAAI,SAAS;YACX,OAAO,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE;QAC7B;QAEA,oCAAoC;QACpC,OAAO,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAC1B,KAAK,aAAa,EAClB,KAAK,SAAS,EACd,KAAK,UAAU,EACf,KAAK,OAAO;IAEhB;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,OAAO,QAAQ;gBACf,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0BACA,gCACA,oCAAoC;QACpC;;0BAIF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,aAAa,iBACrB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;gCAClC,QAAO;gCACP,KAAI;gCACJ,WAAU;;kDAEV,6LAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,8HAAA,CAAA,cAAW;gDACV,KAAK,SAAS,QAAQ,IAAI;gDAC1B,KAAK,SAAS,aAAa,IAAI;gDAC/B,WAAU;;;;;;0DAEZ,6LAAC,8HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACxB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,SAAS,aAAa;;;;;;4CAExB,SAAS,aAAa,kBACrB,6LAAC;gDAAI,WAAU;;oDAAwD;oDACnE,SAAS,aAAa;;;;;;;0DAG5B,6LAAC;gDAAI,WAAU;0DACZ;;;;;;;;;;;;;;;;;qDAKP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,8HAAA,CAAA,cAAW;gDACV,KAAK,SAAS,QAAQ,IAAI;gDAC1B,KAAK,SAAS,aAAa,IAAI;gDAC/B,WAAU;;;;;;0DAEZ,6LAAC,8HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACxB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,SAAS,aAAa;;;;;;0DAEzB,6LAAC;gDAAI,WAAU;0DACZ;;;;;;;;;;;;;;;;;;0CAMT,6LAAC,wIAAA,CAAA,eAAY;;kDACX,6LAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,6LAAC,6NAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;kDAG9B,6LAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DAEzC,6LAAC,wIAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAKpC,WAAW,CAAC,kCACX;;kEACE,6LAAC,wIAAA,CAAA,wBAAqB;;;;;kEACtB,6LAAC,wIAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,aAAa;wDAC5B,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGpC,6LAAC,wIAAA,CAAA,wBAAqB;;;;;kEACtB,6LAAC,wIAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,oBAAoB;wDACnC,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAM;;;;;;;;;;;;;;;;;;0BAKX,6LAAC;gBAAI,WAAU;0BACZ,0BACC,6LAAC,yKAAA,CAAA,UAA0B;oBACzB,QAAQ,KAAK,EAAE;oBACf,gBAAgB;oBAChB,mBAAmB;oBACnB,iBAAiB,KAAK,SAAS;oBAC/B,QAAQ;oBACR,UAAU;;;;;2BAEV,iCACF,6LAAC;oBAAE,WAAU;8BACV;;;;;yCAGH,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,EAAE;oBAC9B,WAAU;8BAEV,cAAA,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;YAOR,KAAK,SAAS,IAAI,CACjB,iCACE,6LAAC;gBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mDACA,wBAAwB,oCAAoC;gBAE9D,SAAS,wBAAwB,IAAM,uBAAuB,QAAQ;0BAErE,sCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS;4BACnB,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA,gBAAgB,qBAChB,cAAc;4BAEhB,QAAQ,IAAM,gBAAgB;4BAC9B,SAAS;gCACP,cAAc;gCACd,gBAAgB;4BAClB;4BACA,OAAM;4BACN,UAAU,QAAQ;;;;;;wBAEnB,8BACC,6LAAC;4BAAI,WAAU;;;;;;;;;;;yCAInB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS;4BACnB,KAAI;4BACJ,IAAI;4BACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4CACA,gBAAgB,qBAChB,cAAc;4BAEhB,QAAQ,IAAM,gBAAgB;4BAC9B,SAAS;gCACP,cAAc;gCACd,gBAAgB;4BAClB;4BACA,OAAM;4BACN,UAAU,QAAQ;;;;;;wBAEnB,8BACC,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;qCAMvB,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,EAAE;gBAAG,WAAU;0BAC/C,cAAA,6LAAC;oBAAI,WAAU;8BACZ,sCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,SAAS;gCACnB,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA,gBAAgB,qBAChB,cAAc;gCAEhB,QAAQ,IAAM,gBAAgB;gCAC9B,SAAS;oCACP,cAAc;oCACd,gBAAgB;gCAClB;gCACA,OAAM;gCACN,UAAU,QAAQ;;;;;;4BAEnB,8BACC,6LAAC;gCAAI,WAAU;;;;;;;;;;;6CAInB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,SAAS;gCACnB,KAAI;gCACJ,IAAI;gCACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4CACA,gBAAgB,qBAChB,cAAc;gCAEhB,QAAQ,IAAM,gBAAgB;gCAC9B,SAAS;oCACP,cAAc;oCACd,gBAAgB;gCAClB;gCACA,OAAM;gCACN,UAAU,QAAQ;;;;;;4BAEnB,8BACC,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;oBAO7B;YAGC,kBAAkB,MAAM,GAAG,KAAK,CAAC,2BAChC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCAAG,WAAU;;wCAA+D;wCACvD,eAAe,MAAM;wCAAC;;;;;;;;;;;;;wBAI3C,kCACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;iDAIpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,KAAK;4BACL,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC;wCACC,WAAU;wCACV,SAAS,CAAC;4CACR,IAAI,SAAS,aAAa,EAAE;gDAC1B,mBAAmB,GAAG,CAAC,CAAC,EAAE,SAAS,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;4CAC1F;wCACF;kDAEA,cAAA,6LAAC,wIAAA,CAAA,UAAe;4CACd,SAAS;gDACP,GAAG,OAAO;gDACV,uCAAuC;gDACvC,aAAa;gDACb,cAAc;gDACd,YAAY,QAAQ,UAAU,IAAI;gDAClC,MAAM,QAAQ,IAAI,IAAI;gDACtB,cAAc;gDACd,QAAQ,QAAQ,SAAS,GAAG;oDAAC,QAAQ,SAAS;iDAAC,GAAG;gDAClD,sBAAsB;gDACtB,YAAY,IAAI;gDAChB,YAAY,IAAI;4CAClB;4CACA,QAAQ;;;;;;;;;;;mCA5BP,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BAwC/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+IAAA,CAAA,UAAW;oBACV,UAAU;oBACV,aAAa,CAAC,CAAC,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,IAAI,OAAO,EAAE;oBAClF,UAAU,CAAC,CAAC,CAAC,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,IAAI,OAAO,EAAE;oBAC3D,SAAS,KAAK,EAAE;oBAChB,SAAS;;;;;;;;;;;0BAKb,6LAAC,+JAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,cAAc;gBACd,QAAQ,KAAK,EAAE;gBACf,aAAa;gBACb,iBAAiB;;;;;;YAIlB,yBAAyB,uBAAuB,KAAK,SAAS,kBAC7D,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;0BAEtC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,uBAAuB;4BACtC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS;4BACnB,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAnnBwB;;QA4B2B,4JAAA,CAAA,mBAAgB;;;KA5B3C", "debugId": null}}, {"offset": {"line": 4752, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/UnifiedPostCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\nimport ModernPostCard from './ModernPostCard';\r\nimport { PostWithBusinessProfile } from '@/lib/types/posts';\r\n\r\ninterface UnifiedPostCardProps {\r\n  post: UnifiedPost;\r\n  index?: number;\r\n  onPostUpdate?: (_postId: string, _newContent: string) => void;\r\n  onPostDelete?: (_postId: string) => void;\r\n  onProductsUpdate?: (_postId: string, _newProductIds: string[]) => void;\r\n  showActualAspectRatio?: boolean;\r\n  disablePostClick?: boolean;\r\n  enableImageFullscreen?: boolean;\r\n}\r\n\r\nexport default function UnifiedPostCard({\r\n  post,\r\n  index = 0,\r\n  onPostUpdate,\r\n  onPostDelete,\r\n  onProductsUpdate,\r\n  showActualAspectRatio = false,\r\n  disablePostClick = false,\r\n  enableImageFullscreen = false\r\n}: UnifiedPostCardProps) {\r\n\r\n  // Transform unified post to business post format for consistent rendering\r\n  // Now using author_name and author_avatar directly from the unified_posts view\r\n  const businessPost: PostWithBusinessProfile = {\r\n    id: post.id,\r\n    business_id: post.author_id,\r\n    content: post.content,\r\n    image_url: post.image_url,\r\n    created_at: post.created_at,\r\n    updated_at: post.updated_at,\r\n    city_slug: post.city_slug,\r\n    state_slug: post.state_slug,\r\n    locality_slug: post.locality_slug,\r\n    pincode: post.pincode,\r\n    product_ids: post.product_ids,\r\n    mentioned_business_ids: post.mentioned_business_ids,\r\n    business_profiles: {\r\n      id: post.author_id,\r\n      business_name: post.author_name || (post.post_source === 'customer' ? 'Customer' : 'Business'),\r\n      logo_url: post.author_avatar,\r\n      business_slug: post.business_slug, // Now available from unified_posts view\r\n      phone: post.phone, // Now available from unified_posts view\r\n      whatsapp_number: post.whatsapp_number, // Now available from unified_posts view\r\n      city: null,\r\n      state: null\r\n    }\r\n  };\r\n\r\n  return (\r\n    <ModernPostCard\r\n      post={businessPost}\r\n      index={index}\r\n      onPostUpdate={onPostUpdate}\r\n      onPostDelete={onPostDelete}\r\n      onProductsUpdate={onProductsUpdate}\r\n      showActualAspectRatio={showActualAspectRatio}\r\n      disablePostClick={disablePostClick}\r\n      enableImageFullscreen={enableImageFullscreen}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAiBe,SAAS,gBAAgB,EACtC,IAAI,EACJ,QAAQ,CAAC,EACT,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,wBAAwB,KAAK,EAC7B,mBAAmB,KAAK,EACxB,wBAAwB,KAAK,EACR;IAErB,0EAA0E;IAC1E,+EAA+E;IAC/E,MAAM,eAAwC;QAC5C,IAAI,KAAK,EAAE;QACX,aAAa,KAAK,SAAS;QAC3B,SAAS,KAAK,OAAO;QACrB,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,YAAY,KAAK,UAAU;QAC3B,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,eAAe,KAAK,aAAa;QACjC,SAAS,KAAK,OAAO;QACrB,aAAa,KAAK,WAAW;QAC7B,wBAAwB,KAAK,sBAAsB;QACnD,mBAAmB;YACjB,IAAI,KAAK,SAAS;YAClB,eAAe,KAAK,WAAW,IAAI,CAAC,KAAK,WAAW,KAAK,aAAa,aAAa,UAAU;YAC7F,UAAU,KAAK,aAAa;YAC5B,eAAe,KAAK,aAAa;YACjC,OAAO,KAAK,KAAK;YACjB,iBAAiB,KAAK,eAAe;YACrC,MAAM;YACN,OAAO;QACT;IACF;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAc;QACb,MAAM;QACN,OAAO;QACP,cAAc;QACd,cAAc;QACd,kBAAkB;QAClB,uBAAuB;QACvB,kBAAkB;QAClB,uBAAuB;;;;;;AAG7B;KAlDwB", "debugId": null}}, {"offset": {"line": 4814, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/PostCardSkeleton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface PostCardSkeletonProps {\r\n  index?: number;\r\n  showImage?: boolean;\r\n  showProducts?: boolean;\r\n}\r\n\r\nexport default function PostCardSkeleton({ \r\n  index = 0, \r\n  showImage = true, \r\n  showProducts = false \r\n}: PostCardSkeletonProps) {\r\n  const cardVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.4,\r\n        delay: index * 0.1,\r\n        ease: \"easeOut\"\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={cardVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className={cn(\r\n        \"bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800\",\r\n        \"shadow-sm overflow-hidden mb-4 md:mb-6\"\r\n      )}\r\n    >\r\n      {/* Header Skeleton */}\r\n      <div className=\"p-4 pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3 flex-1\">\r\n            {/* Avatar Skeleton */}\r\n            <Skeleton className=\"h-12 w-12 rounded-full\" />\r\n            \r\n            <div className=\"flex-1 space-y-2\">\r\n              {/* Business Name */}\r\n              <Skeleton className=\"h-4 w-32\" />\r\n              \r\n              {/* Location and Time */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Skeleton className=\"h-3 w-24\" />\r\n                <Skeleton className=\"h-3 w-1 rounded-full\" />\r\n                <Skeleton className=\"h-3 w-16\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* More Button */}\r\n          <Skeleton className=\"h-8 w-8 rounded-full\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content Skeleton */}\r\n      <div className=\"px-4 pb-3 space-y-2\">\r\n        <Skeleton className=\"h-4 w-full\" />\r\n        <Skeleton className=\"h-4 w-3/4\" />\r\n        <Skeleton className=\"h-4 w-1/2\" />\r\n      </div>\r\n\r\n      {/* Image Skeleton */}\r\n      {showImage && (\r\n        <div className=\"relative w-full\">\r\n          <Skeleton className=\"w-full aspect-[4/3]\" />\r\n        </div>\r\n      )}\r\n\r\n      {/* Products Skeleton */}\r\n      {showProducts && (\r\n        <div className=\"px-4 pt-4 space-y-3\">\r\n          {/* Products Header */}\r\n          <div className=\"flex items-center gap-2\">\r\n            <Skeleton className=\"h-6 w-6 rounded-lg\" />\r\n            <Skeleton className=\"h-4 w-32\" />\r\n          </div>\r\n          \r\n          {/* Products Grid */}\r\n          <div className=\"grid grid-cols-2 gap-3\">\r\n            {Array.from({ length: 2 }).map((_, i) => (\r\n              <div key={i} className=\"bg-neutral-50 dark:bg-neutral-900 rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden\">\r\n                <Skeleton className=\"w-full aspect-square\" />\r\n                <div className=\"p-3 space-y-2\">\r\n                  <Skeleton className=\"h-4 w-full\" />\r\n                  <Skeleton className=\"h-4 w-16\" />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Actions Skeleton */}\r\n      <div className=\"p-4 pt-3\">\r\n        <div className=\"flex gap-3\">\r\n          <Skeleton className=\"h-10 flex-1\" />\r\n          <Skeleton className=\"h-10 flex-1\" />\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYe,SAAS,iBAAiB,EACvC,QAAQ,CAAC,EACT,YAAY,IAAI,EAChB,eAAe,KAAK,EACE;IACtB,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,OAAO,QAAQ;gBACf,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uFACA;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDAGpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAM1B,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;YAIrB,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;YAKvB,8BACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAItB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;+BAJd;;;;;;;;;;;;;;;;0BAalB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;KApGwB", "debugId": null}}, {"offset": {"line": 5106, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/FilterPills.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { FeedFilterType } from '@/lib/types/posts';\r\nimport { cn } from '@/lib/utils';\r\nimport { Sparkles, Users, MapPin, Hash, Building, Globe, List } from 'lucide-react';\r\n\r\ninterface FilterPillsProps {\r\n  activeFilter: FeedFilterType;\r\n  onFilterChange: (_filter: FeedFilterType) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst filterOptions = [\r\n  { value: 'smart' as FeedFilterType, label: 'Smart Feed', icon: Sparkles },\r\n  { value: 'subscribed' as FeedFilterType, label: 'Following', icon: Users },\r\n  { value: 'locality' as FeedFilterType, label: 'Locality', icon: MapPin },\r\n  { value: 'pincode' as FeedFilterType, label: 'Pincode', icon: Hash },\r\n  { value: 'city' as FeedFilterType, label: 'City', icon: Building },\r\n  { value: 'state' as FeedFilterType, label: 'State', icon: Globe },\r\n  { value: 'all' as FeedFilterType, label: 'All Posts', icon: List },\r\n];\r\n\r\nexport default function FilterPills({ activeFilter, onFilterChange, isLoading = false }: FilterPillsProps) {\r\n  const activeFilterOption = filterOptions.find(option => option.value === activeFilter);\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {/* Selected Filter Display - Mobile/Tablet only */}\r\n      <div className=\"md:hidden mb-3 px-4\">\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          Showing: <span className=\"font-medium text-foreground\">{activeFilterOption?.label || 'Smart Feed'}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile: Horizontal scroll, Desktop: Centered */}\r\n      <div className=\"flex gap-2 overflow-x-auto scrollbar-hide pb-2 md:pb-0 md:justify-center\">\r\n        <div className=\"flex gap-2 md:flex-wrap md:justify-center\">\r\n          {filterOptions.map((option) => {\r\n            const Icon = option.icon;\r\n            const isActive = activeFilter === option.value;\r\n\r\n            return (\r\n              <motion.button\r\n                key={option.value}\r\n                onClick={() => !isLoading && onFilterChange(option.value)}\r\n                disabled={isLoading}\r\n                whileHover={{ scale: 1.02 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                className={cn(\r\n                  \"flex items-center gap-2 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap relative cursor-pointer\",\r\n                  \"border border-neutral-200 dark:border-neutral-700\",\r\n                  // Mobile/Tablet: Icon only (circular)\r\n                  \"w-10 h-10 p-0 justify-center md:px-4 md:py-2.5 md:w-auto md:h-auto\",\r\n                  isActive\r\n                    ? \"bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)] shadow-md\"\r\n                    : \"bg-white dark:bg-black text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-900 hover:border-neutral-300 dark:hover:border-neutral-600\",\r\n                  isLoading && \"opacity-50 cursor-not-allowed\"\r\n                )}\r\n              >\r\n                <Icon className=\"h-4 w-4\" />\r\n                <span className=\"hidden md:inline\">{option.label}</span>\r\n\r\n                {/* Active indicator */}\r\n                {isActive && (\r\n                  <motion.div\r\n                    layoutId=\"activeFilter\"\r\n                    className=\"absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10\"\r\n                    initial={false}\r\n                    transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\r\n                  />\r\n                )}\r\n              </motion.button>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAaA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAA2B,OAAO;QAAc,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACxE;QAAE,OAAO;QAAgC,OAAO;QAAa,MAAM,uMAAA,CAAA,QAAK;IAAC;IACzE;QAAE,OAAO;QAA8B,OAAO;QAAY,MAAM,6MAAA,CAAA,SAAM;IAAC;IACvE;QAAE,OAAO;QAA6B,OAAO;QAAW,MAAM,qMAAA,CAAA,OAAI;IAAC;IACnE;QAAE,OAAO;QAA0B,OAAO;QAAQ,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACjE;QAAE,OAAO;QAA2B,OAAO;QAAS,MAAM,uMAAA,CAAA,QAAK;IAAC;IAChE;QAAE,OAAO;QAAyB,OAAO;QAAa,MAAM,qMAAA,CAAA,OAAI;IAAC;CAClE;AAEc,SAAS,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,KAAK,EAAoB;IACvG,MAAM,qBAAqB,cAAc,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IAEzE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAAgC;sCACpC,6LAAC;4BAAK,WAAU;sCAA+B,oBAAoB,SAAS;;;;;;;;;;;;;;;;;0BAKzF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC;wBAClB,MAAM,OAAO,OAAO,IAAI;wBACxB,MAAM,WAAW,iBAAiB,OAAO,KAAK;wBAE9C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BAEZ,SAAS,IAAM,CAAC,aAAa,eAAe,OAAO,KAAK;4BACxD,UAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kIACA,qDACA,sCAAsC;4BACtC,sEACA,WACI,oGACA,sKACJ,aAAa;;8CAGf,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAoB,OAAO,KAAK;;;;;;gCAG/C,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,SAAS;oCACT,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;;;;;;;2BAzBzD,OAAO,KAAK;;;;;oBA8BvB;;;;;;;;;;;;;;;;;AAKV;KAxDwB", "debugId": null}}, {"offset": {"line": 5275, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/ModernFeedHeader.tsx"], "sourcesContent": ["'use client';\r\n\r\n\r\nimport { FeedFilterType } from '@/lib/types/posts';\r\nimport FilterPills from './FilterPills';\r\n\r\ninterface ModernFeedHeaderProps {\r\n  activeFilter: FeedFilterType;\r\n  onFilterChange: (_filter: FeedFilterType) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport default function ModernFeedHeader({\r\n  activeFilter,\r\n  onFilterChange,\r\n  isLoading = false\r\n}: ModernFeedHeaderProps) {\r\n  return (\r\n    <div className=\"mb-8\">\r\n      {/* Filter Section */}\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <h2 className=\"text-lg font-semibold text-foreground\">\r\n          Your Feed\r\n        </h2>\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          Choose your feed preference\r\n        </div>\r\n      </div>\r\n\r\n      <FilterPills\r\n        activeFilter={activeFilter}\r\n        onFilterChange={onFilterChange}\r\n        isLoading={isLoading}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAYe,SAAS,iBAAiB,EACvC,YAAY,EACZ,cAAc,EACd,YAAY,KAAK,EACK;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAI,WAAU;kCAAgC;;;;;;;;;;;;0BAKjD,6LAAC,+IAAA,CAAA,UAAW;gBACV,cAAc;gBACd,gBAAgB;gBAChB,WAAW;;;;;;;;;;;;AAInB;KAxBwB", "debugId": null}}, {"offset": {"line": 5340, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/ModernFeedContainer.tsx"], "sourcesContent": ["'use client';\r\n\r\n\r\nimport { ReactNode } from 'react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface ModernFeedContainerProps {\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport default function ModernFeedContainer({ children, className }: ModernFeedContainerProps) {\r\n  return (\r\n    <div className={cn(\"min-h-screen bg-background\", className)}>\r\n      <div className=\"w-full py-6 lg:px-8\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAWe,SAAS,oBAAoB,EAAE,QAAQ,EAAE,SAAS,EAA4B;IAC3F,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBAC/C,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;KARwB", "debugId": null}}, {"offset": {"line": 5377, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/customerPosts/crud.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/client';\nimport { CustomerPostFormData } from '@/lib/types/posts';\n\nexport interface ActionResponse {\n  success: boolean;\n  message: string;\n  error?: string;\n  data?: unknown;\n}\n\n/**\n * Create a new customer post\n */\nexport async function createCustomerPost(formData: CustomerPostFormData): Promise<ActionResponse> {\n  const supabase = await createClient();\n\n  // Get the current user\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\n\n  if (userError || !user) {\n    return {\n      success: false,\n      message: 'Authentication required',\n      error: 'You must be logged in to create a post'\n    };\n  }\n\n  // Get the user's customer profile (use private table for own profile access)\n  const { data: customerProfile, error: profileError } = await supabase\n    .from('customer_profiles')\n    .select('id, city_slug, state_slug, locality_slug, pincode, avatar_url')\n    .eq('id', user.id)\n    .single();\n\n  if (profileError || !customerProfile) {\n    return {\n      success: false,\n      message: 'Customer profile not found',\n      error: 'You must have a customer profile to create a post'\n    };\n  }\n\n  // Prepare post data\n  const postData = {\n    customer_id: user.id,\n    content: formData.content,\n    image_url: formData.image_url || null,\n    city_slug: customerProfile.city_slug,\n    state_slug: customerProfile.state_slug,\n    locality_slug: customerProfile.locality_slug,\n    pincode: customerProfile.pincode,\n    mentioned_business_ids: formData.mentioned_business_ids || [],\n  };\n\n  // Insert the post\n  const { data, error } = await supabase\n    .from('customer_posts')\n    .insert(postData)\n    .select()\n    .single();\n\n  if (error) {\n    console.error('Error creating customer post:', error);\n    return {\n      success: false,\n      message: 'Failed to create post',\n      error: error.message\n    };\n  }\n\n  return {\n    success: true,\n    message: 'Post created successfully',\n    data\n  };\n}\n\n/**\n * Update an existing customer post\n */\nexport async function updateCustomerPost(postId: string, formData: CustomerPostFormData): Promise<ActionResponse> {\n  const supabase = await createClient();\n\n  // Get the current user\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\n\n  if (userError || !user) {\n    return {\n      success: false,\n      message: 'Authentication required',\n      error: 'You must be logged in to update a post'\n    };\n  }\n\n  // Check if the post exists and belongs to the user\n  const { data: existingPost, error: postError } = await supabase\n    .from('customer_posts')\n    .select('id')\n    .eq('id', postId)\n    .eq('customer_id', user.id)\n    .single();\n\n  if (postError || !existingPost) {\n    return {\n      success: false,\n      message: 'Post not found',\n      error: 'The post does not exist or you do not have permission to update it'\n    };\n  }\n\n  // Prepare update data\n  const updateData = {\n    content: formData.content,\n    image_url: formData.image_url || null,\n    mentioned_business_ids: formData.mentioned_business_ids || [],\n    updated_at: new Date().toISOString()\n  };\n\n  // Update the post\n  const { data, error } = await supabase\n    .from('customer_posts')\n    .update(updateData)\n    .eq('id', postId)\n    .select()\n    .single();\n\n  if (error) {\n    console.error('Error updating customer post:', error);\n    return {\n      success: false,\n      message: 'Failed to update post',\n      error: error.message\n    };\n  }\n\n  return {\n    success: true,\n    message: 'Post updated successfully',\n    data\n  };\n}\n\n/**\n * Delete a customer post\n */\nexport async function deleteCustomerPost(postId: string): Promise<ActionResponse> {\n  const supabase = await createClient();\n\n  // Get the current user\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\n\n  if (userError || !user) {\n    return {\n      success: false,\n      message: 'Authentication required',\n      error: 'You must be logged in to delete a post'\n    };\n  }\n\n  // Check if the post exists and belongs to the user\n  const { data: existingPost, error: postError } = await supabase\n    .from('customer_posts')\n    .select('id')\n    .eq('id', postId)\n    .eq('customer_id', user.id)\n    .single();\n\n  if (postError || !existingPost) {\n    return {\n      success: false,\n      message: 'Post not found',\n      error: 'The post does not exist or you do not have permission to delete it'\n    };\n  }\n\n  // Delete the post\n  const { error } = await supabase\n    .from('customer_posts')\n    .delete()\n    .eq('id', postId);\n\n  if (error) {\n    console.error('Error deleting customer post:', error);\n    return {\n      success: false,\n      message: 'Failed to delete post',\n      error: error.message\n    };\n  }\n\n  return {\n    success: true,\n    message: 'Post deleted successfully'\n  };\n}\n\n/**\n * Get customer posts for a specific customer\n */\nexport async function getCustomerPosts(customerId?: string, page: number = 1, limit: number = 10): Promise<ActionResponse> {\n  const supabase = await createClient();\n\n  let query = supabase\n    .from('customer_posts')\n    .select(`\n      *,\n      customer_profiles (\n        id,\n        name,\n        avatar_url,\n        city,\n        state,\n        locality\n      )\n    `)\n    .order('created_at', { ascending: false });\n\n  // If customerId is provided, filter by it\n  if (customerId) {\n    query = query.eq('customer_id', customerId);\n  }\n\n  // Add pagination\n  const from = (page - 1) * limit;\n  const to = from + limit - 1;\n  query = query.range(from, to);\n\n  const { data, error, count } = await query;\n\n  if (error) {\n    console.error('Error fetching customer posts:', error);\n    return {\n      success: false,\n      message: 'Failed to fetch posts',\n      error: error.message\n    };\n  }\n\n  return {\n    success: true,\n    message: 'Posts fetched successfully',\n    data: {\n      items: data || [],\n      totalCount: count || 0,\n      hasMore: data ? data.length === limit : false\n    }\n  };\n}\n\n/**\n * Get a single customer post by ID\n */\nexport async function getCustomerPost(postId: string): Promise<ActionResponse> {\n  const supabase = await createClient();\n\n  const { data, error } = await supabase\n    .from('customer_posts')\n    .select(`\n      *,\n      customer_profiles (\n        id,\n        name,\n        avatar_url,\n        city,\n        state,\n        locality\n      )\n    `)\n    .eq('id', postId)\n    .single();\n\n  if (error) {\n    console.error('Error fetching customer post:', error);\n    return {\n      success: false,\n      message: 'Failed to fetch post',\n      error: error.message\n    };\n  }\n\n  return {\n    success: true,\n    message: 'Post fetched successfully',\n    data\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAaO,eAAe,mBAAmB,QAA8B;IACrE,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,6EAA6E;IAC7E,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,qBACL,MAAM,CAAC,iEACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,gBAAgB,CAAC,iBAAiB;QACpC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,WAAW;QACf,aAAa,KAAK,EAAE;QACpB,SAAS,SAAS,OAAO;QACzB,WAAW,SAAS,SAAS,IAAI;QACjC,WAAW,gBAAgB,SAAS;QACpC,YAAY,gBAAgB,UAAU;QACtC,eAAe,gBAAgB,aAAa;QAC5C,SAAS,gBAAgB,OAAO;QAChC,wBAAwB,SAAS,sBAAsB,IAAI,EAAE;IAC/D;IAEA,kBAAkB;IAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,mBAAmB,MAAc,EAAE,QAA8B;IACrF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,sBAAsB;IACtB,MAAM,aAAa;QACjB,SAAS,SAAS,OAAO;QACzB,WAAW,SAAS,SAAS,IAAI;QACjC,wBAAwB,SAAS,sBAAsB,IAAI,EAAE;QAC7D,YAAY,IAAI,OAAO,WAAW;IACpC;IAEA,kBAAkB;IAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,mBAAmB,MAAc;IACrD,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF;AAKO,eAAe,iBAAiB,UAAmB,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9F,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,IAAI,QAAQ,SACT,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EACA,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,0CAA0C;IAC1C,IAAI,YAAY;QACd,QAAQ,MAAM,EAAE,CAAC,eAAe;IAClC;IAEA,iBAAiB;IACjB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAC1B,MAAM,KAAK,OAAO,QAAQ;IAC1B,QAAQ,MAAM,KAAK,CAAC,MAAM;IAE1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;IAErC,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT,MAAM;YACJ,OAAO,QAAQ,EAAE;YACjB,YAAY,SAAS;YACrB,SAAS,OAAO,KAAK,MAAM,KAAK,QAAQ;QAC1C;IACF;AACF;AAKO,eAAe,gBAAgB,MAAc;IAClD,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 5589, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/customerPosts/index.ts"], "sourcesContent": ["// Export all customer post actions\r\nexport {\r\n  createCustomerPost,\r\n  updateCustomerPost,\r\n  deleteCustomerPost,\r\n  getCustomerPosts,\r\n  getCustomerPost,\r\n  type ActionResponse\r\n} from './crud';\r\n"], "names": [], "mappings": "AAAA,mCAAmC;;AACnC", "debugId": null}}, {"offset": {"line": 5611, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/forms/LocationDisplay.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { MapPin, Loader2, Edit } from 'lucide-react';\r\nimport { createClient } from '@/utils/supabase/client';\r\nimport { cn } from '@/lib/utils';\r\nimport { Button } from '@/components/ui/button';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface LocationDisplayProps {\r\n  className?: string;\r\n}\r\n\r\ninterface LocationData {\r\n  city?: string;\r\n  state?: string;\r\n  locality?: string;\r\n  pincode?: string;\r\n}\r\n\r\nexport default function LocationDisplay({ className }: LocationDisplayProps) {\r\n  const [location, setLocation] = useState<LocationData | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [userType, setUserType] = useState<'business' | 'customer' | null>(null);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const fetchUserLocation = async () => {\r\n      try {\r\n        const supabase = createClient();\r\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n        if (userError || !user) {\r\n          setError('Authentication required');\r\n          return;\r\n        }\r\n\r\n        // Try to get business profile first, then customer profile\r\n        const { data: businessProfile } = await supabase\r\n          .from('business_profiles')\r\n          .select('city, state, locality, pincode')\r\n          .eq('id', user.id)\r\n          .single();\r\n\r\n        if (businessProfile) {\r\n          setLocation(businessProfile);\r\n          setUserType('business');\r\n          return;\r\n        }\r\n\r\n        // Fallback to customer profile\r\n        const { data: customerProfile } = await supabase\r\n          .from('customer_profiles')\r\n          .select('city, state, locality, pincode')\r\n          .eq('id', user.id)\r\n          .single();\r\n\r\n        if (customerProfile) {\r\n          setLocation(customerProfile);\r\n          setUserType('customer');\r\n        } else {\r\n          setError('Location not found in profile');\r\n        }\r\n      } catch (err) {\r\n        console.error('Error fetching location:', err);\r\n        setError('Failed to load location');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserLocation();\r\n  }, []);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className={cn(\r\n        \"flex items-center gap-2 text-sm text-muted-foreground\",\r\n        className\r\n      )}>\r\n        <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n        <span>Loading location...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !location) {\r\n    return (\r\n      <div className={cn(\r\n        \"flex items-center gap-2 text-sm text-muted-foreground\",\r\n        className\r\n      )}>\r\n        <MapPin className=\"h-4 w-4\" />\r\n        <span>Location not available</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Format location string\r\n  const locationParts = [\r\n    location.locality,\r\n    location.city,\r\n    location.state,\r\n    location.pincode\r\n  ].filter(Boolean);\r\n\r\n  const locationString = locationParts.join(', ');\r\n\r\n  const handleEditLocation = () => {\r\n    if (userType === 'business') {\r\n      router.push('/dashboard/business/profile');\r\n    } else if (userType === 'customer') {\r\n      router.push('/dashboard/customer/profile');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={cn(\r\n      \"flex items-center justify-between gap-2 text-sm text-muted-foreground bg-muted/50 px-3 py-2 rounded-lg border\",\r\n      className\r\n    )}>\r\n      <div className=\"flex items-center gap-2\">\r\n        <MapPin className=\"h-4 w-4 text-[var(--brand-gold)]\" />\r\n        <span>\r\n          <span className=\"font-medium\">Posting from:</span> {locationString}\r\n        </span>\r\n      </div>\r\n      <Button\r\n        variant=\"ghost\"\r\n        size=\"sm\"\r\n        onClick={handleEditLocation}\r\n        className=\"h-6 w-6 p-0 text-muted-foreground hover:text-[var(--brand-gold)]\"\r\n      >\r\n        <Edit className=\"h-3 w-3\" />\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAoBe,SAAS,gBAAgB,EAAE,SAAS,EAAwB;;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IACzE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;+DAAoB;oBACxB,IAAI;wBACF,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;wBAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;wBAExE,IAAI,aAAa,CAAC,MAAM;4BACtB,SAAS;4BACT;wBACF;wBAEA,2DAA2D;wBAC3D,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,qBACL,MAAM,CAAC,kCACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBAET,IAAI,iBAAiB;4BACnB,YAAY;4BACZ,YAAY;4BACZ;wBACF;wBAEA,+BAA+B;wBAC/B,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,qBACL,MAAM,CAAC,kCACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBAET,IAAI,iBAAiB;4BACnB,YAAY;4BACZ,YAAY;wBACd,OAAO;4BACL,SAAS;wBACX;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,SAAS;oBACX,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;oCAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,yDACA;;8BAEA,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,6LAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,IAAI,SAAS,CAAC,UAAU;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,yDACA;;8BAEA,6LAAC,6MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;8BAClB,6LAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,yBAAyB;IACzB,MAAM,gBAAgB;QACpB,SAAS,QAAQ;QACjB,SAAS,IAAI;QACb,SAAS,KAAK;QACd,SAAS,OAAO;KACjB,CAAC,MAAM,CAAC;IAET,MAAM,iBAAiB,cAAc,IAAI,CAAC;IAE1C,MAAM,qBAAqB;QACzB,IAAI,aAAa,YAAY;YAC3B,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,aAAa,YAAY;YAClC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,iHACA;;0BAEA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAoB;4BAAE;;;;;;;;;;;;;0BAGxD,6LAAC,8HAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIxB;GAtHwB;;QAKP,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 5819, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/SocialMediaPostCreator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  Image as ImageIcon,\r\n  MapPin,\r\n  X,\r\n  Send,\r\n  Loader2\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { toast } from \"sonner\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\nimport { createCustomerPost, updateCustomerPost } from \"@/lib/actions/customerPosts\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Image from \"next/image\";\r\nimport LocationDisplay from \"./forms/LocationDisplay\";\r\n\r\ninterface SocialMediaPostCreatorProps {\r\n  customerName?: string;\r\n  onPostCreated?: () => void;\r\n}\r\n\r\nexport default function SocialMediaPostCreator({ \r\n  customerName, \r\n  onPostCreated \r\n}: SocialMediaPostCreatorProps) {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const [content, setContent] = useState(\"\");\r\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [customerAvatar, setCustomerAvatar] = useState<string | null>(null);\r\n  const [customerDisplayName, setCustomerDisplayName] = useState(customerName || \"\");\r\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\r\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\r\n  const [detectedImageUrl, setDetectedImageUrl] = useState<string | null>(null);\r\n  \r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const router = useRouter();\r\n\r\n  // Character limit\r\n  const MAX_CHARS = 2000;\r\n  const charCount = content.length;\r\n  const isOverLimit = charCount > MAX_CHARS;\r\n\r\n  // Fetch customer profile\r\n  useEffect(() => {\r\n    const fetchCustomerProfile = async () => {\r\n      const supabase = createClient();\r\n      const { data: { user } } = await supabase.auth.getUser();\r\n      \r\n      if (user) {\r\n        const { data: profile } = await supabase\r\n          .from('customer_profiles')\r\n          .select('name, avatar_url')\r\n          .eq('id', user.id)\r\n          .single();\r\n        \r\n        if (profile) {\r\n          setCustomerDisplayName(profile.name || customerName || \"User\");\r\n          setCustomerAvatar(profile.avatar_url);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchCustomerProfile();\r\n  }, [customerName]);\r\n\r\n  // Auto-resize textarea\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = 'auto';\r\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;\r\n    }\r\n  }, [content]);\r\n\r\n  // Focus textarea when expanded\r\n  useEffect(() => {\r\n    if (isExpanded && textareaRef.current) {\r\n      textareaRef.current.focus();\r\n    }\r\n  }, [isExpanded]);\r\n\r\n  const handleExpand = () => {\r\n    setIsExpanded(true);\r\n  };\r\n\r\n  const handleCollapse = () => {\r\n    if (!content.trim() && !imageUrl) {\r\n      setIsExpanded(false);\r\n    }\r\n  };\r\n\r\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    // Validate file type\r\n    if (!file.type.startsWith('image/')) {\r\n      toast.error('Please select an image file');\r\n      return;\r\n    }\r\n\r\n    // Validate file size (5MB limit)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      toast.error('Image size must be less than 5MB');\r\n      return;\r\n    }\r\n\r\n    // Store file and create preview URL\r\n    setSelectedFile(file);\r\n    const preview = URL.createObjectURL(file);\r\n    setPreviewUrl(preview);\r\n    setImageUrl(null); // Clear any existing URL\r\n  };\r\n\r\n  const handleImageRemove = () => {\r\n    setSelectedFile(null);\r\n    if (previewUrl) {\r\n      URL.revokeObjectURL(previewUrl);\r\n    }\r\n    setPreviewUrl(null);\r\n    setImageUrl(null);\r\n    setDetectedImageUrl(null);\r\n  };\r\n\r\n  // Function to detect image URLs in content\r\n  const detectImageUrl = (text: string) => {\r\n    const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(?:jpg|jpeg|png|gif|webp)(?:\\?[^\\s]*)?)/i;\r\n    const match = text.match(imageUrlRegex);\r\n    return match ? match[1] : null;\r\n  };\r\n\r\n  // Handle content change with URL detection\r\n  const handleContentChange = (newContent: string) => {\r\n    setContent(newContent);\r\n\r\n    // Detect image URL in content\r\n    const detectedUrl = detectImageUrl(newContent);\r\n    if (detectedUrl && detectedUrl !== detectedImageUrl) {\r\n      setDetectedImageUrl(detectedUrl);\r\n      // Only set as imageUrl if no file is selected\r\n      if (!selectedFile) {\r\n        setImageUrl(detectedUrl);\r\n      }\r\n    } else if (!detectedUrl && detectedImageUrl) {\r\n      setDetectedImageUrl(null);\r\n      // Only clear imageUrl if it was from detected URL (not from file upload)\r\n      if (!selectedFile) {\r\n        setImageUrl(null);\r\n      }\r\n    }\r\n  };\r\n\r\n  const checkCustomerProfile = async () => {\r\n    const supabase = createClient();\r\n    const { data: { user } } = await supabase.auth.getUser();\r\n\r\n    if (!user) {\r\n      toast.error('Please log in to continue');\r\n      return false;\r\n    }\r\n\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('name, pincode, city, state, locality')\r\n      .eq('id', user.id)\r\n      .single();\r\n\r\n    if (error) {\r\n      toast.error('Failed to check customer profile');\r\n      return false;\r\n    }\r\n\r\n    if (!profile?.name || profile.name.trim() === '') {\r\n      toast.error('Please complete your name in your profile before creating posts');\r\n      router.push('/dashboard/customer/profile');\r\n      return false;\r\n    }\r\n\r\n    if (!profile?.pincode || !profile?.city || !profile?.state || !profile?.locality) {\r\n      toast.error('Please complete your address in your profile before creating posts');\r\n      router.push('/dashboard/customer/profile');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!content.trim() && !previewUrl && !detectedImageUrl) {\r\n      toast.error('Please add some content or an image');\r\n      return;\r\n    }\r\n\r\n    if (isOverLimit) {\r\n      toast.error('Post content is too long');\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      const hasValidProfile = await checkCustomerProfile();\r\n      if (!hasValidProfile) {\r\n        setIsSubmitting(false);\r\n        return;\r\n      }\r\n\r\n      let result;\r\n\r\n      if (selectedFile && !imageUrl) {\r\n        // Handle file upload for new posts\r\n        const createResult = await createCustomerPost({\r\n          content: content.trim(),\r\n          image_url: null, // Create post first without image\r\n          mentioned_business_ids: [],\r\n        });\r\n\r\n        if (createResult.success && createResult.data) {\r\n          const postData = createResult.data as { id: string; created_at: string };\r\n\r\n          // Upload the image using the post ID\r\n          const supabase = createClient();\r\n          const { data: { user } } = await supabase.auth.getUser();\r\n\r\n          if (user) {\r\n            const fileExt = selectedFile.name.split('.').pop();\r\n            const fileName = `${user.id}/${postData.id}_${Date.now()}.${fileExt}`;\r\n\r\n            const { data, error } = await supabase.storage\r\n              .from('customer-posts')\r\n              .upload(fileName, selectedFile);\r\n\r\n            if (!error && data) {\r\n              const { data: { publicUrl } } = supabase.storage\r\n                .from('customer-posts')\r\n                .getPublicUrl(data.path);\r\n\r\n              // Update post with image URL\r\n              const updateResult = await updateCustomerPost(postData.id, {\r\n                content: content.trim(),\r\n                image_url: publicUrl,\r\n                mentioned_business_ids: [],\r\n              });\r\n              result = updateResult;\r\n            } else {\r\n              result = createResult; // Post created but image upload failed\r\n            }\r\n          } else {\r\n            result = createResult;\r\n          }\r\n        } else {\r\n          result = createResult;\r\n        }\r\n      } else {\r\n        // No file upload needed or URL provided\r\n        result = await createCustomerPost({\r\n          content: content.trim(),\r\n          image_url: imageUrl,\r\n          mentioned_business_ids: [],\r\n        });\r\n      }\r\n\r\n      if (result.success) {\r\n        toast.success('Post created successfully!');\r\n        setContent(\"\");\r\n        setImageUrl(null);\r\n        handleImageRemove(); // Clear preview\r\n        setIsExpanded(false);\r\n        onPostCreated?.();\r\n      } else {\r\n        toast.error(result.error || 'Failed to create post');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating post:', error);\r\n      toast.error('Failed to create post');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      layout\r\n      className=\"bg-white dark:bg-black overflow-hidden md:border md:border-gray-200 md:dark:border-gray-700 md:rounded-xl md:shadow-sm\"\r\n    >\r\n      {/* Collapsed State */}\r\n      <AnimatePresence mode=\"wait\">\r\n        {!isExpanded && (\r\n          <motion.div\r\n            key=\"collapsed\"\r\n            initial={{ opacity: 0, height: 0 }}\r\n            animate={{ opacity: 1, height: \"auto\" }}\r\n            exit={{ opacity: 0, height: 0 }}\r\n            transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n            className=\"p-4\"\r\n          >\r\n          <div \r\n            className=\"flex items-center gap-3 cursor-pointer\"\r\n            onClick={handleExpand}\r\n          >\r\n            <Avatar className=\"w-10 h-10 ring-2 ring-[#D4AF37]/30\">\r\n              <AvatarImage src={customerAvatar || undefined} alt={customerDisplayName} />\r\n              <AvatarFallback className=\"bg-[#D4AF37] text-white text-sm font-medium\">\r\n                {customerDisplayName.charAt(0).toUpperCase()}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            \r\n            <div className=\"flex-1 bg-gray-50 dark:bg-gray-800 rounded-full px-4 py-3 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\">\r\n              What&apos;s on your mind, {customerDisplayName}?\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* Expanded State */}\r\n      <AnimatePresence mode=\"wait\">\r\n        {isExpanded && (\r\n          <motion.div\r\n            key=\"expanded\"\r\n            initial={{ opacity: 0, height: 0 }}\r\n            animate={{ opacity: 1, height: \"auto\" }}\r\n            exit={{ opacity: 0, height: 0 }}\r\n            transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n            className=\"overflow-hidden\"\r\n          >\r\n            {/* Header */}\r\n            <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <Avatar className=\"w-10 h-10 ring-2 ring-[#D4AF37]/30\">\r\n                  <AvatarImage src={customerAvatar || undefined} alt={customerDisplayName} />\r\n                  <AvatarFallback className=\"bg-[#D4AF37] text-white text-sm font-medium\">\r\n                    {customerDisplayName.charAt(0).toUpperCase()}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <div>\r\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100 text-sm\">\r\n                    {customerDisplayName}\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                    Public post\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              \r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={handleCollapse}\r\n                className=\"h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n              >\r\n                <X className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n\r\n            {/* Content Area */}\r\n            <div className=\"p-4\">\r\n              {/* Posting from location */}\r\n              <div className=\"mb-3\">\r\n                <LocationDisplay />\r\n              </div>\r\n\r\n              <textarea\r\n                ref={textareaRef}\r\n                value={content}\r\n                onChange={(e) => handleContentChange(e.target.value)}\r\n                placeholder={`What's on your mind, ${customerDisplayName}?`}\r\n                className=\"w-full resize-none border-none outline-none text-lg placeholder-gray-500 dark:placeholder-gray-400 bg-transparent text-gray-900 dark:text-gray-100 min-h-[80px]\"\r\n                style={{ maxHeight: '300px' }}\r\n              />\r\n\r\n              {/* Character Count */}\r\n              {charCount > 0 && (\r\n                <div className={`text-xs mt-2 text-right ${\r\n                  isOverLimit ? 'text-red-500' : 'text-gray-400'\r\n                }`}>\r\n                  {charCount}/{MAX_CHARS}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Image Preview */}\r\n            <AnimatePresence>\r\n              {(previewUrl || detectedImageUrl) && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, height: 0 }}\r\n                  animate={{ opacity: 1, height: \"auto\" }}\r\n                  exit={{ opacity: 0, height: 0 }}\r\n                  className=\"px-4 pb-4\"\r\n                >\r\n                  <div className=\"relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700\">\r\n                    <Image\r\n                      src={previewUrl || detectedImageUrl || ''}\r\n                      alt=\"Post image preview\"\r\n                      width={500}\r\n                      height={300}\r\n                      className=\"w-full h-auto max-h-96 object-cover\"\r\n                      onError={handleImageRemove}\r\n                    />\r\n                    <button\r\n                      onClick={handleImageRemove}\r\n                      className=\"absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1.5 hover:bg-opacity-70 transition-all\"\r\n                    >\r\n                      <X className=\"h-4 w-4\" />\r\n                    </button>\r\n                    {detectedImageUrl && !previewUrl && (\r\n                      <div className=\"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded\">\r\n                        Auto-detected image\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </AnimatePresence>\r\n\r\n\r\n\r\n            {/* Action Bar */}\r\n            <div className=\"flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <input\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  onChange={handleFileSelect}\r\n                  className=\"hidden\"\r\n                  id=\"image-upload\"\r\n                />\r\n                <label htmlFor=\"image-upload\">\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    className=\"text-gray-500 hover:text-[#D4AF37] dark:text-gray-400 dark:hover:text-[#D4AF37] h-9 px-3\"\r\n                    asChild\r\n                  >\r\n                    <span>\r\n                      <ImageIcon className=\"h-5 w-5 mr-1\" />\r\n                      <span className=\"text-sm\">Photo</span>\r\n                    </span>\r\n                  </Button>\r\n                </label>\r\n\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 h-9 px-3\"\r\n                  disabled\r\n                >\r\n                  <MapPin className=\"h-5 w-5 mr-1\" />\r\n                  <span className=\"text-sm\">Location</span>\r\n                </Button>\r\n              </div>\r\n\r\n              <Button\r\n                onClick={handleSubmit}\r\n                disabled={isSubmitting || (!content.trim() && !previewUrl && !imageUrl && !detectedImageUrl) || isOverLimit}\r\n                className=\"bg-[#D4AF37] hover:bg-[#B8941F] text-white px-6 py-2 rounded-full font-medium disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                    Posting...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Send className=\"h-4 w-4 mr-2\" />\r\n                    Post\r\n                  </>\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;;;;AAyBe,SAAS,uBAAuB,EAC7C,YAAY,EACZ,aAAa,EACe;;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,kBAAkB;IAClB,MAAM,YAAY;IAClB,MAAM,YAAY,QAAQ,MAAM;IAChC,MAAM,cAAc,YAAY;IAEhC,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM;yEAAuB;oBAC3B,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;oBAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;oBAEtD,IAAI,MAAM;wBACR,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,qBACL,MAAM,CAAC,oBACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBAET,IAAI,SAAS;4BACX,uBAAuB,QAAQ,IAAI,IAAI,gBAAgB;4BACvD,kBAAkB,QAAQ,UAAU;wBACtC;oBACF;gBACF;;YAEA;QACF;2CAAG;QAAC;KAAa;IAEjB,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;gBACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5E;QACF;2CAAG;QAAC;KAAQ;IAEZ,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,cAAc,YAAY,OAAO,EAAE;gBACrC,YAAY,OAAO,CAAC,KAAK;YAC3B;QACF;2CAAG;QAAC;KAAW;IAEf,MAAM,eAAe;QACnB,cAAc;IAChB;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,UAAU;YAChC,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,qBAAqB;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,iCAAiC;QACjC,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,oCAAoC;QACpC,gBAAgB;QAChB,MAAM,UAAU,IAAI,eAAe,CAAC;QACpC,cAAc;QACd,YAAY,OAAO,yBAAyB;IAC9C;IAEA,MAAM,oBAAoB;QACxB,gBAAgB;QAChB,IAAI,YAAY;YACd,IAAI,eAAe,CAAC;QACtB;QACA,cAAc;QACd,YAAY;QACZ,oBAAoB;IACtB;IAEA,2CAA2C;IAC3C,MAAM,iBAAiB,CAAC;QACtB,MAAM,gBAAgB;QACtB,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,2CAA2C;IAC3C,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QAEX,8BAA8B;QAC9B,MAAM,cAAc,eAAe;QACnC,IAAI,eAAe,gBAAgB,kBAAkB;YACnD,oBAAoB;YACpB,8CAA8C;YAC9C,IAAI,CAAC,cAAc;gBACjB,YAAY;YACd;QACF,OAAO,IAAI,CAAC,eAAe,kBAAkB;YAC3C,oBAAoB;YACpB,yEAAyE;YACzE,IAAI,CAAC,cAAc;gBACjB,YAAY;YACd;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEtD,IAAI,CAAC,MAAM;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,wCACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,OAAO;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,OAAO,IAAI;YAChD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,SAAS,QAAQ,CAAC,SAAS,SAAS,CAAC,SAAS,UAAU;YAChF,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;YACZ,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,cAAc,CAAC,kBAAkB;YACvD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,aAAa;YACf,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,kBAAkB,MAAM;YAC9B,IAAI,CAAC,iBAAiB;gBACpB,gBAAgB;gBAChB;YACF;YAEA,IAAI;YAEJ,IAAI,gBAAgB,CAAC,UAAU;gBAC7B,mCAAmC;gBACnC,MAAM,eAAe,MAAM,CAAA,GAAA,0IAAA,CAAA,qBAAkB,AAAD,EAAE;oBAC5C,SAAS,QAAQ,IAAI;oBACrB,WAAW;oBACX,wBAAwB,EAAE;gBAC5B;gBAEA,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE;oBAC7C,MAAM,WAAW,aAAa,IAAI;oBAElC,qCAAqC;oBACrC,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;oBAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;oBAEtD,IAAI,MAAM;wBACR,MAAM,UAAU,aAAa,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;wBAChD,MAAM,WAAW,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,SAAS;wBAErE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,kBACL,MAAM,CAAC,UAAU;wBAEpB,IAAI,CAAC,SAAS,MAAM;4BAClB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,SAAS,OAAO,CAC7C,IAAI,CAAC,kBACL,YAAY,CAAC,KAAK,IAAI;4BAEzB,6BAA6B;4BAC7B,MAAM,eAAe,MAAM,CAAA,GAAA,0IAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,EAAE,EAAE;gCACzD,SAAS,QAAQ,IAAI;gCACrB,WAAW;gCACX,wBAAwB,EAAE;4BAC5B;4BACA,SAAS;wBACX,OAAO;4BACL,SAAS,cAAc,uCAAuC;wBAChE;oBACF,OAAO;wBACL,SAAS;oBACX;gBACF,OAAO;oBACL,SAAS;gBACX;YACF,OAAO;gBACL,wCAAwC;gBACxC,SAAS,MAAM,CAAA,GAAA,0IAAA,CAAA,qBAAkB,AAAD,EAAE;oBAChC,SAAS,QAAQ,IAAI;oBACrB,WAAW;oBACX,wBAAwB,EAAE;gBAC5B;YACF;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,WAAW;gBACX,YAAY;gBACZ,qBAAqB,gBAAgB;gBACrC,cAAc;gBACd;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,MAAM;QACN,WAAU;;0BAGV,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,CAAC,4BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAU;8BAEZ,cAAA,6LAAC;wBACC,WAAU;wBACV,SAAS;;0CAET,6LAAC,8HAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,6LAAC,8HAAA,CAAA,cAAW;wCAAC,KAAK,kBAAkB;wCAAW,KAAK;;;;;;kDACpD,6LAAC,8HAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,oBAAoB,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;0CAI9C,6LAAC;gCAAI,WAAU;;oCAAwJ;oCAC1I;oCAAoB;;;;;;;;;;;;;mBAnB7C;;;;;;;;;;0BA2BV,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,8HAAA,CAAA,cAAW;oDAAC,KAAK,kBAAkB;oDAAW,KAAK;;;;;;8DACpD,6LAAC,8HAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,oBAAoB,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;sDAG9C,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV;;;;;;8DAEH,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;;;;;;;8CAM5D,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,4JAAA,CAAA,UAAe;;;;;;;;;;8CAGlB,6LAAC;oCACC,KAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oCACnD,aAAa,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,CAAC;oCAC3D,WAAU;oCACV,OAAO;wCAAE,WAAW;oCAAQ;;;;;;gCAI7B,YAAY,mBACX,6LAAC;oCAAI,WAAW,CAAC,wBAAwB,EACvC,cAAc,iBAAiB,iBAC/B;;wCACC;wCAAU;wCAAE;;;;;;;;;;;;;sCAMnB,6LAAC,4LAAA,CAAA,kBAAe;sCACb,CAAC,cAAc,gBAAgB,mBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,cAAc,oBAAoB;4CACvC,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,SAAS;;;;;;sDAEX,6LAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;wCAEd,oBAAoB,CAAC,4BACpB,6LAAC;4CAAI,WAAU;sDAAuF;;;;;;;;;;;;;;;;;;;;;;sCAYhH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;4CACV,IAAG;;;;;;sDAEL,6LAAC;4CAAM,SAAQ;sDACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,OAAO;0DAEP,cAAA,6LAAC;;sEACC,6LAAC,uMAAA,CAAA,QAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAKhC,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,QAAQ;;8DAER,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;8CAI9B,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,gBAAiB,CAAC,QAAQ,IAAI,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,oBAAqB;oCAChG,WAAU;8CAET,6BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;mBAjJrC;;;;;;;;;;;;;;;;AA4JhB;GAtcwB;;QAeP,qIAAA,CAAA,YAAS;;;KAfF", "debugId": null}}, {"offset": {"line": 6539, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/ModernCustomerFeedList.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useInView } from 'react-intersection-observer';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Loader2, AlertCircle } from 'lucide-react';\r\nimport { getUnifiedFeedPostsWithAuthors, UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\nimport { FeedFilterType } from '@/lib/types/posts';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\nimport UnifiedPostCard from './shared/UnifiedPostCard';\r\nimport PostCardSkeleton from './shared/PostCardSkeleton';\r\nimport ModernFeedHeader from './shared/ModernFeedHeader';\r\nimport ModernFeedContainer from './shared/ModernFeedContainer';\r\nimport SocialMediaPostCreator from './shared/SocialMediaPostCreator';\r\n\r\ninterface ModernCustomerFeedListProps {\r\n  initialPosts: UnifiedPost[];\r\n  initialTotalCount: number;\r\n  initialHasMore: boolean;\r\n  initialFilter?: FeedFilterType;\r\n  citySlug?: string;\r\n  stateSlug?: string;\r\n  localitySlug?: string;\r\n  pincode?: string;\r\n  userName?: string;\r\n}\r\n\r\nexport default function ModernCustomerFeedList({\r\n  initialPosts,\r\n  initialTotalCount,\r\n  initialHasMore,\r\n  initialFilter = 'smart',\r\n  citySlug,\r\n  stateSlug,\r\n  localitySlug,\r\n  pincode,\r\n  userName = 'Valued Customer'\r\n}: ModernCustomerFeedListProps) {\r\n  // State for posts and pagination\r\n  const [posts, setPosts] = useState<UnifiedPost[]>(initialPosts);\r\n  const [_totalCount, setTotalCount] = useState<number>(initialTotalCount);\r\n  const [hasMore, setHasMore] = useState<boolean>(initialHasMore);\r\n  const [page, setPage] = useState<number>(1);\r\n  const [isLoading, setIsLoading] = useState<boolean>(false);\r\n  const [filter, setFilter] = useState<FeedFilterType>(initialFilter);\r\n\r\n  // Intersection observer for infinite scroll\r\n  const { ref, inView } = useInView();\r\n\r\n  // Load more posts\r\n  const loadMorePosts = useCallback(async () => {\r\n    if (!hasMore || isLoading) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const nextPage = page + 1;\r\n      const result = await getUnifiedFeedPostsWithAuthors({\r\n        filter,\r\n        page: nextPage,\r\n        city_slug: citySlug,\r\n        state_slug: stateSlug,\r\n        locality_slug: localitySlug,\r\n        pincode: pincode,\r\n      });\r\n\r\n      if (result.success && result.data?.items) {\r\n        setPosts(prev => {\r\n          // Create a Set of existing post IDs for fast lookup\r\n          const existingIds = new Set(prev.map(post => post.id));\r\n          // Filter out any posts that already exist\r\n          const newPosts = result.data!.items.filter(post => !existingIds.has(post.id));\r\n          return [...prev, ...newPosts];\r\n        });\r\n        setHasMore(result.data!.hasMore || false);\r\n        setTotalCount(result.data!.totalCount || 0);\r\n        setPage(nextPage);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading more posts:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [hasMore, isLoading, page, filter, citySlug, stateSlug, localitySlug, pincode]);\r\n\r\n  // Load more posts when the user scrolls to the bottom\r\n  useEffect(() => {\r\n    if (inView && hasMore && !isLoading) {\r\n      loadMorePosts();\r\n    }\r\n  }, [inView, hasMore, isLoading, loadMorePosts]);\r\n\r\n  // Handle filter change\r\n  const handleFilterChange = async (newFilter: FeedFilterType) => {\r\n    if (newFilter === filter) return;\r\n\r\n    setIsLoading(true);\r\n    setFilter(newFilter);\r\n\r\n    // Clear existing posts immediately to show skeletons\r\n    setPosts([]);\r\n\r\n    try {\r\n      const result = await getUnifiedFeedPostsWithAuthors({\r\n        filter: newFilter,\r\n        page: 1,\r\n        city_slug: citySlug,\r\n        state_slug: stateSlug,\r\n        locality_slug: localitySlug,\r\n        pincode: pincode,\r\n      });\r\n\r\n      if (result.success && result.data) {\r\n        setPosts(result.data.items);\r\n        setHasMore(result.data.hasMore);\r\n        setTotalCount(result.data.totalCount);\r\n        setPage(1);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error changing filter:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle post creation success\r\n  const handlePostCreated = async () => {\r\n    // Refresh the feed\r\n    try {\r\n      const result = await getUnifiedFeedPostsWithAuthors({\r\n        filter,\r\n        page: 1,\r\n        city_slug: citySlug,\r\n        state_slug: stateSlug,\r\n        locality_slug: localitySlug,\r\n        pincode: pincode,\r\n      });\r\n\r\n      if (result.success && result.data?.items) {\r\n        // For refresh, we replace all posts so no deduplication needed\r\n        setPosts(result.data.items);\r\n        setHasMore(result.data.hasMore || false);\r\n        setTotalCount(result.data.totalCount || 0);\r\n        setPage(1);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error refreshing feed:', error);\r\n    }\r\n  };\r\n\r\n  // Handle post update\r\n  const handlePostUpdate = (postId: string, newContent: string) => {\r\n    setPosts(prevPosts =>\r\n      prevPosts.map(post =>\r\n        post.id === postId\r\n          ? { ...post, content: newContent }\r\n          : post\r\n      )\r\n    );\r\n  };\r\n\r\n  // Handle post deletion\r\n  const handlePostDelete = (postId: string) => {\r\n    setPosts(prevPosts => prevPosts.filter(post => post.id !== postId));\r\n    setTotalCount(prevCount => Math.max(0, prevCount - 1));\r\n  };\r\n\r\n  // Handle product update\r\n  const handleProductsUpdate = (postId: string, newProductIds: string[]) => {\r\n    setPosts(prevPosts =>\r\n      prevPosts.map(post =>\r\n        post.id === postId\r\n          ? { ...post, product_ids: newProductIds }\r\n          : post\r\n      )\r\n    );\r\n  };\r\n\r\n  // Get empty state message\r\n  const getEmptyStateMessage = () => {\r\n    switch (filter) {\r\n      case 'smart':\r\n        return 'No posts available in your smart feed. Try subscribing to businesses or check other filters.';\r\n      case 'subscribed':\r\n        return 'Subscribe to businesses to see their posts here.';\r\n      case 'locality':\r\n        return 'No posts from businesses in your locality yet.';\r\n      case 'pincode':\r\n        return 'No posts from businesses in your pincode yet.';\r\n      case 'city':\r\n        return 'No posts from businesses in your city yet.';\r\n      case 'state':\r\n        return 'No posts from businesses in your state yet.';\r\n      default:\r\n        return 'No posts available at the moment.';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <ModernFeedContainer>\r\n      <ModernFeedHeader\r\n        activeFilter={filter}\r\n        onFilterChange={handleFilterChange}\r\n        isLoading={isLoading}\r\n      />\r\n\r\n      {/* Posts Content */}\r\n      <div className=\"max-w-2xl mx-auto space-y-6\">\r\n        {/* Customer Post Creation Card */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5, delay: 0.2 }}\r\n        >\r\n          <SocialMediaPostCreator\r\n            customerName={userName}\r\n            onPostCreated={handlePostCreated}\r\n          />\r\n        </motion.div>\r\n\r\n        <AnimatePresence mode=\"wait\">\r\n          {posts.length === 0 && !isLoading ? (\r\n            <motion.div\r\n              key=\"empty-state\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              <Alert className=\"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800\">\r\n                <AlertCircle className=\"h-4 w-4\" />\r\n                <AlertTitle>No posts found</AlertTitle>\r\n                <AlertDescription>{getEmptyStateMessage()}</AlertDescription>\r\n              </Alert>\r\n            </motion.div>\r\n          ) : (\r\n            <motion.div\r\n              key=\"posts-list\"\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              exit={{ opacity: 0 }}\r\n              transition={{ duration: 0.3 }}\r\n              className=\"space-y-0\"\r\n            >\r\n              {/* Loading skeletons during filter change */}\r\n              {isLoading && posts.length === 0 && (\r\n                <>\r\n                  {Array.from({ length: 10 }).map((_, index) => (\r\n                    <PostCardSkeleton\r\n                      key={`skeleton-${index}`}\r\n                      index={index}\r\n                      showImage={Math.random() > 0.3}\r\n                      showProducts={Math.random() > 0.7}\r\n                    />\r\n                  ))}\r\n                </>\r\n              )}\r\n\r\n              {/* Posts */}\r\n              {posts.map((post, index) => (\r\n                <UnifiedPostCard\r\n                  key={post.id}\r\n                  post={post}\r\n                  index={index}\r\n                  onPostUpdate={handlePostUpdate}\r\n                  onPostDelete={handlePostDelete}\r\n                  onProductsUpdate={handleProductsUpdate}\r\n                />\r\n              ))}\r\n\r\n              {/* Infinite scroll trigger */}\r\n              {hasMore && (\r\n                <div ref={ref} className=\"flex justify-center items-center py-8\">\r\n                  {isLoading && (\r\n                    <div className=\"flex items-center gap-2 text-neutral-500\">\r\n                      <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n                      <span className=\"text-sm\">Loading more posts...</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Load more button (fallback) */}\r\n              {hasMore && !isLoading && (\r\n                <div className=\"flex justify-center mt-8 mb-4\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    onClick={loadMorePosts}\r\n                    disabled={isLoading}\r\n                    className=\"bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900\"\r\n                  >\r\n                    Load More Posts\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </ModernFeedContainer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;AA4Be,SAAS,uBAAuB,EAC7C,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,gBAAgB,OAAO,EACvB,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,OAAO,EACP,WAAW,iBAAiB,EACA;;IAC5B,iCAAiC;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAErD,4CAA4C;IAC5C,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD;IAEhC,kBAAkB;IAClB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YAChC,IAAI,CAAC,WAAW,WAAW;YAE3B,aAAa;YAEb,IAAI;gBACF,MAAM,WAAW,OAAO;gBACxB,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,iCAA8B,AAAD,EAAE;oBAClD;oBACA,MAAM;oBACN,WAAW;oBACX,YAAY;oBACZ,eAAe;oBACf,SAAS;gBACX;gBAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,OAAO;oBACxC;6EAAS,CAAA;4BACP,oDAAoD;4BACpD,MAAM,cAAc,IAAI,IAAI,KAAK,GAAG;qFAAC,CAAA,OAAQ,KAAK,EAAE;;4BACpD,0CAA0C;4BAC1C,MAAM,WAAW,OAAO,IAAI,CAAE,KAAK,CAAC,MAAM;8FAAC,CAAA,OAAQ,CAAC,YAAY,GAAG,CAAC,KAAK,EAAE;;4BAC3E,OAAO;mCAAI;mCAAS;6BAAS;wBAC/B;;oBACA,WAAW,OAAO,IAAI,CAAE,OAAO,IAAI;oBACnC,cAAc,OAAO,IAAI,CAAE,UAAU,IAAI;oBACzC,QAAQ;gBACV;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C,SAAU;gBACR,aAAa;YACf;QACF;4DAAG;QAAC;QAAS;QAAW;QAAM;QAAQ;QAAU;QAAW;QAAc;KAAQ;IAEjF,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,UAAU,WAAW,CAAC,WAAW;gBACnC;YACF;QACF;2CAAG;QAAC;QAAQ;QAAS;QAAW;KAAc;IAE9C,uBAAuB;IACvB,MAAM,qBAAqB,OAAO;QAChC,IAAI,cAAc,QAAQ;QAE1B,aAAa;QACb,UAAU;QAEV,qDAAqD;QACrD,SAAS,EAAE;QAEX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,iCAA8B,AAAD,EAAE;gBAClD,QAAQ;gBACR,MAAM;gBACN,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;YACX;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,SAAS,OAAO,IAAI,CAAC,KAAK;gBAC1B,WAAW,OAAO,IAAI,CAAC,OAAO;gBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;gBACpC,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB,mBAAmB;QACnB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,iCAA8B,AAAD,EAAE;gBAClD;gBACA,MAAM;gBACN,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;YACX;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,OAAO;gBACxC,+DAA+D;gBAC/D,SAAS,OAAO,IAAI,CAAC,KAAK;gBAC1B,WAAW,OAAO,IAAI,CAAC,OAAO,IAAI;gBAClC,cAAc,OAAO,IAAI,CAAC,UAAU,IAAI;gBACxC,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC,QAAgB;QACxC,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,OACZ,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAW,IAC/B;IAGV;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,CAAC;QACxB,SAAS,CAAA,YAAa,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC3D,cAAc,CAAA,YAAa,KAAK,GAAG,CAAC,GAAG,YAAY;IACrD;IAEA,wBAAwB;IACxB,MAAM,uBAAuB,CAAC,QAAgB;QAC5C,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,OACZ,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE,aAAa;gBAAc,IACtC;IAGV;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,uJAAA,CAAA,UAAmB;;0BAClB,6LAAC,oJAAA,CAAA,UAAgB;gBACf,cAAc;gBACd,gBAAgB;gBAChB,WAAW;;;;;;0BAIb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,6LAAC,0JAAA,CAAA,UAAsB;4BACrB,cAAc;4BACd,eAAe;;;;;;;;;;;kCAInB,6LAAC,4LAAA,CAAA,kBAAe;wBAAC,MAAK;kCACnB,MAAM,MAAM,KAAK,KAAK,CAAC,0BACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC,6HAAA,CAAA,aAAU;kDAAC;;;;;;kDACZ,6LAAC,6HAAA,CAAA,mBAAgB;kDAAE;;;;;;;;;;;;2BATjB;;;;iDAaN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;gCAGT,aAAa,MAAM,MAAM,KAAK,mBAC7B;8CACG,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,6LAAC,oJAAA,CAAA,UAAgB;4CAEf,OAAO;4CACP,WAAW,KAAK,MAAM,KAAK;4CAC3B,cAAc,KAAK,MAAM,KAAK;2CAHzB,CAAC,SAAS,EAAE,OAAO;;;;;;gCAU/B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,mJAAA,CAAA,UAAe;wCAEd,MAAM;wCACN,OAAO;wCACP,cAAc;wCACd,cAAc;wCACd,kBAAkB;uCALb,KAAK,EAAE;;;;;gCAUf,yBACC,6LAAC;oCAAI,KAAK;oCAAK,WAAU;8CACtB,2BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;gCAOjC,WAAW,CAAC,2BACX,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;2BArDD;;;;;;;;;;;;;;;;;;;;;;AAgElB;GAlRwB;;QAoBE,sKAAA,CAAA,YAAS;;;KApBX", "debugId": null}}]}