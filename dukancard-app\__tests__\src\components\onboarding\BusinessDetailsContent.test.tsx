import React from 'react';
import { render, screen } from '@testing-library/react-native';
import BusinessDetailsContent from '@/src/components/onboarding/BusinessDetailsContent';
import { useForm } from 'react-hook-form';
import { useTheme } from '@/src/hooks/useTheme';
import { useOnboarding } from '@/src/contexts/OnboardingContext';

// Mock useTheme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      textPrimary: '#000',
      textSecondary: '#888',
    },
    spacing: { md: 16, lg: 24, xs: 4 },
    typography: {
      fontSize: { xxl: 24, base: 16 },
      fontWeight: { '700': 'bold' },
      lineHeight: { normal: 1.5 },
    },
  }),
}));

// Mock useOnboarding hook
jest.mock('@/src/contexts/OnboardingContext', () => ({
  useOnboarding: () => ({
    slugValidation: { isValid: true, error: null },
    validateSlug: jest.fn(),
  }),
}));

describe('BusinessDetailsContent', () => {
  interface BusinessDetailsFormData {
    businessName: string;
    email: string;
    businessSlug: string;
  }

  const TestComponent = ({ errors = {} }: { errors?: any }) => {
    const { control } = useForm<BusinessDetailsFormData>();
    return <BusinessDetailsContent control={control} errors={errors} />;
  };

  it('renders correctly with all form fields', () => {
    render(<TestComponent />);
    expect(screen.getByText('Business Details')).toBeTruthy();
    expect(screen.getByPlaceholderText('My Awesome Business')).toBeTruthy();
    expect(screen.getByPlaceholderText('my-awesome-business')).toBeTruthy();
    expect(screen.getByPlaceholderText('<EMAIL>')).toBeTruthy();
  });

  it('displays slug validation error', () => {
    (useOnboarding as jest.Mock).mockReturnValue({
      slugValidation: { isValid: false, error: 'Slug is already taken' },
      validateSlug: jest.fn(),
    });
    render(<TestComponent />);
    expect(screen.getByText('Slug is already taken')).toBeTruthy();
  });

  it('displays form field error', () => {
    const mockErrors = {
      businessName: { type: 'required', message: 'Business name is required' },
    };
    render(<TestComponent errors={mockErrors} />);
    expect(screen.getByText('Business name is required')).toBeTruthy();
  });
});
