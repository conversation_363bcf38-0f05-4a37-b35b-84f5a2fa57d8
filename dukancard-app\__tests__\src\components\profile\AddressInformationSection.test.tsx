import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import AddressInformationSection from '../../../../src/components/profile/AddressInformationSection';
import { useForm, FormProvider } from 'react-hook-form';
import { Alert } from 'react-native';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/src/components/ui/LocationPicker', () => ({
  LocationPicker: jest.fn(({ onLocationDetected, onAddressDetected, onError, disabled }) => (
    <TouchableOpacity
      testID="mock-location-picker"
      onPress={() => onAddressDetected({ pincode: '111111', city: 'DetectedCity', state: 'DetectedState', locality: 'DetectedLocality' })}
      disabled={disabled}
    >
      <Text>Mock Location Picker</Text>
    </TouchableOpacity>
  )),
}));

jest.mock('@/src/components/pickers/LocalityBottomSheetPicker', () => {
  const React = require('react');
  const MockLocalityBottomSheetPicker = React.forwardRef(({ onSelect, localities }, ref) => {
    React.useImperativeHandle(ref, () => ({
      present: jest.fn(),
    }));
    return <></>;
  });
  return MockLocalityBottomSheetPicker;
});

jest.spyOn(Alert, 'alert');

const mockToast = {
  error: jest.fn(),
  warning: jest.fn(),
};

const TestWrapper = ({ children, defaultValues = {} }) => {
  const methods = useForm({
    defaultValues: {
      pincode: '',
      city: '',
      state: '',
      locality: '',
      address: '',
      ...defaultValues,
    },
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe('AddressInformationSection', () => {
  const commonProps = {
    textColor: '#000',
    borderColor: '#ccc',
    styles: {
      section: {},
      sectionTitle: {},
      inputGroup: {},
      halfWidth: {},
      label: {},
      input: {},
      inputError: {},
      errorText: {},
      row: {},
    },
    isDark: false,
    isPincodeLoading: false,
    availableLocalities: [],
    locationPermission: { granted: true },
    hasGpsCoordinates: false,
    handlePincodeInputChange: jest.fn(),
    handleLocationDetected: jest.fn(),
    handleAddressDetected: jest.fn(),
    handleLocalitySelect: jest.fn(),
    toast: mockToast,
    trigger: jest.fn(() => Promise.resolve(true)),
    localityPickerRef: React.createRef(),
  };

  it('renders correctly', () => {
    const { getByText, getByPlaceholderText } = render(
      <TestWrapper>
        <AddressInformationSection {...commonProps} />
      </TestWrapper>
    );

    expect(getByText('Address Information')).toBeTruthy();
    expect(getByPlaceholderText('Enter 6-digit pincode')).toBeTruthy();
    expect(getByPlaceholderText('Auto-filled from pincode')).toBeTruthy();
    expect(getByPlaceholderText('Select locality')).toBeTruthy();
    expect(getByPlaceholderText('Enter full address')).toBeTruthy();
  });

  it('calls handlePincodeInputChange on pincode input change', () => {
    const { getByPlaceholderText } = render(
      <TestWrapper>
        <AddressInformationSection {...commonProps} />
      </TestWrapper>
    );
    const pincodeInput = getByPlaceholderText('Enter 6-digit pincode');

    fireEvent.changeText(pincodeInput, '123456');
    expect(commonProps.handlePincodeInputChange).toHaveBeenCalledWith('123456');
  });

  it('calls handleAddressDetected when LocationPicker detects an address', () => {
    const { getByTestId } = render(
      <TestWrapper>
        <AddressInformationSection {...commonProps} />
      </TestWrapper>
    );
    const locationPicker = getByTestId('mock-location-picker');

    fireEvent.press(locationPicker);
    expect(commonProps.handleAddressDetected).toHaveBeenCalledWith({
      pincode: '111111',
      city: 'DetectedCity',
      state: 'DetectedState',
      locality: 'DetectedLocality',
    });
  });

  it('presents LocalityBottomSheetPicker when locality is clicked and localities are available', () => {
    const { getByText } = render(
      <TestWrapper>
        <AddressInformationSection {...commonProps} availableLocalities={['Locality1']} />
      </TestWrapper>
    );
    const localityInput = getByText('Select locality');

    fireEvent.press(localityInput);
    expect(commonProps.localityPickerRef.current?.present).toHaveBeenCalled();
  });

  it('shows warning toast when locality is clicked but no localities are available', () => {
    const { getByText } = render(
      <TestWrapper>
        <AddressInformationSection {...commonProps} availableLocalities={[]} />
      </TestWrapper>
    );
    const localityInput = getByText('Select locality');

    fireEvent.press(localityInput);
    expect(mockToast.warning).toHaveBeenCalledWith(
      'No Localities Available',
      'Please enter a valid pincode first to see available localities.'
    );
  });

  it('displays location permission message when not granted and no GPS coordinates', () => {
    const { getByText } = render(
      <TestWrapper>
        <AddressInformationSection
          {...commonProps}
          locationPermission={{ granted: false }}
          hasGpsCoordinates={false}
        />
      </TestWrapper>
    );

    expect(getByText('Location permission is required')).toBeTruthy();
  });

  it('does not display location permission message when granted', () => {
    const { queryByText } = render(
      <TestWrapper>
        <AddressInformationSection
          {...commonProps}
          locationPermission={{ granted: true }}
          hasGpsCoordinates={false}
        />
      </TestWrapper>
    );

    expect(queryByText('Location permission is required')).toBeNull();
  });

  it('does not display location permission message when hasGpsCoordinates is true', () => {
    const { queryByText } = render(
      <TestWrapper>
        <AddressInformationSection
          {...commonProps}
          locationPermission={{ granted: false }}
          hasGpsCoordinates={true}
        />
      </TestWrapper>
    );

    expect(queryByText('Location permission is required')).toBeNull();
  });
});
