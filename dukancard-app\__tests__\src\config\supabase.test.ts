import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SUPABASE_CONFIG } from '@/src/config/publicKeys';

// Mock @supabase/supabase-js
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    // Mock any methods you expect to be called on the client
    auth: {
      getSession: jest.fn(),
      setSession: jest.fn(),
      onAuthStateChange: jest.fn(),
    },
  })),
}));

// Mock @react-native-async-storage/async-storage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('Supabase Client Configuration', () => {
  it('should create a Supabase client with correct configuration', () => {
    // Import the supabase client after mocks are set up
    const { supabase } = require('@/src/config/supabase');

    expect(createClient).toHaveBeenCalledWith(
      SUPABASE_CONFIG.url,
      SUPABASE_CONFIG.anonKey,
      {
        auth: {
          autoRefreshToken: true,
          persistSession: true,
          storage: AsyncStorage,
          detectSessionInUrl: false,
        },
        global: {
          headers: {
            'X-Client-Info': 'dukancard-mobile-client',
          },
        },
      }
    );
  });

  it('should export the created supabase client as default', () => {
    const defaultExport = require('@/src/config/supabase').default;
    const { supabase } = require('@/src/config/supabase');
    expect(defaultExport).toBe(supabase);
  });
});