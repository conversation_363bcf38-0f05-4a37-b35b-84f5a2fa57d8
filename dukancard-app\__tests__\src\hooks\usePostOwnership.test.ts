import { renderHook, act, waitFor } from '@testing-library/react-hooks';
import { usePostOwnership, checkPostOwnership } from '@/src/hooks/usePostOwnership';
import { supabase } from '@/lib/supabase';

// Mock external dependencies
jest.mock('@/lib/supabase');

describe('usePostOwnership', () => {
  const mockUserId = 'user123';
  const mockBusinessId = 'biz456';
  const mockCustomerId = 'cust789';

  beforeEach(() => {
    jest.clearAllMocks();
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: { user: { id: mockUserId } },
      error: null,
    });
  });

  it('should return initial loading state', () => {
    const { result } = renderHook(() => usePostOwnership({
      postSource: 'business',
      postBusinessId: mockBusinessId,
    }));
    expect(result.current.isLoading).toBe(true);
    expect(result.current.isOwner).toBe(false);
    expect(result.current.currentUserId).toBeNull();
  });

  it('should identify user as owner for business post if IDs match', async () => {
    const { result } = renderHook(() => usePostOwnership({
      postSource: 'business',
      postBusinessId: mockUserId,
    }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isOwner).toBe(true);
      expect(result.current.currentUserId).toBe(mockUserId);
    });
  });

  it('should identify user as owner for customer post if IDs match', async () => {
    const { result } = renderHook(() => usePostOwnership({
      postSource: 'customer',
      postCustomerId: mockUserId,
    }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isOwner).toBe(true);
      expect(result.current.currentUserId).toBe(mockUserId);
    });
  });

  it('should identify user as not owner if IDs do not match for business post', async () => {
    const { result } = renderHook(() => usePostOwnership({
      postSource: 'business',
      postBusinessId: mockBusinessId,
    }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isOwner).toBe(false);
      expect(result.current.currentUserId).toBe(mockUserId);
    });
  });

  it('should identify user as not owner if IDs do not match for customer post', async () => {
    const { result } = renderHook(() => usePostOwnership({
      postSource: 'customer',
      postCustomerId: mockCustomerId,
    }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isOwner).toBe(false);
      expect(result.current.currentUserId).toBe(mockUserId);
    });
  });

  it('should handle no authenticated user', async () => {
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: { user: null },
      error: null,
    });

    const { result } = renderHook(() => usePostOwnership({
      postSource: 'business',
      postBusinessId: mockBusinessId,
    }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isOwner).toBe(false);
      expect(result.current.currentUserId).toBeNull();
    });
  });

  it('should handle error during user fetch', async () => {
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: { user: null },
      error: new Error('Fetch error'),
    });

    const { result } = renderHook(() => usePostOwnership({
      postSource: 'business',
      postBusinessId: mockBusinessId,
    }));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isOwner).toBe(false);
      expect(result.current.currentUserId).toBeNull();
    });
  });
});

describe('checkPostOwnership', () => {
  it('should return true for business post if currentUserId matches postBusinessId', () => {
    expect(checkPostOwnership('user1', 'user1', undefined, 'business')).toBe(true);
  });

  it('should return false for business post if currentUserId does not match postBusinessId', () => {
    expect(checkPostOwnership('user1', 'user2', undefined, 'business')).toBe(false);
  });

  it('should return true for customer post if currentUserId matches postCustomerId', () => {
    expect(checkPostOwnership('user1', undefined, 'user1', 'customer')).toBe(true);
  });

  it('should return false for customer post if currentUserId does not match postCustomerId', () => {
    expect(checkPostOwnership('user1', undefined, 'user2', 'customer')).toBe(false);
  });

  it('should return false if currentUserId is null', () => {
    expect(checkPostOwnership(null, 'user1', undefined, 'business')).toBe(false);
  });

  it('should return false if postBusinessId is undefined for business post', () => {
    expect(checkPostOwnership('user1', undefined, undefined, 'business')).toBe(false);
  });

  it('should return false if postCustomerId is undefined for customer post', () => {
    expect(checkPostOwnership('user1', undefined, undefined, 'customer')).toBe(false);
  });
});