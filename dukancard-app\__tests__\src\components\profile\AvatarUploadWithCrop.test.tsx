import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { AvatarUploadWithCrop } from '../../../../src/components/profile/AvatarUploadWithCrop';
import { Alert } from 'react-native';

// Mock necessary modules
jest.mock('expo-image', () => ({
  Image: 'Image',
}));

jest.mock('lucide-react-native', () => ({
  Camera: 'Camera',
  User: 'User',
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/src/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: 'test-user-id' } }),
}));

jest.mock('@/backend/supabase/services/storage/avatarUploadService', () => ({
  openCameraForAvatar: jest.fn(),
  openGalleryForAvatar: jest.fn(),
}));

jest.mock('@/src/components/pickers/ImagePickerBottomSheet', () => {
  const React = require('react');
  const MockImagePickerBottomSheet = React.forwardRef(({ onCameraPress, onGalleryPress }, ref) => {
    React.useImperativeHandle(ref, () => ({
      present: jest.fn(),
    }));
    return (
      <>
        <button onClick={onCameraPress} data-testid="camera-option">Camera</button>
        <button onClick={onGalleryPress} data-testid="gallery-option">Gallery</button>
      </>
    );
  });
  return MockImagePickerBottomSheet;
});

jest.spyOn(Alert, 'alert');

describe('AvatarUploadWithCrop', () => {
  const mockOnImageSelectForCrop = jest.fn();
  const mockImagePickerRef = React.createRef();

  beforeEach(() => {
    jest.clearAllMocks();
    (mockImagePickerRef.current as any) = { present: jest.fn() };
    (require('@/backend/supabase/services/storage/avatarUploadService').openCameraForAvatar as jest.Mock).mockResolvedValue({
      canceled: true,
    });
    (require('@/backend/supabase/services/storage/avatarUploadService').openGalleryForAvatar as jest.Mock).mockResolvedValue({
      canceled: true,
    });
  });

  it('renders with initial avatar URL', () => {
    const { getByTestId } = render(
      <AvatarUploadWithCrop
        initialAvatarUrl="https://example.com/avatar.png"
        onImageSelectForCrop={mockOnImageSelectForCrop}
        imagePickerRef={mockImagePickerRef}
      />
    );
    expect(getByTestId('avatar-image')).toBeTruthy();
  });

  it('renders with placeholder when no initial avatar URL', () => {
    const { getByTestId } = render(
      <AvatarUploadWithCrop
        userName="John Doe"
        onImageSelectForCrop={mockOnImageSelectForCrop}
        imagePickerRef={mockImagePickerRef}
      />
    );
    expect(getByTestId('avatar-placeholder')).toBeTruthy();
  });

  it('calls imagePickerRef.current?.present() on avatar press', () => {
    const { getByTestId } = render(
      <AvatarUploadWithCrop
        onImageSelectForCrop={mockOnImageSelectForCrop}
        imagePickerRef={mockImagePickerRef}
      />
    );
    fireEvent.press(getByTestId('avatar-container'));
    expect(mockImagePickerRef.current?.present).toHaveBeenCalled();
  });

  it('handles camera image selection and calls onImageSelectForCrop', async () => {
    (require('@/backend/supabase/services/storage/avatarUploadService').openCameraForAvatar as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/camera-image.jpg', fileSize: 100000 }],
    });

    const { getByTestId } = render(
      <AvatarUploadWithCrop
        onImageSelectForCrop={mockOnImageSelectForCrop}
        imagePickerRef={mockImagePickerRef}
      />
    );

    fireEvent.press(getByTestId('camera-option'));

    await waitFor(() => {
      expect(mockOnImageSelectForCrop).toHaveBeenCalledWith('file://mock/path/to/camera-image.jpg');
    });
  });

  it('handles gallery image selection and calls onImageSelectForCrop', async () => {
    (require('@/backend/supabase/services/storage/avatarUploadService').openGalleryForAvatar as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/gallery-image.jpg', fileSize: 100000 }],
    });

    const { getByTestId } = render(
      <AvatarUploadWithCrop
        onImageSelectForCrop={mockOnImageSelectForCrop}
        imagePickerRef={mockImagePickerRef}
      />
    );

    fireEvent.press(getByTestId('gallery-option'));

    await waitFor(() => {
      expect(mockOnImageSelectForCrop).toHaveBeenCalledWith('file://mock/path/to/gallery-image.jpg');
    });
  });

  it('shows alert for large file size from camera', async () => {
    (require('@/backend/supabase/services/storage/avatarUploadService').openCameraForAvatar as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/large-image.jpg', fileSize: 16 * 1024 * 1024 }],
    });

    const { getByTestId } = render(
      <AvatarUploadWithCrop
        onImageSelectForCrop={mockOnImageSelectForCrop}
        imagePickerRef={mockImagePickerRef}
      />
    );

    fireEvent.press(getByTestId('camera-option'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'File size must be less than 15MB.');
      expect(mockOnImageSelectForCrop).not.toHaveBeenCalled();
    });
  });

  it('shows alert for large file size from gallery', async () => {
    (require('@/backend/supabase/services/storage/avatarUploadService').openGalleryForAvatar as jest.Mock).mockResolvedValue({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/large-image.jpg', fileSize: 16 * 1024 * 1024 }],
    });

    const { getByTestId } = render(
      <AvatarUploadWithCrop
        onImageSelectForCrop={mockOnImageSelectForCrop}
        imagePickerRef={mockImagePickerRef}
      />
    );

    fireEvent.press(getByTestId('gallery-option'));

    await waitFor(() => {
      expect(Alert.alert).toHaveBeenCalledWith('Error', 'File size must be less than 15MB.');
      expect(mockOnImageSelectForCrop).not.toHaveBeenCalled();
    });
  });

  it('displays selecting state when image selection is in progress', async () => {
    (require('@/backend/supabase/services/storage/avatarUploadService').openCameraForAvatar as jest.Mock).mockImplementationOnce(() => new Promise(resolve => setTimeout(() => resolve({
      canceled: false,
      assets: [{ uri: 'file://mock/path/to/image.jpg', fileSize: 100000 }],
    }), 100)));

    const { getByText, getByTestId } = render(
      <AvatarUploadWithCrop
        onImageSelectForCrop={mockOnImageSelectForCrop}
        imagePickerRef={mockImagePickerRef}
      />
    );

    fireEvent.press(getByTestId('camera-option'));

    await waitFor(() => {
      expect(getByText('Selecting...')).toBeTruthy();
      expect(getByTestId('activity-indicator')).toBeTruthy();
    });

    await waitFor(() => {
      expect(getByText('Tap to change picture')).toBeTruthy();
    }, { timeout: 200 });
  });
});
