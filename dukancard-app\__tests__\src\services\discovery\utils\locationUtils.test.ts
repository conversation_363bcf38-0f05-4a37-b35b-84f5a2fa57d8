import { getPincodeDetails } from '@/src/services/discovery/utils/locationUtils';
import { supabase } from '@/src/config/supabase';

// Mock external dependencies
jest.mock('@/src/config/supabase');

describe('getPincodeDetails', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock supabase.from chainable methods
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      // Default successful response
      then: jest.fn((resolve) => resolve({
        data: [
          { OfficeName: 'Locality A', DivisionName: 'City A', StateName: 'State A' },
          { OfficeName: 'Locality B', DivisionName: 'City A', StateName: 'State A' },
        ],
        error: null,
      })),
    });
  });

  it('should return pincode details for a valid pincode', async () => {
    const result = await getPincodeDetails('123456');
    expect(result.data).toEqual({
      city: 'City A',
      state: 'State A',
      localities: ['Locality A', 'Locality B'],
    });
    expect(result.city).toBe('City A');
    expect(result.state).toBe('State A');
    expect(result.localities).toEqual(['Locality A', 'Locality B']);
    expect(supabase.from).toHaveBeenCalledWith('pincodes');
    expect(supabase.from('pincodes').select).toHaveBeenCalledWith('OfficeName, DivisionName, StateName');
    expect(supabase.from('pincodes').eq).toHaveBeenCalledWith('Pincode', '123456');
    expect(supabase.from('pincodes').order).toHaveBeenCalledWith('OfficeName');
  });

  it('should return error for invalid pincode format', async () => {
    const result = await getPincodeDetails('123');
    expect(result.error).toBe('Invalid Pincode format.');
    expect(result.data).toBeUndefined();
  });

  it('should return error if pincode is not found', async () => {
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockResolvedValue({
        data: [],
        error: null,
      }),
    });

    const result = await getPincodeDetails('999999');
    expect(result.error).toBe('Pincode not found.');
    expect(result.data).toBeUndefined();
  });

  it('should return error for database fetch error', async () => {
    (supabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockResolvedValue({
        data: null,
        error: { message: 'Database error' },
      }),
    });

    const result = await getPincodeDetails('123456');
    expect(result.error).toBe('Database error fetching pincode details.');
    expect(result.data).toBeUndefined();
  });

  it('should return error for unexpected exceptions', async () => {
    (supabase.from as jest.Mock).mockImplementation(() => {
      throw new Error('Unexpected error');
    });

    const result = await getPincodeDetails('123456');
    expect(result.error).toBe('An unexpected error occurred during pincode lookup.');
    expect(result.data).toBeUndefined();
  });
});