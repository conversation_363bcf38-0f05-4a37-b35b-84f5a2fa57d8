import { createProfileHeaderStyles } from '../../../../src/components/shared/ProfileHeader.styles';
import { StyleSheet } from 'react-native';

describe('createProfileHeaderStyles', () => {
  it('returns a StyleSheet object', () => {
    const styles = createProfileHeaderStyles('light');
    expect(typeof styles).toBe('object');
    expect(styles).toHaveProperty('profileHeaderContainer');
    expect(styles).toHaveProperty('avatarSection');
  });

  it('applies light theme styles correctly', () => {
    const styles = createProfileHeaderStyles('light');
    expect(styles.profileHeaderContainer.backgroundColor).toBe('#FFFFFF');
    expect(styles.profileName.color).toBeUndefined(); // Default text color
  });

  it('applies dark theme styles correctly', () => {
    const styles = createProfileHeaderStyles('dark');
    expect(styles.profileHeaderContainer.backgroundColor).toBe('#000000');
    expect(styles.profileName.color).toBeUndefined(); // Default text color
  });

  it('defines specific styles for largeAvatar', () => {
    const styles = createProfileHeaderStyles('light');
    expect(styles.largeAvatar.width).toBe(90);
    expect(styles.largeAvatar.height).toBe(90);
    expect(styles.largeAvatar.borderRadius).toBe(45);
    expect(styles.largeAvatar.borderColor).toBe('#D4AF37');
  });

  it('defines specific styles for statLabel', () => {
    const styles = createProfileHeaderStyles('light');
    expect(styles.statLabel.fontSize).toBe(13);
    expect(styles.statLabel.color).toBe('#666');
  });
});
