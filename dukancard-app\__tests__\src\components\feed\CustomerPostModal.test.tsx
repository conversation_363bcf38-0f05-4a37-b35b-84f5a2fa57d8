import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { CustomerPostModal } from '../../../src/components/feed/CustomerPostModal';
import { supabase } from '@/lib/supabase';
import { createCustomerPost, updateCustomerPost, deleteCustomerPost } from '@/lib/actions/customerPosts';
import { uploadCustomerPostImage } from '@/backend/supabase/services/storage/customerPostImageUploadService';
import * as ImagePicker from 'expo-image-picker';

// Mock all external dependencies
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'user123' } } })),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: { name: 'Test Customer', avatar_url: 'http://test.com/avatar.png' } })),
        })),
      })),
    })),
  },
}));

jest.mock('@/lib/actions/customerPosts', () => ({
  createCustomerPost: jest.fn(),
  updateCustomerPost: jest.fn(),
  deleteCustomerPost: jest.fn(),
}));

jest.mock('@/backend/supabase/services/storage/customerPostImageUploadService', () => ({
  uploadCustomerPostImage: jest.fn(),
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: jest.fn(() => 'light'),
}));

jest.mock('@/backend/supabase/services/storage/imageUploadService', () => ({
  openCamera: jest.fn(),
  openImageGallery: jest.fn(),
}));

describe('CustomerPostModal', () => {
  const defaultProps = {
    visible: true,
    onClose: jest.fn(),
    customerName: 'Test Customer',
    onPostCreated: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (createCustomerPost as jest.Mock).mockResolvedValue({ success: true, data: { id: 'newPost123', created_at: new Date().toISOString() } });
    (updateCustomerPost as jest.Mock).mockResolvedValue({ success: true });
    (uploadCustomerPostImage as jest.Mock).mockResolvedValue({ success: true, url: 'http://uploaded.image.com/image.jpg' });
  });

  it('renders correctly when visible', async () => {
    const { getByText, getByPlaceholderText } = render(<CustomerPostModal {...defaultProps} />);

    await waitFor(() => {
      expect(getByText('Create Post')).toBeTruthy();
      expect(getByPlaceholderText("What's on your mind, Test Customer?")).toBeTruthy();
    });
  });

  it('closes the modal when back button is pressed', async () => {
    const { getByTestId } = render(<CustomerPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByTestId('back-button'));
    });

    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('handles text input change', async () => {
    const { getByPlaceholderText } = render(<CustomerPostModal {...defaultProps} />);
    const input = getByPlaceholderText("What's on your mind, Test Customer?");

    fireEvent.changeText(input, 'Hello world');
    expect(input.props.value).toBe('Hello world');
  });

  it('handles image selection from camera', async () => {
    const mockImage = { uri: 'file://camera_image.jpg', width: 100, height: 100 };
    (ImagePicker.openCamera as jest.Mock).mockResolvedValue({ canceled: false, assets: [mockImage] });

    const { getByText, getByTestId } = render(<CustomerPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByText('Add Photo'));
      // Simulate selection from bottom sheet
      await (ImagePicker.openCamera as jest.Mock)();
    });

    await waitFor(() => {
      expect(getByTestId('post-image-preview')).toBeTruthy();
      expect(getByTestId('post-image-preview').props.source.uri).toBe(mockImage.uri);
    });
  });

  it('handles image selection from gallery', async () => {
    const mockImage = { uri: 'file://gallery_image.jpg', width: 100, height: 100 };
    (ImagePicker.openImageGallery as jest.Mock).mockResolvedValue({ canceled: false, assets: [mockImage] });

    const { getByText, getByTestId } = render(<CustomerPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByText('Add Photo'));
      // Simulate selection from bottom sheet
      await (ImagePicker.openImageGallery as jest.Mock)();
    });

    await waitFor(() => {
      expect(getByTestId('post-image-preview')).toBeTruthy();
      expect(getByTestId('post-image-preview').props.source.uri).toBe(mockImage.uri);
    });
  });

  it('removes image when remove button is pressed', async () => {
    const mockImage = { uri: 'file://camera_image.jpg', width: 100, height: 100 };
    (ImagePicker.openCamera as jest.Mock).mockResolvedValue({ canceled: false, assets: [mockImage] });

    const { getByText, getByTestId, queryByTestId } = render(<CustomerPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByText('Add Photo'));
      await (ImagePicker.openCamera as jest.Mock)();
    });

    await waitFor(() => {
      expect(getByTestId('post-image-preview')).toBeTruthy();
    });

    fireEvent.press(getByTestId('remove-image-button'));
    expect(queryByTestId('post-image-preview')).toBeNull();
  });

  it('submits post with text content', async () => {
    const { getByText, getByPlaceholderText } = render(<CustomerPostModal {...defaultProps} />);
    const input = getByPlaceholderText("What's on your mind, Test Customer?");
    fireEvent.changeText(input, 'My new post content');

    await act(async () => {
      fireEvent.press(getByText('Post'));
    });

    expect(createCustomerPost).toHaveBeenCalledWith({
      content: 'My new post content',
      image_url: null,
    });
    expect(defaultProps.onPostCreated).toHaveBeenCalled();
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('submits post with image and uploads it', async () => {
    const mockImage = { uri: 'file://new_image.jpg', width: 100, height: 100 };
    (ImagePicker.openCamera as jest.Mock).mockResolvedValue({ canceled: false, assets: [mockImage] });

    const { getByText } = render(<CustomerPostModal {...defaultProps} />);

    await act(async () => {
      fireEvent.press(getByText('Add Photo'));
      await (ImagePicker.openCamera as jest.Mock)();
      fireEvent.press(getByText('Post'));
    });

    expect(createCustomerPost).toHaveBeenCalledWith(expect.objectContaining({ image_url: null }));
    expect(uploadCustomerPostImage).toHaveBeenCalledWith(mockImage.uri, 'newPost123', expect.any(String));
    expect(updateCustomerPost).toHaveBeenCalledWith('newPost123', expect.objectContaining({ image_url: 'http://uploaded.image.com/image.jpg' }));
    expect(defaultProps.onPostCreated).toHaveBeenCalled();
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('shows alert if no content or image is selected', async () => {
    const { getByText } = render(<CustomerPostModal {...defaultProps} />);
    const alertSpy = jest.spyOn(require('react-native').Alert, 'alert');

    await act(async () => {
      fireEvent.press(getByText('Post'));
    });

    expect(alertSpy).toHaveBeenCalledWith(
      'Error',
      'Please add some content or an image to your post.'
    );
    alertSpy.mockRestore();
  });

  it('should rollback post creation when image upload fails', async () => {
    const mockCreateCustomerPost = createCustomerPost as jest.MockedFunction<typeof createCustomerPost>;
    const mockUploadCustomerPostImage = uploadCustomerPostImage as jest.MockedFunction<typeof uploadCustomerPostImage>;
    const mockDeleteCustomerPost = deleteCustomerPost as jest.MockedFunction<typeof deleteCustomerPost>;

    // Mock successful post creation
    mockCreateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    // Mock failed image upload
    mockUploadCustomerPostImage.mockResolvedValue({
      success: false,
      error: 'Upload failed'
    });

    // Mock successful post deletion
    mockDeleteCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post deleted'
    });

    const alertSpy = jest.spyOn(require('react-native').Alert, 'alert');

    const { getByPlaceholderText, getByText } = render(
      <CustomerPostModal {...defaultProps} />
    );

    // Add content and simulate image selection
    fireEvent.changeText(getByPlaceholderText("What's on your mind?"), 'Test post with image');

    // Mock image selection by setting imageUri state
    const modal = getByText('Post').closest('View');
    // Simulate image being selected (this would normally come from image picker)

    await act(async () => {
      fireEvent.press(getByText('Post'));
    });

    await waitFor(() => {
      expect(mockCreateCustomerPost).toHaveBeenCalled();
      expect(mockDeleteCustomerPost).toHaveBeenCalledWith('post-123');
      expect(alertSpy).toHaveBeenCalledWith('Error', 'Failed to upload image. Please try again.');
    });

    alertSpy.mockRestore();
  });

  it('should handle compression errors with rollback', async () => {
    const mockCreateCustomerPost = createCustomerPost as jest.MockedFunction<typeof createCustomerPost>;
    const mockDeleteCustomerPost = deleteCustomerPost as jest.MockedFunction<typeof deleteCustomerPost>;

    // Mock successful post creation
    mockCreateCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post created',
      data: { id: 'post-123', created_at: '2024-01-01T00:00:00Z' }
    });

    // Mock successful post deletion
    mockDeleteCustomerPost.mockResolvedValue({
      success: true,
      message: 'Post deleted'
    });

    // Mock compression failure
    jest.doMock('@/src/utils/client-image-compression', () => ({
      compressFileUltraAggressive: jest.fn().mockRejectedValue(new Error('Compression failed'))
    }));

    const alertSpy = jest.spyOn(require('react-native').Alert, 'alert');

    const { getByPlaceholderText, getByText } = render(
      <CustomerPostModal {...defaultProps} />
    );

    // Add content
    fireEvent.changeText(getByPlaceholderText("What's on your mind?"), 'Test post with image');

    await act(async () => {
      fireEvent.press(getByText('Post'));
    });

    await waitFor(() => {
      expect(mockDeleteCustomerPost).toHaveBeenCalledWith('post-123');
      expect(alertSpy).toHaveBeenCalledWith('Error', 'Failed to process image. Please try again.');
    });

    alertSpy.mockRestore();
  });
});
