{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_a5b8fa46._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_3918d6b0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "IuW0EIE6ZtJeNK6eheo6gjV6CIiM3pfC1AJTutob0DQ=", "__NEXT_PREVIEW_MODE_ID": "2d6b17d2777520f6939af748a483d5bb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1bc575892a488685fbf90d08303eb7a42454b69f691da638e5c0181b5ca9db7a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5191d3cfdb8dc01a0aef3406682b9047edbffc63b80d8858659c4c4401d341a5"}}}, "sortedMiddleware": ["/"], "functions": {}}