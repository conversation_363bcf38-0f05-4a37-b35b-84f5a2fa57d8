import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { DashboardLayout } from '../../../../src/components/shared/layout/DashboardLayout';
import { Animated, ScrollView } from 'react-native';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('react-native-safe-area-context', () => ({
  SafeAreaView: jest.fn(({ children, style }) => <View style={style}>{children}</View>),
}));

jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  RN.Animated.timing = jest.fn(() => ({
    start: jest.fn((callback) => callback && callback()),
  }));
  return RN;
});

jest.mock('@/src/components/shared/ui/Header', () => ({
  Header: jest.fn(({ userName, businessName, avatarUrl, showNotifications, onProfilePress, onNotificationPress }) => (
    <mock-Header
      testID="mock-header"
      userName={userName}
      businessName={businessName}
      avatarUrl={avatarUrl}
      showNotifications={showNotifications}
      onProfilePress={onProfilePress}
      onNotificationPress={onNotificationPress}
    />
  )),
}));

describe('DashboardLayout', () => {
  it('renders children correctly', () => {
    const { getByText } = render(
      <DashboardLayout>
        <Text>Test Child</Text>
      </DashboardLayout>
    );
    expect(getByText('Test Child')).toBeTruthy();
  });

  it('renders Header component with correct props', () => {
    const { getByTestId } = render(
      <DashboardLayout
        userName="John Doe"
        businessName="Acme Inc."
        avatarUrl="http://example.com/avatar.png"
        showNotifications={true}
        onProfilePress={jest.fn()}
        onNotificationPress={jest.fn()}
      >
        <Text>Child</Text>
      </DashboardLayout>
    );
    const header = getByTestId('mock-header');
    expect(header.props.userName).toBe('John Doe');
    expect(header.props.businessName).toBe('Acme Inc.');
    expect(header.props.avatarUrl).toBe('http://example.com/avatar.png');
    expect(header.props.showNotifications).toBe(true);
    expect(header.props.onProfilePress).toBeInstanceOf(Function);
    expect(header.props.onNotificationPress).toBeInstanceOf(Function);
  });

  it('applies fullWidth style when fullWidth prop is true', () => {
    const { getByTestId } = render(
      <DashboardLayout fullWidth={true}>
        <ScrollView testID="scroll-view" />
      </DashboardLayout>
    );
    const contentContainer = getByTestId('scroll-view').parent;
    expect(contentContainer.props.style.paddingHorizontal).toBeUndefined();
  });

  it('hides header when hideHeader prop is true', () => {
    const { queryByTestId } = render(
      <DashboardLayout hideHeader={true}>
        <Text>Child</Text>
      </DashboardLayout>
    );
    expect(queryByTestId('mock-header')).toBeNull();
  });

  it('applies initial padding top to content', () => {
    const { getByTestId } = render(
      <DashboardLayout>
        <ScrollView testID="scroll-view" />
      </DashboardLayout>
    );
    const contentContainer = getByTestId('scroll-view').parent;
    // Initial padding top should be set based on header height
    expect(contentContainer.props.style.paddingTop).toBeGreaterThan(0);
  });

  it('collapses header on scroll down and expands on scroll up', async () => {
    const { getByTestId } = render(
      <DashboardLayout>
        <ScrollView testID="scroll-view" />
      </DashboardLayout>
    );

    const scrollView = getByTestId('scroll-view');
    const headerContainer = getByTestId('mock-header').parent;

    // Simulate scroll down
    fireEvent.scroll(scrollView, {
      nativeEvent: { contentOffset: { y: 100 } },
    });

    await waitFor(() => {
      expect(Animated.timing).toHaveBeenCalledTimes(2);
      // Expect header to be hidden (translateY to negative header height)
      expect(headerContainer.props.style.transform[0].translateY).toBeLessThan(0);
    });

    // Simulate scroll up
    fireEvent.scroll(scrollView, {
      nativeEvent: { contentOffset: { y: 0 } },
    });

    await waitFor(() => {
      expect(Animated.timing).toHaveBeenCalledTimes(4); // Called twice more
      // Expect header to be visible (translateY to 0)
      expect(headerContainer.props.style.transform[0].translateY).toBe(0);
    });
  });
});
