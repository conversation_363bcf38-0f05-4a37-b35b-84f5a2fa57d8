import { renderHook, act } from '@testing-library/react-hooks';
import { useColorScheme } from '@/src/hooks/useColorScheme.web';
import { useColorScheme as useRNColorScheme } from 'react-native';

// Mock react-native's useColorScheme
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  useColorScheme: jest.fn(),
}));

describe('useColorScheme.web', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRNColorScheme as jest.Mock).mockReturnValue('dark'); // Default system theme for web
  });

  it('returns 'light' initially before hydration', () => {
    const { result } = renderHook(() => useColorScheme());
    expect(result.current).toBe('light');
  });

  it('returns system color scheme after hydration', () => {
    const { result } = renderHook(() => useColorScheme());
    act(() => {
      // Simulate useEffect for hydration
      result.current; // Access result to trigger re-render after hydration
    });
    expect(result.current).toBe('dark');
  });

  it('updates when system color scheme changes after hydration', () => {
    const { result, rerender } = renderHook(() => useColorScheme());
    act(() => {
      // Simulate useEffect for hydration
      result.current;
    });

    (useRNColorScheme as jest.Mock).mockReturnValue('light');
    rerender();
    expect(result.current).toBe('light');
  });
});