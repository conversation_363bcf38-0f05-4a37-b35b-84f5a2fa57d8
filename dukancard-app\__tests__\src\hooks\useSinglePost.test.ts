import { renderHook, act, waitFor } from '@testing-library/react-hooks';
import { useSinglePost } from '@/src/hooks/useSinglePost';
import { fetchSinglePost } from '@/lib/actions/posts/fetchSinglePost';

// Mock external dependencies
jest.mock('@/lib/actions/posts/fetchSinglePost');

describe('useSinglePost', () => {
  const mockPost = { id: '1', content: 'Test Post' };

  beforeEach(() => {
    jest.clearAllMocks();
    (fetchSinglePost as jest.Mock).mockResolvedValue({
      success: true,
      data: mockPost,
    });
  });

  it('should return initial loading state', () => {
    const { result } = renderHook(() => useSinglePost('1'));
    expect(result.current.loading).toBe(true);
    expect(result.current.post).toBeNull();
    expect(result.current.error).toBeNull();
  });

  it('should fetch and set post data on mount', async () => {
    const { result } = renderHook(() => useSinglePost('1'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.post).toEqual(mockPost);
      expect(result.current.error).toBeNull();
    });
    expect(fetchSinglePost).toHaveBeenCalledWith('1');
  });

  it('should handle fetch error', async () => {
    (fetchSinglePost as jest.Mock).mockResolvedValue({
      success: false,
      message: 'Post not found',
    });

    const { result } = renderHook(() => useSinglePost('999'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.post).toBeNull();
      expect(result.current.error).toBe('Post not found');
    });
  });

  it('should handle unexpected error during fetch', async () => {
    (fetchSinglePost as jest.Mock).mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useSinglePost('1'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.post).toBeNull();
      expect(result.current.error).toBe('Network error');
    });
  });

  it('should refetch post data', async () => {
    const { result } = renderHook(() => useSinglePost('1'));

    await waitFor(() => expect(result.current.post).toEqual(mockPost));

    const updatedPost = { ...mockPost, content: 'Updated Post' };
    (fetchSinglePost as jest.Mock).mockResolvedValueOnce({
      success: true,
      data: updatedPost,
    });

    await act(async () => {
      await result.current.refetch();
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.post).toEqual(updatedPost);
    expect(fetchSinglePost).toHaveBeenCalledTimes(2); // Initial fetch + refetch
  });

  it('should abort previous fetch on new request', async () => {
    let resolveFetch: (value: any) => void;
    (fetchSinglePost as jest.Mock).mockImplementationOnce(() => new Promise(resolve => { resolveFetch = resolve; }));

    const { result, rerender } = renderHook(({ postId }) => useSinglePost(postId), {
      initialProps: { postId: '1' },
    });

    expect(result.current.loading).toBe(true);

    // Rerender with a new postId, which should trigger a new fetch and abort the previous one
    rerender({ postId: '2' });

    // Resolve the first fetch, it should not update the state
    act(() => {
      resolveFetch({ success: true, data: { id: 'old', content: 'Old Post' } });
    });

    // Mock the second fetch to resolve immediately
    (fetchSinglePost as jest.Mock).mockResolvedValueOnce({
      success: true,
      data: { id: '2', content: 'New Post' },
    });

    await waitFor(() => {
      expect(result.current.post).toEqual({ id: '2', content: 'New Post' });
      expect(result.current.loading).toBe(false);
    });
  });

  it('should not update state if unmounted during fetch', async () => {
    let resolveFetch: (value: any) => void;
    (fetchSinglePost as jest.Mock).mockImplementationOnce(() => new Promise(resolve => { resolveFetch = resolve; }));

    const { result, unmount } = renderHook(() => useSinglePost('1'));

    expect(result.current.loading).toBe(true);

    unmount();

    act(() => {
      resolveFetch({ success: true, data: mockPost });
    });

    // Wait for any potential state updates, but expect none
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(result.current.post).toBeNull();
    expect(result.current.loading).toBe(true); // Loading state should remain as it was before unmount
  });
});