import { renderHook, act, waitFor } from '@testing-library/react-hooks';
import { useBusinessInteractions } from '@/src/hooks/useBusinessInteractions';
import { supabase } from '@/lib/supabase';
import {
  getBusinessInteractionStatus,
  toggleBusinessLike,
  toggleBusinessSubscription,
} from '@/backend/supabase/services/business/businessInteractions';
import { useToast } from '@/src/components/ui/Toast';

// Mock external dependencies
jest.mock('@/lib/supabase');
jest.mock('@/backend/supabase/services/business/businessInteractions');
jest.mock('@/src/components/ui/Toast');

describe('useBusinessInteractions', () => {
  const mockBusinessId = 'biz123';
  const mockUserId = 'user123';
  const mockToastError = jest.fn();
  const mockToastSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: { user: { id: mockUserId } },
      error: null,
    });
    (getBusinessInteractionStatus as jest.Mock).mockResolvedValue({
      success: true,
      data: { is_liked: false, is_subscribed: false, has_reviewed: false },
    });
    (toggleBusinessLike as jest.Mock).mockResolvedValue({
      success: true,
      message: 'Like status updated',
    });
    (toggleBusinessSubscription as jest.Mock).mockResolvedValue({
      success: true,
      message: 'Subscription status updated',
    });
    (useToast as jest.Mock).mockReturnValue({
      error: mockToastError,
      success: mockToastSuccess,
    });
  });

  it('should return initial interaction status and ownership', async () => {
    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => {
      expect(result.current.interactionStatus).toEqual({
        is_liked: false,
        is_subscribed: false,
        has_reviewed: false,
      });
      expect(result.current.isOwner).toBe(false);
    });
  });

  it('should identify as owner if businessId matches userId', async () => {
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: { user: { id: mockBusinessId } },
      error: null,
    });

    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => {
      expect(result.current.isOwner).toBe(true);
      expect(result.current.interactionStatus).toBeNull(); // Should not fetch interactions for owner
    });
  });

  it('should handle liking a business', async () => {
    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => expect(result.current.isOwner).toBe(false));

    await act(async () => {
      await result.current.handleLikePress();
    });

    expect(toggleBusinessLike).toHaveBeenCalledWith(mockBusinessId);
    expect(mockToastSuccess).toHaveBeenCalledWith('Like status updated');
    expect(getBusinessInteractionStatus).toHaveBeenCalledTimes(2); // Initial + after toggle
  });

  it('should handle unliking a business', async () => {
    (getBusinessInteractionStatus as jest.Mock).mockResolvedValueOnce({
      success: true,
      data: { is_liked: true, is_subscribed: false, has_reviewed: false },
    });
    (toggleBusinessLike as jest.Mock).mockResolvedValueOnce({
      success: true,
      message: 'Unlike status updated',
    });

    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => expect(result.current.interactionStatus?.is_liked).toBe(true));

    await act(async () => {
      await result.current.handleLikePress();
    });

    expect(toggleBusinessLike).toHaveBeenCalledWith(mockBusinessId);
    expect(mockToastSuccess).toHaveBeenCalledWith('Unlike status updated');
  });

  it('should prevent owner from liking their own business', async () => {
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: { user: { id: mockBusinessId } },
      error: null,
    });

    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => expect(result.current.isOwner).toBe(true));

    await act(async () => {
      await result.current.handleLikePress();
    });

    expect(toggleBusinessLike).not.toHaveBeenCalled();
    expect(mockToastError).toHaveBeenCalledWith('You cannot like your own business');
  });

  it('should handle subscribing to a business', async () => {
    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => expect(result.current.isOwner).toBe(false));

    await act(async () => {
      await result.current.handleSubscribePress();
    });

    expect(toggleBusinessSubscription).toHaveBeenCalledWith(mockBusinessId);
    expect(mockToastSuccess).toHaveBeenCalledWith('Subscription status updated');
    expect(getBusinessInteractionStatus).toHaveBeenCalledTimes(2); // Initial + after toggle
  });

  it('should prevent owner from subscribing to their own business', async () => {
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: { user: { id: mockBusinessId } },
      error: null,
    });

    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => expect(result.current.isOwner).toBe(true));

    await act(async () => {
      await result.current.handleSubscribePress();
    });

    expect(toggleBusinessSubscription).not.toHaveBeenCalled();
    expect(mockToastError).toHaveBeenCalledWith('You cannot subscribe to your own business');
  });

  it('should call onReviewModalOpen when handleReviewPress is called by non-owner', async () => {
    const mockOnReviewModalOpen = jest.fn();
    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => expect(result.current.isOwner).toBe(false));

    act(() => {
      result.current.handleReviewPress(mockOnReviewModalOpen);
    });

    expect(mockOnReviewModalOpen).toHaveBeenCalledTimes(1);
  });

  it('should prevent owner from reviewing their own business', async () => {
    const mockOnReviewModalOpen = jest.fn();
    (supabase.auth.getUser as jest.Mock).mockResolvedValue({
      data: { user: { id: mockBusinessId } },
      error: null,
    });

    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => expect(result.current.isOwner).toBe(true));

    act(() => {
      result.current.handleReviewPress(mockOnReviewModalOpen);
    });

    expect(mockOnReviewModalOpen).not.toHaveBeenCalled();
    expect(mockToastError).toHaveBeenCalledWith('You cannot review your own business');
  });

  it('should refresh interaction status after review submitted', async () => {
    const { result } = renderHook(() => useBusinessInteractions(mockBusinessId));

    await waitFor(() => expect(getBusinessInteractionStatus).toHaveBeenCalledTimes(1));

    await act(async () => {
      await result.current.handleReviewSubmitted();
    });

    expect(getBusinessInteractionStatus).toHaveBeenCalledTimes(2); // Called again after review submitted
  });
});