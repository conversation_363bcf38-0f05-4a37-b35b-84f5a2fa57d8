import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { Alert, ActivityIndicator } from 'react-native';
import { LikeCard } from '@/src/components/social/LikeCard';
import { useRouter } from 'expo-router';
import { useToast } from '@/src/components/ui/Toast';

// Mock necessary modules
jest.mock('expo-router');
jest.mock('@/src/components/ui/Toast');
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
  ActivityIndicator: jest.fn(() => null), // Mock ActivityIndicator to return null
}));

const mockLike = {
  id: 'like1',
  business_profiles: {
    id: 'business1',
    business_name: 'Test Business',
    business_slug: 'test-business',
    logo_url: 'https://example.com/logo.jpg',
    locality: 'Test Locality',
    city: 'Test City',
    state: 'Test State',
  },
};

describe('LikeCard', () => {
  const onUnlikeMock = jest.fn();
  const mockRouterPush = jest.fn();
  const mockToastError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockRouterPush });
    (useToast as jest.Mock).mockReturnValue({ error: mockToastError });
    (ActivityIndicator as jest.Mock).mockImplementation(({ color }) => <Text testID="activity-indicator" style={{ color }}>Loading...</Text>);
  });

  it('renders correctly with business data', () => {
    render(<LikeCard like={mockLike} onUnlike={onUnlikeMock} />);

    expect(screen.getByText('Test Business')).toBeOnTheScreen();
    expect(screen.getByText('Test Locality, Test City, Test State')).toBeOnTheScreen();
    expect(screen.getByText('Liked')).toBeOnTheScreen();
  });

  it('displays business initials when no logo_url is provided', () => {
    const likeWithoutLogo = {
      ...mockLike,
      business_profiles: { ...mockLike.business_profiles, logo_url: null },
    };
    render(<LikeCard like={likeWithoutLogo} onUnlike={onUnlikeMock} />);

    expect(screen.getByText('TB')).toBeOnTheScreen();
  });

  it('navigates to business profile on touch', () => {
    render(<LikeCard like={mockLike} onUnlike={onUnlikeMock} />);
    fireEvent.press(screen.getByText('Test Business'));
    expect(mockRouterPush).toHaveBeenCalledWith('/business/test-business');
  });

  it('shows alert on unlike button press', () => {
    render(<LikeCard like={mockLike} onUnlike={onUnlikeMock} />);
    fireEvent.press(screen.getByLabelText('Unlike')); // Assuming an accessibilityLabel for the unlike button
    expect(Alert.alert).toHaveBeenCalledWith(
      'Unlike Business',
      'Are you sure you want to remove Test Business from your liked businesses?',
      expect.any(Array)
    );
  });

  it('calls onUnlike when unlike is confirmed', async () => {
    render(<LikeCard like={mockLike} onUnlike={onUnlikeMock} />);
    fireEvent.press(screen.getByLabelText('Unlike'));

    // Simulate pressing the 'Unlike' button in the alert
    const unlikeAction = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    await unlikeAction.onPress();

    expect(onUnlikeMock).toHaveBeenCalledWith('like1');
  });

  it('shows activity indicator when unliking', async () => {
    onUnlikeMock.mockImplementationOnce(() => new Promise(resolve => setTimeout(resolve, 100)));

    render(<LikeCard like={mockLike} onUnlike={onUnlikeMock} />);
    fireEvent.press(screen.getByLabelText('Unlike'));

    const unlikeAction = (Alert.alert as jest.Mock).mock.calls[0][2][1];
    unlikeAction.onPress();

    expect(screen.getByTestId('activity-indicator')).toBeOnTheScreen();
    await screen.findByText('Test Business'); // Wait for unliking to complete
    expect(screen.queryByTestId('activity-indicator')).toBeNull();
  });

  it('handles navigation error gracefully', () => {
    const likeWithoutSlug = {
      ...mockLike,
      business_profiles: { ...mockLike.business_profiles, business_slug: null },
    };
    render(<LikeCard like={likeWithoutSlug} onUnlike={onUnlikeMock} />);
    fireEvent.press(screen.getByText('Test Business'));
    expect(mockToastError).toHaveBeenCalledWith("Navigation Error", "Business profile not available");
    expect(mockRouterPush).not.toHaveBeenCalled();
  });
});