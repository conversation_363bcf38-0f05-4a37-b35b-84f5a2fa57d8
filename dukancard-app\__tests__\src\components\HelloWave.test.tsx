import React from 'react';
import { render } from '@testing-library/react-native';
import { HelloWave } from '../../../src/components/HelloWave';
import Animated, { useSharedValue, withRepeat, withSequence, withTiming } from 'react-native-reanimated';

// Mock reanimated functions
jest.mock('react-native-reanimated', () => ({
  useSharedValue: jest.fn(() => ({ value: 0 })),
  useAnimatedStyle: jest.fn(() => ({})),
  withRepeat: jest.fn((animation) => animation),
  withSequence: jest.fn((...animations) => animations[0]), // Only return the first animation for simplicity
  withTiming: jest.fn((toValue, config) => `timing-${toValue}-${config.duration}`),
}));

jest.mock('@/src/components/ThemedText', () => ({
  ThemedText: ({ children, style }: any) => <text style={style}>{children}</text>,
}));

describe('HelloWave', () => {
  it('renders the wave emoji', () => {
    const { getByText } = render(<HelloWave />);
    expect(getByText('👋')).toBeTruthy();
  });

  it('initializes rotationAnimation with useSharedValue', () => {
    render(<HelloWave />);
    expect(useSharedValue).toHaveBeenCalledWith(0);
  });

  it('sets up rotation animation with useEffect', () => {
    const mockSharedValue = { value: 0 };
    (useSharedValue as jest.Mock).mockReturnValue(mockSharedValue);

    render(<HelloWave />);

    expect(withRepeat).toHaveBeenCalled();
    expect(withSequence).toHaveBeenCalledWith(
      'timing-25-150',
      'timing-0-150'
    );
    expect(withTiming).toHaveBeenCalledWith(25, { duration: 150 });
    expect(withTiming).toHaveBeenCalledWith(0, { duration: 150 });
  });
});
