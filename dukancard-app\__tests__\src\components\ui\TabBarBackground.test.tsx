import { useBottomTabOverflow } from '@/src/components/ui/TabBarBackground';

describe('TabBarBackground (Android/Web Shim)', () => {
  it('useBottomTabOverflow returns 0', () => {
    expect(useBottomTabOverflow()).toBe(0);
  });

  it('default export is undefined', () => {
    // This file is a shim and exports undefined as default
    const TabBarBackground = require('@/src/components/ui/TabBarBackground').default;
    expect(TabBarBackground).toBeUndefined();
  });
});