import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ProductRecommendations from '../../../../src/components/product/ProductRecommendations';

// Mock necessary modules
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light', // Mock for consistent testing
}));

jest.mock('@/src/components/shared/ui', () => ({
  ProductCard: jest.fn(({ product, isClickable, variant, width }) => (
    <mock-ProductCard
      testID={`product-card-${product.id}`}
      product={product}
      isClickable={isClickable}
      variant={variant}
      width={width}
    />
  )),
}));

describe('ProductRecommendations', () => {
  const mockBusinessProducts = [
    {
      id: 'prod1',
      name: 'Product 1',
      base_price: 100,
      business_id: 'biz1',
      slug: 'product-1',
    },
    {
      id: 'prod2',
      name: 'Product 2',
      base_price: 200,
      business_id: 'biz1',
      slug: 'product-2',
    },
  ];

  const mockOtherBusinessProducts = [
    {
      id: 'prod3',
      name: 'Product 3',
      base_price: 300,
      business_id: 'biz2',
      business_slug: 'business-2',
      slug: 'product-3',
    },
  ];

  it('renders loading state correctly', () => {
    const { getByText, getByTestId } = render(
      <ProductRecommendations
        businessProducts={[]}
        otherBusinessProducts={[]}
        businessSlug="test-business"
        loading={true}
      />
    );
    expect(getByText('Loading recommendations...')).toBeTruthy();
    expect(getByTestId('ActivityIndicator')).toBeTruthy();
  });

  it('renders nothing when no products are available and not loading', () => {
    const { queryByText } = render(
      <ProductRecommendations
        businessProducts={[]}
        otherBusinessProducts={[]}
        businessSlug="test-business"
        loading={false}
      />
    );
    expect(queryByText('More from this business')).toBeNull();
    expect(queryByText('You might also like')).toBeNull();
  });

  it('renders "More from this business" section when businessProducts are provided', () => {
    const { getByText, getByTestId } = render(
      <ProductRecommendations
        businessProducts={mockBusinessProducts}
        otherBusinessProducts={[]}
        businessSlug="test-business"
      />
    );
    expect(getByText('More from this business')).toBeTruthy();
    expect(getByTestId('product-card-prod1')).toBeTruthy();
    expect(getByTestId('product-card-prod2')).toBeTruthy();
  });

  it('renders "You might also like" section when otherBusinessProducts are provided', () => {
    const { getByText, getByTestId } = render(
      <ProductRecommendations
        businessProducts={[]}
        otherBusinessProducts={mockOtherBusinessProducts}
        businessSlug="test-business"
      />
    );
    expect(getByText('You might also like')).toBeTruthy();
    expect(getByTestId('product-card-prod3')).toBeTruthy();
  });

  it('calls router.push with correct product ID when ProductCard is pressed', () => {
    const mockPush = jest.fn();
    jest.mock('expo-router', () => ({
      useRouter: () => ({
        push: mockPush,
      }),
    }));

    const { getByTestId } = render(
      <ProductRecommendations
        businessProducts={mockBusinessProducts}
        otherBusinessProducts={[]}
        businessSlug="test-business"
      />
    );

    fireEvent.press(getByTestId('product-card-prod1'));
    expect(mockPush).toHaveBeenCalledWith('/product/prod1');
  });
});
