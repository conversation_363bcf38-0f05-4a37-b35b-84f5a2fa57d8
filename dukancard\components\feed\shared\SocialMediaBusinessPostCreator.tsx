"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Image as ImageIcon,
  MapPin,
  X,
  Send,
  Loader2,
  Package,
  Building2,
  Check,
  ChevronsUpDown,
  Search,
  GripVertical
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { toast } from "sonner";
import { createClient } from "@/utils/supabase/client";
import { createPost, updatePost } from "@/lib/actions/posts";
import { useRouter } from "next/navigation";
import Image from "next/image";
import LocationDisplay from "./forms/LocationDisplay";
import { cn } from "@/lib/utils";
import { searchBusinessProducts, getSelectedProducts } from "@/lib/actions/shared/productActions";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface ProductData {
  id: string;
  name: string;
  slug: string | null;
  base_price: number | null;
  discounted_price: number | null;
  image_url: string | null;
}

interface SocialMediaBusinessPostCreatorProps {
  businessName?: string;
  onPostCreated?: () => void;
}

// Sortable Product Item Component
interface SortableProductItemProps {
  product: ProductData;
  onRemove: (_productId: string) => void;
  formatPrice: (_price: number | null) => string;
}

function SortableProductItem({ product, onRemove, formatPrice }: SortableProductItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: product.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-muted/50 rounded-lg border min-h-[60px] sm:min-h-[68px]"
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground shrink-0"
      >
        <GripVertical className="h-4 w-4" />
      </div>

      {/* Product Image */}
      <div className="relative h-8 w-8 sm:h-10 sm:w-10 shrink-0 rounded-md overflow-hidden bg-background">
        {product.image_url ? (
          <Image
            src={product.image_url}
            alt={product.name}
            fill
            className="object-cover"
            sizes="(max-width: 640px) 32px, 40px"
          />
        ) : (
          <div className="flex items-center justify-center h-full w-full">
            <Package className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </div>
        )}
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0 pr-1 sm:pr-2">
        {/* Product Name - Truncated to single line for consistent height */}
        <div className="font-medium text-xs sm:text-sm leading-tight mb-1">
          <span className="line-clamp-1 break-words">
            {product.name}
          </span>
        </div>

        {/* Price - Single line layout */}
        <div className="text-xs text-muted-foreground">
          {product.discounted_price ? (
            <div className="flex items-center gap-1 flex-wrap">
              <span className="text-primary font-medium">
                {formatPrice(product.discounted_price)}
              </span>
              <span className="line-through text-xs">
                {formatPrice(product.base_price)}
              </span>
            </div>
          ) : (
            <span className="font-medium">
              {formatPrice(product.base_price)}
            </span>
          )}
        </div>
      </div>

      {/* Remove Button */}
      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6 sm:h-8 sm:w-8 shrink-0 hover:bg-destructive/10 hover:text-destructive"
        onClick={() => onRemove(product.id)}
      >
        <X className="h-3 w-3 sm:h-4 sm:w-4" />
      </Button>
    </div>
  );
}

export default function SocialMediaBusinessPostCreator({ 
  businessName, 
  onPostCreated 
}: SocialMediaBusinessPostCreatorProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [content, setContent] = useState("");
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [businessAvatar, setBusinessAvatar] = useState<string | null>(null);
  const [businessDisplayName, setBusinessDisplayName] = useState(businessName || "");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [detectedImageUrl, setDetectedImageUrl] = useState<string | null>(null);
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [products, setProducts] = useState<ProductData[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<ProductData[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const router = useRouter();

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Character limit
  const MAX_CHARS = 2000;
  const charCount = content.length;
  const isOverLimit = charCount > MAX_CHARS;

  // Fetch business profile
  useEffect(() => {
    const fetchBusinessProfile = async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { data: profile } = await supabase
          .from('business_profiles')
          .select('business_name, logo_url')
          .eq('id', user.id)
          .single();
        
        if (profile) {
          setBusinessDisplayName(profile.business_name || businessName || "Business");
          setBusinessAvatar(profile.logo_url);
        }
      }
    };

    fetchBusinessProfile();
  }, [businessName]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [content]);

  // Focus textarea when expanded
  useEffect(() => {
    if (isExpanded && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isExpanded]);



  const handleExpand = () => {
    setIsExpanded(true);
  };

  const handleCollapse = () => {
    if (!content.trim() && !imageUrl) {
      setIsExpanded(false);
      setSelectedProductIds([]);
      setShowProductSelector(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size must be less than 5MB');
      return;
    }

    // Store file and create preview URL
    setSelectedFile(file);
    const preview = URL.createObjectURL(file);
    setPreviewUrl(preview);
    setImageUrl(null); // Clear any existing URL
  };

  const handleImageRemove = () => {
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    setImageUrl(null);
    setDetectedImageUrl(null);
  };

  // Function to detect image URLs in content
  const detectImageUrl = (text: string) => {
    const imageUrlRegex = /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^\s]*)?)/i;
    const match = text.match(imageUrlRegex);
    return match ? match[1] : null;
  };

  // Handle content change with URL detection
  const handleContentChange = (newContent: string) => {
    setContent(newContent);

    // Detect image URL in content
    const detectedUrl = detectImageUrl(newContent);
    if (detectedUrl && detectedUrl !== detectedImageUrl) {
      setDetectedImageUrl(detectedUrl);
      // Only set as imageUrl if no file is selected
      if (!selectedFile) {
        setImageUrl(detectedUrl);
      }
    } else if (!detectedUrl && detectedImageUrl) {
      setDetectedImageUrl(null);
      // Only clear imageUrl if it was from detected URL (not from file upload)
      if (!selectedFile) {
        setImageUrl(null);
      }
    }
  };

  const handleProductToggle = () => {
    setShowProductSelector(!showProductSelector);
  };

  // Handle drag end for product reordering
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = selectedProducts.findIndex((product) => product.id === active.id);
      const newIndex = selectedProducts.findIndex((product) => product.id === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const newSelectedProducts = arrayMove(selectedProducts, oldIndex, newIndex);
        setSelectedProducts(newSelectedProducts);

        // Update the product IDs array to maintain the new order
        const newSelectedIds = newSelectedProducts.map(product => product.id);
        setSelectedProductIds(newSelectedIds);
      }
    }
  };

  // Load selected products details
  const loadSelectedProducts = useCallback(async () => {
    if (selectedProductIds.length === 0) {
      setSelectedProducts([]);
      return;
    }

    setIsLoadingProducts(true);
    try {
      const result = await getSelectedProducts(selectedProductIds);

      if (result.success && result.data) {
        // Maintain the order of selectedProductIds
        const orderedProducts = selectedProductIds
          .map(id => result.data?.find(product => product.id === id))
          .filter(Boolean) as ProductData[];

        setSelectedProducts(orderedProducts);
      } else {
        console.error('Error loading selected products:', result.error);
        setSelectedProducts([]);
      }
    } catch (error) {
      console.error('Error loading selected products:', error);
      setSelectedProducts([]);
    } finally {
      setIsLoadingProducts(false);
    }
  }, [selectedProductIds]);

  // Search products
  const searchProducts = async (query: string) => {
    setIsLoadingProducts(true);
    setHasSearched(false);
    try {
      const result = await searchBusinessProducts(query);

      if (result.success && result.data) {
        setProducts(result.data);
      } else {
        console.error('Error searching products:', result.error);
        setProducts([]);
      }
    } catch (error) {
      console.error('Error searching products:', error);
      setProducts([]);
    } finally {
      setIsLoadingProducts(false);
      setHasSearched(true);
    }
  };

  // Handle search input change with debouncing
  const handleSearchChange = (value: string) => {
    setSearchValue(value);

    // Clear existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Clear products if search is too short
    if (value.length < 2) {
      setProducts([]);
      setHasSearched(false);
      return;
    }

    // Set new timer for debounced search
    debounceTimerRef.current = setTimeout(() => {
      searchProducts(value);
    }, 300); // 300ms debounce delay
  };

  // Toggle product selection
  const toggleProduct = (product: ProductData) => {
    const isSelected = selectedProductIds.includes(product.id);
    let newSelectedIds: string[];

    if (isSelected) {
      newSelectedIds = selectedProductIds.filter(id => id !== product.id);
      setSelectedProducts(prev => prev.filter(p => p.id !== product.id));
    } else {
      // Check if maximum limit is reached
      if (selectedProductIds.length >= 5) {
        return; // Don't add more products if limit is reached
      }
      newSelectedIds = [...selectedProductIds, product.id];
      setSelectedProducts(prev => [...prev, product]);
    }

    setSelectedProductIds(newSelectedIds);
  };

  // Remove a selected product
  const removeProduct = (productId: string) => {
    const newSelectedIds = selectedProductIds.filter(id => id !== productId);
    setSelectedProducts(prev => prev.filter(p => p.id !== productId));
    setSelectedProductIds(newSelectedIds);
  };

  // Format price display
  const formatPrice = (price: number | null) => {
    if (price === null) return 'N/A';
    return `₹${price.toLocaleString('en-IN')}`;
  };

  const checkBusinessProfile = async () => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast.error('Please log in to continue');
      return false;
    }

    const { data: profile, error } = await supabase
      .from('business_profiles')
      .select('business_name, pincode, city, state, locality')
      .eq('id', user.id)
      .single();

    if (error) {
      toast.error('Failed to check business profile');
      return false;
    }

    if (!profile?.business_name || profile.business_name.trim() === '') {
      toast.error('Please complete your business name in your profile');
      router.push('/dashboard/business/profile');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!content.trim() && !previewUrl && !detectedImageUrl) {
      toast.error('Please add some content or an image');
      return;
    }

    if (isOverLimit) {
      toast.error('Post content is too long');
      return;
    }

    setIsSubmitting(true);

    try {
      const hasValidProfile = await checkBusinessProfile();
      if (!hasValidProfile) {
        setIsSubmitting(false);
        return;
      }

      let result;

      if (selectedFile && !imageUrl) {
        // Compress image locally first
        const { compressImageUltraAggressiveClient } = await import("@/lib/utils/client-image-compression");
        const compressionResult = await compressImageUltraAggressiveClient(selectedFile, {
          maxDimension: 1200,
          targetSizeKB: 100
        });

        // Convert compressed blob to file
        const compressedFile = new File([compressionResult.blob], selectedFile.name, {
          type: compressionResult.blob.type
        });

        // Create post first without image
        const createResult = await createPost({
          content: content.trim(),
          image_url: null,
          product_ids: selectedProductIds,
          mentioned_business_ids: [],
        });

        if (createResult.success && createResult.data) {
          const postData = createResult.data as { id: string; created_at: string };

          try {
            // Upload compressed image using proper service
            const { uploadBusinessPostImage } = await import("@/lib/actions/shared/upload-business-post-media");
            const formData = new FormData();
            formData.append("imageFile", compressedFile);

            const uploadResult = await uploadBusinessPostImage(formData, postData.id, postData.created_at);

            if (uploadResult.success && uploadResult.url) {
              // Update post with image URL
              const updateResult = await updatePost(postData.id, {
                content: content.trim(),
                image_url: uploadResult.url,
                product_ids: selectedProductIds,
                mentioned_business_ids: [],
              });
              result = updateResult;
            } else {
              // Image upload failed - delete the created post to rollback
              console.error('Image upload failed:', uploadResult.error);
              // TODO: Add post deletion logic here
              toast.error('Failed to upload image. Please try again.');
              setIsSubmitting(false);
              return;
            }
          } catch (uploadError) {
            console.error('Image upload error:', uploadError);
            // TODO: Add post deletion logic here
            toast.error('Failed to upload image. Please try again.');
            setIsSubmitting(false);
            return;
          }
        } else {
          result = createResult;
        }
      } else {
        // No file upload needed or URL provided
        result = await createPost({
          content: content.trim(),
          image_url: imageUrl,
          product_ids: selectedProductIds,
          mentioned_business_ids: [],
        });
      }

      if (result.success) {
        toast.success('Post created successfully!');
        setContent("");
        setImageUrl(null);
        handleImageRemove(); // Clear preview
        setIsExpanded(false);
        setSelectedProductIds([]);
        setShowProductSelector(false);
        onPostCreated?.();
      } else {
        toast.error(result.error || 'Failed to create post');
      }
    } catch (error) {
      console.error('Error creating post:', error);
      toast.error('Failed to create post');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Load selected products on mount
  useEffect(() => {
    if (selectedProductIds.length > 0) {
      loadSelectedProducts();
    }
  }, [selectedProductIds, loadSelectedProducts]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return (
    <motion.div
      layout
      className="bg-white dark:bg-black overflow-hidden md:border md:border-gray-200 md:dark:border-gray-700 md:rounded-xl md:shadow-sm"
    >
      {/* Collapsed State */}
      <AnimatePresence mode="wait">
        {!isExpanded && (
          <motion.div
            key="collapsed"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="p-4"
          >
          <div 
            className="flex items-center gap-3 cursor-pointer"
            onClick={handleExpand}
          >
            <Avatar className="w-10 h-10 ring-2 ring-[#D4AF37]/30">
              <AvatarImage src={businessAvatar || undefined} alt={businessDisplayName} />
              <AvatarFallback className="bg-[#D4AF37] text-white text-sm font-medium">
                <Building2 className="h-5 w-5" />
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 bg-gray-50 dark:bg-gray-800 rounded-full px-4 py-3 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              What&apos;s happening at {businessDisplayName}?
            </div>
          </div>
        </motion.div>
        )}
      </AnimatePresence>

      {/* Expanded State */}
      <AnimatePresence mode="wait">
        {isExpanded && (
          <motion.div
            key="expanded"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10 ring-2 ring-[#D4AF37]/30">
                  <AvatarImage src={businessAvatar || undefined} alt={businessDisplayName} />
                  <AvatarFallback className="bg-[#D4AF37] text-white text-sm font-medium">
                    <Building2 className="h-5 w-5" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                    {businessDisplayName}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Business post
                  </p>
                </div>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCollapse}
                className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Content Area */}
            <div className="p-4">
              {/* Posting from location */}
              <div className="mb-3">
                <LocationDisplay />
              </div>

              <textarea
                ref={textareaRef}
                value={content}
                onChange={(e) => handleContentChange(e.target.value)}
                placeholder={`What's happening at ${businessDisplayName}?`}
                className="w-full resize-none border-none outline-none text-lg placeholder-gray-500 dark:placeholder-gray-400 bg-transparent text-gray-900 dark:text-gray-100 min-h-[80px]"
                style={{ maxHeight: '300px' }}
              />

              {/* Character Count */}
              {charCount > 0 && (
                <div className={`text-xs mt-2 text-right ${
                  isOverLimit ? 'text-red-500' : 'text-gray-400'
                }`}>
                  {charCount}/{MAX_CHARS}
                </div>
              )}
            </div>

            {/* Image Preview */}
            <AnimatePresence>
              {(previewUrl || detectedImageUrl) && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="px-4 pb-4"
                >
                  <div className="relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                    <Image
                      src={previewUrl || detectedImageUrl || ''}
                      alt="Post image preview"
                      width={500}
                      height={300}
                      className="w-full h-auto max-h-96 object-cover"
                      onError={handleImageRemove}
                    />
                    <button
                      onClick={handleImageRemove}
                      className="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1.5 hover:bg-opacity-70 transition-all"
                    >
                      <X className="h-4 w-4" />
                    </button>
                    {detectedImageUrl && !previewUrl && (
                      <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                        Auto-detected image
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Product Selector */}
            <AnimatePresence>
              {showProductSelector && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="px-4 pb-4"
                >
                  <div className="space-y-4">
                    {/* Product Search Combobox */}
                    <Popover open={searchValue.length >= 2} onOpenChange={(newOpen) => {
                      if (!newOpen) {
                        setSearchValue('');
                        setProducts([]);
                        setHasSearched(false);
                      }
                    }}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          className="w-full justify-between h-auto min-h-[40px] px-3 py-2"
                        >
                          <div className="flex items-center gap-2">
                            <Search className="h-4 w-4 text-muted-foreground" />
                            <span className="text-left text-muted-foreground">
                              Search and add products...
                            </span>
                          </div>
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="p-0"
                        align="start"
                        sideOffset={4}
                        style={{ width: 'var(--radix-popover-trigger-width)' }}
                      >
                        <Command>
                          <CommandInput
                            placeholder="Search your products..."
                            value={searchValue}
                            onValueChange={handleSearchChange}
                            className="h-9 border-0 focus:ring-0 focus:ring-offset-0"
                          />
                          <CommandList className="max-h-[300px]">
                            {/* Show loading state */}
                            {isLoadingProducts && (
                              <div className="flex items-center justify-center py-8">
                                <div className="flex flex-col items-center gap-2">
                                  <Loader2 className="h-6 w-6 animate-spin text-[#D4AF37]" />
                                  <span className="text-sm text-muted-foreground">Searching products...</span>
                                </div>
                              </div>
                            )}

                            {/* Show empty state only when not loading and no products */}
                            {!isLoadingProducts && products.length === 0 && (
                              <CommandEmpty>
                                <div className="flex flex-col items-center justify-center py-8 text-center">
                                  <Package className="h-8 w-8 text-muted-foreground mb-2" />
                                  <span className="text-sm font-medium">
                                    {searchValue.length < 2
                                      ? 'Type at least 2 characters to search'
                                      : hasSearched
                                      ? 'No products found'
                                      : ''}
                                  </span>
                                  {searchValue.length >= 2 && hasSearched && (
                                    <span className="text-xs text-muted-foreground mt-1">
                                      Try a different search term
                                    </span>
                                  )}
                                </div>
                              </CommandEmpty>
                            )}

                            {/* Show products only when not loading and we have products */}
                            {!isLoadingProducts && products.length > 0 && (
                              <CommandGroup>
                                {products.map((product) => (
                                <CommandItem
                                  key={product.id}
                                  value={product.slug || product.id}
                                  onSelect={() => {
                                    // Only allow selection if not already at max limit or if already selected
                                    if (selectedProductIds.length < 5 || selectedProductIds.includes(product.id)) {
                                      toggleProduct(product);
                                      setSearchValue('');
                                      setProducts([]);
                                      setHasSearched(false);
                                    }
                                  }}
                                  disabled={selectedProductIds.length >= 5 && !selectedProductIds.includes(product.id)}
                                  className={cn(
                                    "flex items-center gap-3 p-3 cursor-pointer",
                                    selectedProductIds.length >= 5 && !selectedProductIds.includes(product.id)
                                      ? "opacity-50 cursor-not-allowed"
                                      : ""
                                  )}
                                >
                                  {/* Product Image */}
                                  <div className="relative h-10 w-10 shrink-0 rounded-md overflow-hidden bg-muted">
                                    {product.image_url ? (
                                      <Image
                                        src={product.image_url}
                                        alt={product.name}
                                        fill
                                        className="object-cover"
                                        sizes="40px"
                                      />
                                    ) : (
                                      <div className="flex items-center justify-center h-full w-full">
                                        <Package className="h-5 w-5 text-muted-foreground" />
                                      </div>
                                    )}
                                  </div>

                                  {/* Product Details */}
                                  <div className="flex-1 min-w-0 pr-2">
                                    <div className="font-medium text-sm truncate mb-1">{product.name}</div>
                                    <div className="text-xs text-muted-foreground">
                                      {product.discounted_price ? (
                                        <div className="flex items-center gap-1 flex-wrap">
                                          <span className="text-[#D4AF37] font-medium">
                                            {formatPrice(product.discounted_price)}
                                          </span>
                                          <span className="line-through text-xs">
                                            {formatPrice(product.base_price)}
                                          </span>
                                        </div>
                                      ) : (
                                        <span className="font-medium">
                                          {formatPrice(product.base_price)}
                                        </span>
                                      )}
                                    </div>
                                  </div>

                                  {/* Check Icon */}
                                  <Check
                                    className={cn(
                                      "ml-auto h-4 w-4",
                                      selectedProductIds.includes(product.id)
                                        ? "opacity-100 text-[#D4AF37]"
                                        : "opacity-0"
                                    )}
                                  />
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            )}
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    {/* Selected products with drag and drop */}
                    <div className="space-y-3">
                      {/* Show loading state when loading selected products */}
                      {isLoadingProducts && selectedProductIds.length > 0 && selectedProducts.length === 0 && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                            <Package className="h-4 w-4" />
                            <span>Loading Selected Products...</span>
                          </div>
                          <div className="flex justify-center py-6">
                            <div className="flex flex-col items-center gap-2">
                              <Loader2 className="h-6 w-6 animate-spin text-[#D4AF37]" />
                              <span className="text-xs text-muted-foreground">Fetching product details...</span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Show selected products when loaded */}
                      {selectedProducts.length > 0 && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                            <Package className="h-4 w-4" />
                            <span>Selected Products</span>
                            <span className="text-xs text-muted-foreground font-normal">
                              (Drag to reorder)
                            </span>
                          </div>
                          <DndContext
                            sensors={sensors}
                            collisionDetection={closestCenter}
                            onDragEnd={handleDragEnd}
                          >
                            <SortableContext
                              items={selectedProducts.map(p => p.id)}
                              strategy={verticalListSortingStrategy}
                            >
                              <div className="grid gap-2">
                                {selectedProducts.map((product) => (
                                  <SortableProductItem
                                    key={product.id}
                                    product={product}
                                    onRemove={removeProduct}
                                    formatPrice={formatPrice}
                                  />
                                ))}
                              </div>
                            </SortableContext>
                          </DndContext>
                        </div>
                      )}

                      {/* Product limit indicator */}
                      <div className="flex justify-between items-center text-xs">
                        <span className="text-muted-foreground">
                          {selectedProductIds.length === 0
                            ? "No products selected"
                            : `${selectedProductIds.length} product${selectedProductIds.length !== 1 ? 's' : ''} selected`}
                        </span>
                        <div className="flex items-center gap-1">
                          <span className={cn(
                            "font-medium",
                            selectedProductIds.length >= 5 ? "text-destructive" : "text-muted-foreground"
                          )}>
                            {selectedProductIds.length}/5
                          </span>
                          <span className="text-muted-foreground">max</span>
                        </div>
                      </div>

                      {/* Warning message when limit is reached */}
                      {selectedProductIds.length >= 5 && (
                        <div className="flex items-center gap-2 p-2 bg-destructive/10 text-destructive rounded-md">
                          <Package className="h-4 w-4 shrink-0" />
                          <span className="text-xs font-medium">
                            Maximum limit of 5 products reached
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Action Bar */}
            <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-1">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="business-image-upload"
                />
                <label htmlFor="business-image-upload">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-500 hover:text-[#D4AF37] dark:text-gray-400 dark:hover:text-[#D4AF37] h-9 px-3"
                    asChild
                  >
                    <span>
                      <ImageIcon className="h-5 w-5 mr-1" />
                      <span className="text-sm">Photo</span>
                    </span>
                  </Button>
                </label>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleProductToggle}
                  className="text-gray-500 hover:text-purple-500 dark:text-gray-400 dark:hover:text-purple-400 h-9 px-3"
                >
                  <Package className="h-5 w-5 mr-1" />
                  <span className="text-sm">Products</span>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 h-9 px-3"
                  disabled
                >
                  <MapPin className="h-5 w-5 mr-1" />
                  <span className="text-sm">Location</span>
                </Button>
              </div>

              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || (!content.trim() && !previewUrl && !detectedImageUrl) || isOverLimit}
                className="bg-[#D4AF37] hover:bg-[#B8941F] text-white px-6 py-2 rounded-full font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Posting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Post
                  </>
                )}
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
