import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import { Header } from '../../../../src/components/shared/ui/Header';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

jest.mock('@/src/components/shared/NotificationIcon', () => ({
  NotificationIcon: jest.fn(({ size, color, onPress }) => (
    <mock-NotificationIcon
      testID="notification-icon"
      size={size}
      color={color}
      onPress={onPress}
    />
  )),
}));

jest.mock('@/src/contexts/NotificationContext', () => ({
  useNotifications: jest.fn(() => ({
    unreadCount: 0,
    showModal: jest.fn(),
  })),
}));

describe('Header', () => {
  const mockOnNotificationPress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    require('@/src/contexts/NotificationContext').useNotifications.mockReturnValue({
      unreadCount: 0,
      showModal: jest.fn(),
    });
  });

  it('renders the logo correctly', () => {
    render(<Header />);
    expect(screen.getByText('Dukan')).toBeTruthy();
    expect(screen.getByText('card')).toBeTruthy();
  });

  it('renders NotificationIcon by default', () => {
    render(<Header />);
    expect(screen.getByTestId('notification-icon')).toBeTruthy();
  });

  it('does not render NotificationIcon when showNotifications is false', () => {
    render(<Header showNotifications={false} />);
    expect(screen.queryByTestId('notification-icon')).toBeNull();
  });

  it('calls onNotificationPress when provided', () => {
    render(<Header onNotificationPress={mockOnNotificationPress} />);
    fireEvent.press(screen.getByTestId('notification-icon'));
    expect(mockOnNotificationPress).toHaveBeenCalledTimes(1);
  });

  it('calls showModal from useNotifications when onNotificationPress is not provided', () => {
    const mockShowModal = jest.fn();
    require('@/src/contexts/NotificationContext').useNotifications.mockReturnValue({
      unreadCount: 0,
      showModal: mockShowModal,
    });
    render(<Header />);
    fireEvent.press(screen.getByTestId('notification-icon'));
    expect(mockShowModal).toHaveBeenCalledTimes(1);
  });

  it('renders in dark mode', () => {
    require('@/src/hooks/useColorScheme').useColorScheme.mockReturnValue('dark');
    render(<Header />);
    // Check for dark mode specific styles if they were explicitly set in the mock
    // For now, just ensure it renders without errors in dark mode
    expect(screen.getByText('Dukan')).toBeTruthy();
  });
});
