import { PLAN_PRIORITY, createBusinessPriorityGroups, distributePrioritizedBusinessPosts, getPlanDisplayName, hasPremiumFeatures } from '@/src/utils/feed/planPrioritizer';

// Mock UnifiedPost type for testing
interface MockUnifiedPost {
  id: string;
  author_id: string;
  created_at: string;
  business_plan?: string;
}

describe('planPrioritizer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('PLAN_PRIORITY', () => {
    it('should define correct priority levels', () => {
      expect(PLAN_PRIORITY.enterprise).toBe(5);
      expect(PLAN_PRIORITY.pro).toBe(4);
      expect(PLAN_PRIORITY.growth).toBe(3);
      expect(PLAN_PRIORITY.basic).toBe(2);
      expect(PLAN_PRIORITY.free).toBe(1);
    });
  });

  describe('createBusinessPriorityGroups', () => {
    it('should group posts by author and sort by plan priority and latest post time', () => {
      const businessPosts: MockUnifiedPost[] = [
        { id: 'b1', author_id: 'biz1', created_at: '2023-01-01T12:00:00Z', business_plan: 'pro' },
        { id: 'b2', author_id: 'biz2', created_at: '2023-01-01T11:00:00Z', business_plan: 'enterprise' },
        { id: 'b3', author_id: 'biz1', created_at: '2023-01-01T13:00:00Z', business_plan: 'pro' },
        { id: 'b4', author_id: 'biz3', created_at: '2023-01-01T10:00:00Z', business_plan: 'free' },
      ];

      const groups = createBusinessPriorityGroups(businessPosts);

      expect(groups.length).toBe(3);
      expect(groups[0].authorId).toBe('biz2'); // Enterprise (highest priority)
      expect(groups[1].authorId).toBe('biz1'); // Pro (next priority, latest post)
      expect(groups[2].authorId).toBe('biz3'); // Free (lowest priority)

      // Check posts within groups are sorted by time
      expect(groups[1].posts[0].id).toBe('b3');
      expect(groups[1].posts[1].id).toBe('b1');
    });

    it('should handle posts with no business_plan (default to free)', () => {
      const businessPosts: MockUnifiedPost[] = [
        { id: 'b1', author_id: 'biz1', created_at: '2023-01-01T12:00:00Z' },
      ];
      const groups = createBusinessPriorityGroups(businessPosts);
      expect(groups[0].priority).toBe(PLAN_PRIORITY.free);
    });
  });

  describe('distributePrioritizedBusinessPosts', () => {
    it('should distribute posts in a round-robin fashion based on plan priority', () => {
      const businessGroups = [
        { authorId: 'biz1', priority: PLAN_PRIORITY.enterprise, latestPostTime: 1, posts: [{ id: 'e1' }, { id: 'e2' }] as MockUnifiedPost[] },
        { authorId: 'biz2', priority: PLAN_PRIORITY.pro, latestPostTime: 2, posts: [{ id: 'p1' }, { id: 'p2' }] as MockUnifiedPost[] },
        { authorId: 'biz3', priority: PLAN_PRIORITY.free, latestPostTime: 3, posts: [{ id: 'f1' }] as MockUnifiedPost[] },
      ];

      const distributed = distributePrioritizedBusinessPosts(businessGroups);

      expect(distributed.map(p => p.id)).toEqual([
        'e1', 'p1', 'f1', 'e2', 'p2'
      ]);
    });

    it('should handle empty business groups', () => {
      expect(distributePrioritizedBusinessPosts([])).toEqual([]);
    });
  });

  describe('getPlanDisplayName', () => {
    it('should return correct display name for each plan', () => {
      expect(getPlanDisplayName('enterprise')).toBe('Enterprise');
      expect(getPlanDisplayName('pro')).toBe('Pro');
      expect(getPlanDisplayName('growth')).toBe('Growth');
      expect(getPlanDisplayName('basic')).toBe('Basic');
      expect(getPlanDisplayName('free')).toBe('Free');
    });

    it('should default to Free for unknown plans', () => {
      expect(getPlanDisplayName('unknown')).toBe('Free');
    });
  });

  describe('hasPremiumFeatures', () => {
    it('should return true for growth, pro, and enterprise plans', () => {
      expect(hasPremiumFeatures('growth')).toBe(true);
      expect(hasPremiumFeatures('pro')).toBe(true);
      expect(hasPremiumFeatures('enterprise')).toBe(true);
    });

    it('should return false for basic and free plans', () => {
      expect(hasPremiumFeatures('basic')).toBe(false);
      expect(hasPremiumFeatures('free')).toBe(false);
    });

    it('should return false for unknown plans', () => {
      expect(hasPremiumFeatures('unknown')).toBe(false);
    });
  });
});