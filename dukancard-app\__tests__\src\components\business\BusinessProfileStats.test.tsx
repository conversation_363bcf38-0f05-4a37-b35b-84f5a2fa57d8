import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react-native';
import { BusinessProfileStats } from '@/src/components/business/BusinessProfileStats';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { useIsFocused } from '@react-navigation/native';
import { formatIndianNumberShort } from '@/lib/utils';
import { realtimeService } from '@/backend/supabase/services/realtime/realtimeService';
import { createBusinessProfileStyles } from '@/styles/dashboard/business/business-profile-styles';

// Mock external modules
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: jest.fn(() => 'light'),
}));

jest.mock('@react-navigation/native', () => ({
  useIsFocused: jest.fn(),
}));

jest.mock('@/lib/utils', () => ({
  formatIndianNumberShort: jest.fn((num) => num.toString()), // Simple mock for testing
}));

jest.mock('@/backend/supabase/services/realtime/realtimeService', () => ({
  realtimeService: {
    subscribeToBusinessUpdates: jest.fn(() => ({
      unsubscribe: jest.fn(),
    })),
  },
}));

jest.mock('@/styles/dashboard/business/business-profile-styles', () => ({
  createBusinessProfileStyles: jest.fn(() => ({
    profileStats: {},
    profileStatItem: {},
    statValue: {},
    statLabel: {},
  })),
}));

describe('BusinessProfileStats', () => {
  const mockUseColorScheme = useColorScheme as jest.Mock;
  const mockUseIsFocused = useIsFocused as jest.Mock;
  const mockFormatIndianNumberShort = formatIndianNumberShort as jest.Mock;
  const mockSubscribeToBusinessUpdates = realtimeService.subscribeToBusinessUpdates as jest.Mock;
  const mockCreateBusinessProfileStyles = createBusinessProfileStyles as jest.Mock;
  const mockUnsubscribe = jest.fn();

  const initialProfile = {
    id: 'business-1',
    business_name: 'Test Business',
    total_likes: 12345,
    total_subscriptions: 678,
    average_rating: 4.56,
  };
  const userId = 'user-1';

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseColorScheme.mockReturnValue('light');
    mockUseIsFocused.mockReturnValue(true); // Default to focused
    mockFormatIndianNumberShort.mockImplementation((num) => num.toString());
    mockSubscribeToBusinessUpdates.mockReturnValue({
      unsubscribe: mockUnsubscribe,
    });
    mockCreateBusinessProfileStyles.mockReturnValue({
      profileStats: {},
      profileStatItem: {},
      statValue: {},
      statLabel: {},
    });
  });

  it('renders initial profile stats correctly', () => {
    render(<BusinessProfileStats initialProfile={initialProfile} userId={userId} />);

    const statValues = screen.getAllByTestId('stat-value');
    const statLabels = screen.getAllByTestId('stat-label');

    expect(statValues[0]).toHaveTextContent('12345');
    expect(statLabels[0]).toHaveTextContent('Likes');
    expect(statValues[1]).toHaveTextContent('678');
    expect(statLabels[1]).toHaveTextContent('Subscribers');
    expect(statValues[2]).toHaveTextContent('4.6'); // 4.56.toFixed(1)
    expect(statLabels[2]).toHaveTextContent('Rating');
  });

  it('calls formatIndianNumberShort for likes and subscriptions', () => {
    render(<BusinessProfileStats initialProfile={initialProfile} userId={userId} />);
    expect(mockFormatIndianNumberShort).toHaveBeenCalledWith(initialProfile.total_likes);
    expect(mockFormatIndianNumberShort).toHaveBeenCalledWith(initialProfile.total_subscriptions);
  });

  it('subscribes to real-time updates when focused', () => {
    render(<BusinessProfileStats initialProfile={initialProfile} userId={userId} />);
    expect(mockSubscribeToBusinessUpdates).toHaveBeenCalledWith(userId, expect.any(Function));
  });

  it('unsubscribes from real-time updates when unfocused', () => {
    const { rerender } = render(<BusinessProfileStats initialProfile={initialProfile} userId={userId} />);
    expect(mockSubscribeToBusinessUpdates).toHaveBeenCalledTimes(1);

    mockUseIsFocused.mockReturnValue(false);
    rerender(<BusinessProfileStats initialProfile={initialProfile} userId={userId} />);
    expect(mockUnsubscribe).toHaveBeenCalledTimes(1);
  });

  it('updates profile state on real-time event', async () => {
    let updateCallback: (event: any) => void = () => {};
    mockSubscribeToBusinessUpdates.mockImplementation((id, callback) => {
      updateCallback = callback;
      return { unsubscribe: mockUnsubscribe };
    });

    render(<BusinessProfileStats initialProfile={initialProfile} userId={userId} />);

    const newProfileData = {
      id: 'business-1',
      business_name: 'Updated Business',
      total_likes: 20000,
      total_subscriptions: 1000,
      average_rating: 4.8,
    };

    // Simulate a real-time update
    act(() => {
      updateCallback({ new: newProfileData });
    });

    await waitFor(() => {
      const statValues = screen.getAllByTestId('stat-value');
      expect(statValues[0]).toHaveTextContent('20000');
      expect(statValues[1]).toHaveTextContent('1000');
      expect(statValues[2]).toHaveTextContent('4.8');
    });
  });

  it('cleans up subscription on unmount', () => {
    const { unmount } = render(<BusinessProfileStats initialProfile={initialProfile} userId={userId} />);
    expect(mockSubscribeToBusinessUpdates).toHaveBeenCalledTimes(1);
    unmount();
    expect(mockUnsubscribe).toHaveBeenCalledTimes(1);
  });

  it('applies dark mode styles', () => {
    mockUseColorScheme.mockReturnValue('dark');
    render(<BusinessProfileStats initialProfile={initialProfile} userId={userId} />);
    expect(createBusinessProfileStyles).toHaveBeenCalledWith('dark');
    expect(screen.getByTestId('profile-stats-container')).toHaveStyle({ backgroundColor: '#2a2a2a' });
    expect(screen.getAllByTestId('stat-value')[0]).toHaveStyle({ color: '#D4AF37' });
    expect(screen.getAllByTestId('stat-label')[0]).toHaveStyle({ color: '#999' });
  });

  it('applies light mode styles', () => {
    mockUseColorScheme.mockReturnValue('light');
    render(<BusinessProfileStats initialProfile={initialProfile} userId={userId} />);
    expect(createBusinessProfileStyles).toHaveBeenCalledWith('light');
    expect(screen.getByTestId('profile-stats-container')).toHaveStyle({ backgroundColor: '#F9FAFB' });
    expect(screen.getAllByTestId('stat-value')[0]).toHaveStyle({ color: '#D4AF37' });
    expect(screen.getAllByTestId('stat-label')[0]).toHaveStyle({ color: '#666' });
  });
});