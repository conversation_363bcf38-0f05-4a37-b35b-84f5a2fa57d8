import { 
  processOptimizedHybrid,
  processOptimizedHybridWithInterleaving,
  getOptimizedAlgorithmStats,
  validateOptimizedAlgorithm,
} from '@/src/utils/feed/optimizedHybridAlgorithm';
import { applyDiversityRules } from '@/src/utils/feed/diversityEngine';

// Mock UnifiedPost type for testing
interface MockUnifiedPost {
  id: string;
  author_id: string;
  created_at: string;
  post_source: 'customer' | 'business';
  business_plan?: string;
}

// Mock applyDiversityRules
jest.mock('@/src/utils/feed/diversityEngine', () => ({
  applyDiversityRules: jest.fn((posts) => posts), // Default: return posts as is
}));

describe('optimizedHybridAlgorithm', () => {
  const mockCustomerPosts: MockUnifiedPost[] = [
    { id: 'c1', author_id: 'cust1', created_at: '2023-01-01T12:00:00Z', post_source: 'customer' },
    { id: 'c2', author_id: 'cust2', created_at: '2023-01-01T12:05:00Z', post_source: 'customer' },
    { id: 'c3', author_id: 'cust1', created_at: '2023-01-01T12:10:00Z', post_source: 'customer' },
  ];

  const mockBusinessPosts: MockUnifiedPost[] = [
    { id: 'b1', author_id: 'biz1', created_at: '2023-01-01T12:02:00Z', post_source: 'business', business_plan: 'enterprise' },
    { id: 'b2', author_id: 'biz2', created_at: '2023-01-01T12:07:00Z', post_source: 'business', business_plan: 'pro' },
    { id: 'b3', author_id: 'biz1', created_at: '2023-01-01T12:12:00Z', post_source: 'business', business_plan: 'enterprise' },
    { id: 'b4', author_id: 'biz3', created_at: '2023-01-01T11:00:00Z', post_source: 'business', business_plan: 'free' },
  ];

  const allMockPosts = [...mockCustomerPosts, ...mockBusinessPosts];

  beforeEach(() => {
    jest.clearAllMocks();
    (applyDiversityRules as jest.Mock).mockImplementation((posts) => posts); // Reset mock to default behavior
  });

  describe('processOptimizedHybrid', () => {
    it('should return an empty array for empty input', () => {
      expect(processOptimizedHybrid([])).toEqual([]);
    });

    it('should process and merge posts with diversity enabled', () => {
      const result = processOptimizedHybrid(allMockPosts);
      expect(applyDiversityRules).toHaveBeenCalledTimes(1);
      expect(result.length).toBe(allMockPosts.length);
    });

    it('should process and merge posts with diversity disabled', () => {
      const result = processOptimizedHybrid(allMockPosts, { enableDiversity: false });
      expect(applyDiversityRules).not.toHaveBeenCalled();
      expect(result.length).toBe(allMockPosts.length);
    });

    it('should handle only customer posts', () => {
      const result = processOptimizedHybrid(mockCustomerPosts);
      expect(result.length).toBe(mockCustomerPosts.length);
      expect(result[0].id).toBe('c3'); // Latest customer post
    });

    it('should handle only business posts', () => {
      const result = processOptimizedHybrid(mockBusinessPosts);
      expect(result.length).toBe(mockBusinessPosts.length);
      expect(result[0].id).toBe('b3'); // Latest business post
    });
  });

  describe('processOptimizedHybridWithInterleaving', () => {
    it('should interleave posts with diversity enabled', () => {
      const result = processOptimizedHybridWithInterleaving(allMockPosts);
      expect(applyDiversityRules).toHaveBeenCalledTimes(1);
      expect(result.length).toBe(allMockPosts.length);
    });

    it('should interleave posts with diversity disabled', () => {
      const result = processOptimizedHybridWithInterleaving(allMockPosts, { enableDiversity: false });
      expect(applyDiversityRules).not.toHaveBeenCalled();
      expect(result.length).toBe(allMockPosts.length);
    });
  });

  describe('getOptimizedAlgorithmStats', () => {
    it('should return correct statistics', () => {
      const processedPosts: MockUnifiedPost[] = [
        { id: 'c1', author_id: 'cust1', created_at: '2023-01-01T12:00:00Z', post_source: 'customer' },
        { id: 'b1', author_id: 'biz1', created_at: '2023-01-01T12:02:00Z', post_source: 'business', business_plan: 'enterprise' },
        { id: 'b2', author_id: 'biz2', created_at: '2023-01-01T12:07:00Z', post_source: 'business', business_plan: 'pro' },
      ];
      const stats = getOptimizedAlgorithmStats(allMockPosts, processedPosts);
      expect(stats.originalCount).toBe(allMockPosts.length);
      expect(stats.processedCount).toBe(processedPosts.length);
      expect(stats.customerPosts).toBe(1);
      expect(stats.businessPosts).toBe(2);
      expect(stats.planDistribution).toEqual({ enterprise: 1, pro: 1 });
      expect(stats.postsLost).toBe(allMockPosts.length - processedPosts.length);
      expect(stats.efficiency).toBeCloseTo(processedPosts.length / allMockPosts.length);
    });
  });

  describe('validateOptimizedAlgorithm', () => {
    it('should return isValid true if no posts are lost and counts match', () => {
      const original = [{ id: '1' }, { id: '2' }] as MockUnifiedPost[];
      const processed = [{ id: '1' }, { id: '2' }] as MockUnifiedPost[];
      const result = validateOptimizedAlgorithm(original, processed);
      expect(result.isValid).toBe(true);
      expect(result.issues).toEqual([]);
    });

    it('should return isValid false and issues if post count mismatch', () => {
      const original = [{ id: '1' }, { id: '2' }, { id: '3' }] as MockUnifiedPost[];
      const processed = [{ id: '1' }, { id: '3' }] as MockUnifiedPost[];
      const result = validateOptimizedAlgorithm(original, processed);
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Post count mismatch: 3 → 2');
      expect(result.issues).toContain('Lost posts: 2');
    });

    it('should return isValid false and issues if posts are lost', () => {
      const original = [{ id: '1' }, { id: '2' }] as MockUnifiedPost[];
      const processed = [{ id: '1' }] as MockUnifiedPost[];
      const result = validateOptimizedAlgorithm(original, processed);
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Post count mismatch: 2 → 1');
      expect(result.issues).toContain('Lost posts: 2');
    });
  });
});