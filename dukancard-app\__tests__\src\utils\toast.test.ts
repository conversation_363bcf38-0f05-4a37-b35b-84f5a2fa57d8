import { Toast, setGlobalToastInstance } from '@/src/utils/toast';

describe('Toast Utility', () => {
  let mockToastInstance: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockToastInstance = {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
      info: jest.fn(),
    };
    setGlobalToastInstance(mockToastInstance);
  });

  it('should call success method on globalToastInstance for success type', () => {
    Toast.show('Success message', 'success');
    expect(mockToastInstance.success).toHaveBeenCalledWith('Success message');
  });

  it('should call error method on globalToastInstance for error type', () => {
    Toast.show('Error message', 'error');
    expect(mockToastInstance.error).toHaveBeenCalledWith('Error message');
  });

  it('should call warning method on globalToastInstance for warning type', () => {
    Toast.show('Warning message', 'warning');
    expect(mockToastInstance.warning).toHaveBeenCalledWith('Warning message');
  });

  it('should call info method on globalToastInstance for info type', () => {
    Toast.show('Info message', 'info');
    expect(mockToastInstance.info).toHaveBeenCalledWith('Info message');
  });

  it('should call info method on globalToastInstance for default type', () => {
    Toast.show('Default message');
    expect(mockToastInstance.info).toHaveBeenCalledWith('Default message');
  });

  it('should call success method directly', () => {
    Toast.success('Direct success');
    expect(mockToastInstance.success).toHaveBeenCalledWith('Direct success');
  });

  it('should call error method directly', () => {
    Toast.error('Direct error');
    expect(mockToastInstance.error).toHaveBeenCalledWith('Direct error');
  });

  it('should call warning method directly', () => {
    Toast.warning('Direct warning');
    expect(mockToastInstance.warning).toHaveBeenCalledWith('Direct warning');
  });

  it('should call info method directly', () => {
    Toast.info('Direct info');
    expect(mockToastInstance.info).toHaveBeenCalledWith('Direct info');
  });

  it('should not throw error if globalToastInstance is null', () => {
    setGlobalToastInstance(null);
    expect(() => Toast.show('No instance')).not.toThrow();
  });
});