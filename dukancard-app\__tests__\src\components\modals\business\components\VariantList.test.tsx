import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import VariantList from '@/src/components/modals/business/components/VariantList';
import { useTheme } from '@/src/hooks/useTheme';
import { useToast } from '@/src/components/ui/Toast';
import { getProductVariants, deleteProductVariant } from '@/backend/supabase/services/business/variantService';
import { Alert } from 'react-native';

// Mock dependencies
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: () => ({
    colors: {
      primary: '#C29D5B',
      textPrimary: '#000',
      textSecondary: '#888',
      mutedForeground: '#aaa',
      success: 'green',
      destructive: 'red',
    },
  }),
}));

jest.mock('@/src/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
  }),
}));

jest.mock('@/backend/supabase/services/business/variantService', () => ({
  getProductVariants: jest.fn(),
  deleteProductVariant: jest.fn(),
}));

jest.spyOn(Alert, 'alert');

describe('VariantList', () => {
  const mockProductId = 'product123';
  const mockOnAddVariant = jest.fn();
  const mockOnEditVariant = jest.fn();

  const mockVariants = [
    {
      id: 'var1',
      variant_name: 'Red Small',
      variant_values: { Color: 'Red', Size: 'Small' },
      base_price: 100,
      discounted_price: 90,
      is_available: true,
      images: ['http://example.com/red_small.jpg'],
      featured_image_index: 0,
    },
    {
      id: 'var2',
      variant_name: 'Blue Large',
      variant_values: { Color: 'Blue', Size: 'Large' },
      base_price: 150,
      discounted_price: null,
      is_available: false,
      images: [],
      featured_image_index: 0,
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (getProductVariants as jest.Mock).mockResolvedValue({ success: true, data: mockVariants });
    (deleteProductVariant as jest.Mock).mockResolvedValue({ success: true });
  });

  it('renders correctly with variants', async () => {
    const { getByText } = render(
      <VariantList
        productId={mockProductId}
        onAddVariant={mockOnAddVariant}
        onEditVariant={mockOnEditVariant}
      />
    );

    await waitFor(() => {
      expect(getByText('Red Small')).toBeTruthy();
      expect(getByText('Blue Large')).toBeTruthy();
      expect(getByText('Color: Red, Size: Small')).toBeTruthy();
      expect(getByText('Available')).toBeTruthy();
      expect(getByText('Unavailable')).toBeTruthy();
    });
  });

  it('shows loading state initially', () => {
    (getProductVariants as jest.Mock).mockReturnValueOnce(new Promise(() => {})); // Never resolve
    const { getByText } = render(
      <VariantList
        productId={mockProductId}
        onAddVariant={mockOnAddVariant}
        onEditVariant={mockOnEditVariant}
      />
    );
    expect(getByText('Loading variants...')).toBeTruthy();
  });

  it('renders empty state when no variants are found', async () => {
    (getProductVariants as jest.Mock).mockResolvedValue({ success: true, data: [] });
    const { getByText } = render(
      <VariantList
        productId={mockProductId}
        onAddVariant={mockOnAddVariant}
        onEditVariant={mockOnEditVariant}
      />
    );

    await waitFor(() => {
      expect(getByText('No Variants')).toBeTruthy();
      expect(getByText('Add First Variant')).toBeTruthy();
    });
  });

  it('calls onAddVariant when Add Variant button is pressed', async () => {
    const { getByText } = render(
      <VariantList
        productId={mockProductId}
        onAddVariant={mockOnAddVariant}
        onEditVariant={mockOnEditVariant}
      />
    );

    await waitFor(() => {
      fireEvent.press(getByText('Add Variant'));
    });
    expect(mockOnAddVariant).toHaveBeenCalled();
  });

  it('calls onEditVariant when edit button is pressed', async () => {
    const { getAllByTestId } = render(
      <VariantList
        productId={mockProductId}
        onAddVariant={mockOnAddVariant}
        onEditVariant={mockOnEditVariant}
      />
    );

    await waitFor(() => {
      fireEvent.press(getAllByTestId('edit-button')[0]);
    });
    expect(mockOnEditVariant).toHaveBeenCalledWith(mockVariants[0]);
  });

  it('handles variant deletion successfully', async () => {
    const { getAllByTestId, queryByText } = render(
      <VariantList
        productId={mockProductId}
        onAddVariant={mockOnAddVariant}
        onEditVariant={mockOnEditVariant}
      />
    );

    await waitFor(() => {
      fireEvent.press(getAllByTestId('delete-button')[0]);
    });

    // Simulate Alert.alert confirmation
    expect(Alert.alert).toHaveBeenCalledWith(
      'Delete Variant',
      `Are you sure you want to delete "${mockVariants[0].variant_name}"? This action cannot be undone.`,
      expect.arrayContaining([
        expect.objectContaining({ text: 'Delete', style: 'destructive' }),
      ]),
    );

    // Manually trigger the onPress for the 'Delete' button in the mock Alert
    const deleteButton = (Alert.alert as jest.Mock).mock.calls[0][2].find(
      (btn: any) => btn.text === 'Delete',
    );
    await act(async () => {
      deleteButton.onPress();
    });

    expect(deleteProductVariant).toHaveBeenCalledWith(mockVariants[0].id);
    expect(useToast().success).toHaveBeenCalledWith('Success', 'Variant deleted successfully');
    await waitFor(() => expect(queryByText('Red Small')).toBeNull());
  });

  it('shows error toast if variant deletion fails', async () => {
    (deleteProductVariant as jest.Mock).mockResolvedValue({ success: false, error: 'Delete failed' });

    const { getAllByTestId } = render(
      <VariantList
        productId={mockProductId}
        onAddVariant={mockOnAddVariant}
        onEditVariant={mockOnEditVariant}
      />
    );

    await waitFor(() => {
      fireEvent.press(getAllByTestId('delete-button')[0]);
    });

    const deleteButton = (Alert.alert as jest.Mock).mock.calls[0][2].find(
      (btn: any) => btn.text === 'Delete',
    );
    await act(async () => {
      deleteButton.onPress();
    });

    expect(useToast().error).toHaveBeenCalledWith('Error', 'Delete failed');
  });
});
