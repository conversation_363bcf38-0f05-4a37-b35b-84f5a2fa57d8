import { StyleSheet } from 'react-native';
import { Theme } from '@/src/hooks/useTheme';

export const createSubscriptionCardStyles = (theme: Theme) => {
  return StyleSheet.create({
    listItem: {
      backgroundColor: theme.isDark ? '#000000' : theme.colors.card,
    },
    listContent: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      minHeight: 72,
    },
    logoContainer: {
      marginRight: 12,
    },
    logo: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    logoPlaceholder: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: '#D4AF37',
      justifyContent: 'center',
      alignItems: 'center',
    },
    logoText: {
      color: '#fff',
      fontSize: 16,
      fontWeight: 'bold',
    },
    businessInfo: {
      flex: 1,
      marginRight: 12,
    },
    businessName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.textPrimary,
      marginBottom: 4,
    },
    businessAddress: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 6,
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    statusDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#4CAF50',
      marginRight: 6,
    },
    statusText: {
      fontSize: 12,
      color: '#4CAF50',
      fontWeight: '500',
    },
    unsubscribeButton: {
      padding: 8,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 20,
      backgroundColor: 'rgba(0, 122, 255, 0.1)',
    },
    unsubscribeButtonDisabled: {
      backgroundColor: 'rgba(204, 204, 204, 0.1)',
    },
    separator: {
      height: 1,
      backgroundColor: theme.colors.border,
      marginLeft: 72, // Align with text content (logo width + margin)
    },
  });
};