import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react-native';
import { ThemeToggleButton } from '@/src/components/ui/ThemeToggleButton';
import { useThemeContext } from '@/src/contexts/ThemeContext';
import { Sun, Moon, Monitor, Check } from 'lucide-react-native';

// Mock necessary modules
jest.mock('@/src/contexts/ThemeContext');
jest.mock('lucide-react-native', () => ({
  Sun: jest.fn(() => null),
  Moon: jest.fn(() => null),
  Monitor: jest.fn(() => null),
  Check: jest.fn(() => null),
}));

describe('ThemeToggleButton', () => {
  const mockSetThemeMode = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useThemeContext as jest.Mock).mockReturnValue({
      themeMode: 'light',
      setThemeMode: mockSetThemeMode,
      isDark: false,
    });
    (Sun as jest.Mock).mockImplementation(() => <Text testID="sun-icon">SunIcon</Text>);
    (Moon as jest.Mock).mockImplementation(() => <Text testID="moon-icon">MoonIcon</Text>);
    (Monitor as jest.Mock).mockImplementation(() => <Text testID="monitor-icon">MonitorIcon</Text>);
    (Check as jest.Mock).mockImplementation(() => <Text testID="check-icon">CheckIcon</Text>);
  });

  it('renders correctly with default props', () => {
    render(<ThemeToggleButton />);
    expect(screen.getByTestId('moon-icon')).toBeOnTheScreen(); // Default is light, so Moon icon
  });

  it('opens modal on button press', () => {
    render(<ThemeToggleButton />);
    fireEvent.press(screen.getByTestId('theme-toggle-button')); // Assuming a testID for the TouchableOpacity
    expect(screen.getByText('Choose Theme')).toBeOnTheScreen();
    expect(screen.getByText('System')).toBeOnTheScreen();
    expect(screen.getByText('Light')).toBeOnTheScreen();
    expect(screen.getByText('Dark')).toBeOnTheScreen();
  });

  it('closes modal on overlay press', () => {
    render(<ThemeToggleButton />);
    fireEvent.press(screen.getByTestId('theme-toggle-button')); // Open modal
    fireEvent.press(screen.getByTestId('modal-overlay')); // Assuming a testID for the Pressable overlay
    expect(screen.queryByText('Choose Theme')).toBeNull();
  });

  it('calls setThemeMode and closes modal on theme option select', () => {
    render(<ThemeToggleButton />);
    fireEvent.press(screen.getByTestId('theme-toggle-button')); // Open modal
    fireEvent.press(screen.getByText('Dark'));

    expect(mockSetThemeMode).toHaveBeenCalledWith('dark');
    expect(screen.queryByText('Choose Theme')).toBeNull();
  });

  it('displays checkmark for selected theme', () => {
    (useThemeContext as jest.Mock).mockReturnValue({
      themeMode: 'dark',
      setThemeMode: mockSetThemeMode,
      isDark: true,
    });
    render(<ThemeToggleButton />);
    fireEvent.press(screen.getByTestId('theme-toggle-button')); // Open modal
    expect(screen.getByTestId('check-icon')).toBeOnTheScreen(); // Checkmark for Dark theme
  });

  it('applies profile variant styles', () => {
    render(<ThemeToggleButton variant="profile" showLabel={true} />);
    expect(screen.getByText('Light')).toBeOnTheScreen(); // Label should be visible
    expect(screen.getByTestId('theme-toggle-button').props.style).toContainEqual({
      flexDirection: 'row',
    });
  });

  it('applies dark mode styles', () => {
    (useThemeContext as jest.Mock).mockReturnValue({
      themeMode: 'dark',
      setThemeMode: mockSetThemeMode,
      isDark: true,
    });
    render(<ThemeToggleButton />);
    expect(screen.getByTestId('sun-icon')).toBeOnTheScreen(); // Dark mode, so Sun icon
    fireEvent.press(screen.getByTestId('theme-toggle-button')); // Open modal
    expect(screen.getByText('Choose Theme').props.style).toContainEqual({
      color: '#fff',
    });
  });
});