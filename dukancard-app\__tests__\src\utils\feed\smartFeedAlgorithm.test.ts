import { 
  processSmartFeed,
  paginateSmartFeed,
  getFeedStats,
  validateFeedQuality,
} from '@/src/utils/feed/smartFeedAlgorithm';
import { applyDiversityRules } from '@/src/utils/feed/diversityEngine';
import { createBusinessPriorityGroups, distributePrioritizedBusinessPosts } from '@/src/utils/feed/planPrioritizer';
import { mergeCustomerAndBusinessPosts } from '@/src/utils/feed/feedMerger';

// Mock UnifiedPost type for testing
interface MockUnifiedPost {
  id: string;
  author_id: string;
  created_at: string;
  post_source: 'customer' | 'business';
  business_plan?: string;
}

// Mock external dependencies
jest.mock('@/src/utils/feed/diversityEngine', () => ({
  applyDiversityRules: jest.fn((posts) => posts), // Default: return posts as is
}));
jest.mock('@/src/utils/feed/planPrioritizer', () => ({
  createBusinessPriorityGroups: jest.fn((posts) => {
    // Simple mock: group by author and assign a dummy priority
    const grouped = new Map();
    posts.forEach((p: MockUnifiedPost) => {
      if (!grouped.has(p.author_id)) {
        grouped.set(p.author_id, []);
      }
      grouped.get(p.author_id).push(p);
    });
    return Array.from(grouped.entries()).map(([authorId, authorPosts]) => ({
      authorId,
      priority: authorId === 'biz1' ? 5 : 1, // Example priority
      latestPostTime: new Date((authorPosts as MockUnifiedPost[])[0].created_at).getTime(),
      posts: authorPosts,
    }));
  }),
  distributePrioritizedBusinessPosts: jest.fn((groups) => {
    // Simple mock: flatten groups
    return groups.flatMap((g: any) => g.posts);
  }),
  PLAN_PRIORITY: {
    'enterprise': 5,
    'pro': 4,
    'growth': 3,
    'basic': 2,
    'free': 1
  },
}));
jest.mock('@/src/utils/feed/feedMerger', () => ({
  mergeCustomerAndBusinessPosts: jest.fn((customerPosts, businessPosts) => {
    // Simple mock: concatenate and sort by created_at
    return [...customerPosts, ...businessPosts].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }),
}));

describe('smartFeedAlgorithm', () => {
  const mockCustomerPosts: MockUnifiedPost[] = [
    { id: 'c1', author_id: 'cust1', created_at: '2023-01-01T12:00:00Z', post_source: 'customer' },
    { id: 'c2', author_id: 'cust2', created_at: '2023-01-01T12:05:00Z', post_source: 'customer' },
  ];

  const mockBusinessPosts: MockUnifiedPost[] = [
    { id: 'b1', author_id: 'biz1', created_at: '2023-01-01T12:02:00Z', post_source: 'business', business_plan: 'enterprise' },
    { id: 'b2', author_id: 'biz2', created_at: '2023-01-01T12:07:00Z', post_source: 'business', business_plan: 'pro' },
  ];

  const allMockPosts = [...mockCustomerPosts, ...mockBusinessPosts];

  beforeEach(() => {
    jest.clearAllMocks();
    (applyDiversityRules as jest.Mock).mockImplementation((posts) => posts); // Reset mock to default behavior
  });

  describe('processSmartFeed', () => {
    it('should return an empty array for empty input', () => {
      expect(processSmartFeed([])).toEqual([]);
    });

    it('should process and merge posts with default options', () => {
      const result = processSmartFeed(allMockPosts);
      expect(applyDiversityRules).toHaveBeenCalledTimes(1);
      expect(mergeCustomerAndBusinessPosts).toHaveBeenCalledTimes(1);
      expect(result.length).toBe(allMockPosts.length);
    });

    it('should not apply diversity rules if disabled', () => {
      processSmartFeed(allMockPosts, { enableDiversity: false });
      expect(applyDiversityRules).not.toHaveBeenCalled();
    });

    it('should handle only customer posts', () => {
      const result = processSmartFeed(mockCustomerPosts);
      expect(result.length).toBe(mockCustomerPosts.length);
      expect(result[0].id).toBe('c2'); // Latest customer post
    });

    it('should handle only business posts', () => {
      const result = processSmartFeed(mockBusinessPosts);
      expect(result.length).toBe(mockBusinessPosts.length);
      expect(result[0].id).toBe('b2'); // Latest business post
    });
  });

  describe('paginateSmartFeed', () => {
    it('should paginate the processed feed correctly', () => {
      const processedPosts: MockUnifiedPost[] = [
        { id: '1', author_id: 'A', created_at: '' , post_source: 'customer'},
        { id: '2', author_id: 'B', created_at: '' , post_source: 'customer'},
        { id: '3', author_id: 'C', created_at: '' , post_source: 'customer'},
        { id: '4', author_id: 'D', created_at: '' , post_source: 'customer'},
      ];
      expect(paginateSmartFeed(processedPosts, 1, 2)).toEqual([
        { id: '1', author_id: 'A', created_at: '' , post_source: 'customer'},
        { id: '2', author_id: 'B', created_at: '' , post_source: 'customer'},
      ]);
      expect(paginateSmartFeed(processedPosts, 2, 2)).toEqual([
        { id: '3', author_id: 'C', created_at: '' , post_source: 'customer'},
        { id: '4', author_id: 'D', created_at: '' , post_source: 'customer'},
      ]);
      expect(paginateSmartFeed(processedPosts, 3, 2)).toEqual([]);
    });
  });

  describe('getFeedStats', () => {
    it('should return correct statistics for the feed', () => {
      const posts: MockUnifiedPost[] = [
        { id: 'c1', author_id: 'cust1', created_at: '' , post_source: 'customer'},
        { id: 'b1', author_id: 'biz1', created_at: '' , post_source: 'business', business_plan: 'enterprise' },
        { id: 'b2', author_id: 'biz2', created_at: '' , post_source: 'business', business_plan: 'pro' },
        { id: 'c2', author_id: 'cust2', created_at: '' , post_source: 'customer'},
        { id: 'b3', author_id: 'biz1', created_at: '' , post_source: 'business', business_plan: 'enterprise' },
      ];
      const stats = getFeedStats(posts);
      expect(stats.totalPosts).toBe(5);
      expect(stats.customerPosts).toBe(2);
      expect(stats.businessPosts).toBe(3);
      expect(stats.planDistribution).toEqual({ enterprise: 2, pro: 1 });
      expect(stats.uniqueAuthors).toBe(4); // cust1, cust2, biz1, biz2
    });
  });

  describe('validateFeedQuality', () => {
    it('should return isValid true if no more than 2 consecutive posts from same author', () => {
      const posts: MockUnifiedPost[] = [
        { id: '1', author_id: 'A', created_at: '' , post_source: 'customer'},
        { id: '2', author_id: 'A', created_at: '' , post_source: 'customer'},
        { id: '3', author_id: 'B', created_at: '' , post_source: 'customer'},
        { id: '4', author_id: 'A', created_at: '' , post_source: 'customer'},
      ];
      const result = validateFeedQuality(posts);
      expect(result.isValid).toBe(true);
      expect(result.issues).toEqual([]);
    });

    it('should return isValid false if more than 2 consecutive posts from same author', () => {
      const posts: MockUnifiedPost[] = [
        { id: '1', author_id: 'A', created_at: '' , post_source: 'customer'},
        { id: '2', author_id: 'A', created_at: '' , post_source: 'customer'},
        { id: '3', author_id: 'A', created_at: '' , post_source: 'customer'},
        { id: '4', author_id: 'B', created_at: '' , post_source: 'customer'},
      ];
      const result = validateFeedQuality(posts);
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Found 3 consecutive posts from same author');
    });

    it('should calculate diversity score', () => {
      const posts: MockUnifiedPost[] = [
        { id: '1', author_id: 'A', created_at: '' , post_source: 'customer'},
        { id: '2', author_id: 'A', created_at: '' , post_source: 'customer'},
        { id: '3', author_id: 'B', created_at: '' , post_source: 'customer'},
      ];
      const result = validateFeedQuality(posts);
      // maxConsecutive = 2, posts.length = 3
      // diversityScore = 1 - (2-1)/3 = 1 - 1/3 = 0.666...
      expect(result.diversityScore).toBeCloseTo(0.666);
    });
  });
});