import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import { FormField } from '@/src/components/ui/FormField';
import { useColorScheme } from '@/src/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';

// Mock necessary modules
jest.mock('@/src/hooks/useColorScheme');
jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons', // Mock Ionicons as a simple string
}));

jest.useFakeTimers();

describe('FormField', () => {
  const mockValidationFunction = jest.fn((value: string) => {
    if (value.length < 5) {
      return { isValid: false, error: 'Too short' };
    }
    return { isValid: true, error: undefined };
  });
  const mockOnChangeText = jest.fn();
  const mockOnValidationChange = jest.fn();
  const mockOnBlur = jest.fn();
  const mockOnFocus = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useColorScheme as jest.Mock).mockReturnValue('light');
  });

  it('renders correctly with a label and default type', () => {
    render(<FormField label="Username" />);
    expect(screen.getByText('Username')).toBeOnTheScreen();
    expect(screen.getByRole('textbox')).toBeOnTheScreen();
  });

  it('handles text input and calls onChangeText', () => {
    render(<FormField onChangeText={mockOnChangeText} />);
    fireEvent.changeText(screen.getByRole('textbox'), 'test input');
    expect(mockOnChangeText).toHaveBeenCalledWith('test input');
  });

  it('applies correct keyboardType for email', () => {
    render(<FormField type="email" />);
    expect(screen.getByRole('textbox').props.keyboardType).toBe('email-address');
  });

  it('toggles password visibility', () => {
    render(<FormField type="password" />);
    const passwordInput = screen.getByRole('textbox');
    expect(passwordInput.props.secureTextEntry).toBe(true);

    fireEvent.press(screen.getByLabelText('toggle-password-visibility')); // Assuming accessibilityLabel
    expect(passwordInput.props.secureTextEntry).toBe(false);

    fireEvent.press(screen.getByLabelText('toggle-password-visibility'));
    expect(passwordInput.props.secureTextEntry).toBe(true);
  });

  it('validates on change with debounce', async () => {
    render(
      <FormField
        onChangeText={mockOnChangeText}
        validationFunction={mockValidationFunction}
        validateOnChange={true}
        debounceMs={100}
      />
    );
    const input = screen.getByRole('textbox');

    fireEvent.changeText(input, 'abc');
    expect(mockValidationFunction).not.toHaveBeenCalled();
    jest.advanceTimersByTime(50);
    expect(mockValidationFunction).not.toHaveBeenCalled();
    jest.advanceTimersByTime(50);
    expect(mockValidationFunction).toHaveBeenCalledWith('abc');

    fireEvent.changeText(input, 'abcdef');
    jest.advanceTimersByTime(100);
    expect(mockValidationFunction).toHaveBeenCalledWith('abcdef');
  });

  it('validates on blur', () => {
    render(
      <FormField
        value="short"
        onChangeText={mockOnChangeText}
        validationFunction={mockValidationFunction}
        validateOnBlur={true}
      />
    );
    const input = screen.getByRole('textbox');

    fireEvent(input, 'blur');
    expect(mockValidationFunction).toHaveBeenCalledWith('short');
    expect(screen.getByText('Too short')).toBeOnTheScreen();
  });

  it('displays external error prop', () => {
    render(<FormField error="External Error" />);
    expect(screen.getByText('External Error')).toBeOnTheScreen();
  });

  it('displays validation icon for valid input', () => {
    render(
      <FormField
        value="valid input"
        onChangeText={mockOnChangeText}
        validationFunction={mockValidationFunction}
        validateOnChange={true}
        showValidationIcon={true}
      />
    );
    fireEvent.changeText(screen.getByRole('textbox'), 'valid input');
    jest.runAllTimers();
    expect(screen.getByLabelText('checkmark-circle')).toBeOnTheScreen();
  });

  it('displays validation icon for invalid input', () => {
    render(
      <FormField
        value="short"
        onChangeText={mockOnChangeText}
        validationFunction={mockValidationFunction}
        validateOnChange={true}
        showValidationIcon={true}
      />
    );
    fireEvent.changeText(screen.getByRole('textbox'), 'short');
    jest.runAllTimers();
    expect(screen.getByLabelText('close-circle')).toBeOnTheScreen();
  });

  it('displays helper text', () => {
    render(<FormField helperText="Some helpful text" />);
    expect(screen.getByText('Some helpful text')).toBeOnTheScreen();
  });

  it('displays character count when maxLength is provided', () => {
    render(<FormField maxLength={10} value="abc" />);
    expect(screen.getByText('3/10')).toBeOnTheScreen();
  });

  it('applies dark mode styles', () => {
    (useColorScheme as jest.Mock).mockReturnValue('dark');
    render(<FormField label="Test" />);
    // Check for dark mode specific styles (e.g., text color)
    expect(screen.getByText('Test').props.style).toContainEqual({
      color: '#f2f2f2',
    });
  });
});