import React from 'react';
import { render, screen, waitFor } from '@testing-library/react-native';
import { LocationProvider, useLocation, calculateDistanceFromCurrent } from '@/src/contexts/LocationContext';
import { useAuth } from '@/src/contexts/AuthContext';
import * as Location from 'expo-location';
import { requestLocationPermission, getCurrentLocation } from '@/backend/supabase/services/location/locationService';

// Mock all external dependencies
jest.mock('@/src/contexts/AuthContext');
jest.mock('expo-location');
jest.mock('@/backend/supabase/services/location/locationService');

describe('LocationContext', () => {
  const mockUser = { id: 'user123' };
  const mockLocationCoords = { latitude: 10, longitude: 20, timestamp: Date.now() };

  beforeEach(() => {
    jest.clearAllMocks();
    (useAuth as jest.Mock).mockReturnValue({ user: mockUser });
    (requestLocationPermission as jest.Mock).mockResolvedValue({ granted: true, canAskAgain: true });
    (getCurrentLocation as jest.Mock).mockResolvedValue({ coords: { latitude: 10, longitude: 20 } });
  });

  const TestComponent = () => {
    const context = useLocation();
    return (
      <>
        <Text testID="current-location">{context.currentLocation ? `${context.currentLocation.latitude},${context.currentLocation.longitude}` : 'N/A'}</Text>
        <Text testID="is-location-loading">{context.isLocationLoading.toString()}</Text>
        <Text testID="location-permission-granted">{context.locationPermissionGranted.toString()}</Text>
        <Text testID="location-error">{context.locationError || 'N/A'}</Text>
        <button onPress={() => context.refreshLocation()}>Refresh Location</button>
        <button onPress={() => context.requestLocationAccess()}>Request Access</button>
      </>
    );
  };

  it('initializes location for authenticated user', async () => {
    render(
      <LocationProvider>
        <TestComponent />
      </LocationProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('current-location').props.children).toBe('10,20');
      expect(screen.getByTestId('is-location-loading').props.children).toBe('false');
      expect(screen.getByTestId('location-permission-granted').props.children).toBe('true');
      expect(screen.getByTestId('location-error').props.children).toBe('N/A');
    });
  });

  it('does not fetch location if user is not authenticated', async () => {
    (useAuth as jest.Mock).mockReturnValue({ user: null });

    render(
      <LocationProvider>
        <TestComponent />
      </LocationProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('current-location').props.children).toBe('N/A');
      expect(screen.getByTestId('is-location-loading').props.children).toBe('false');
      expect(getCurrentLocation).not.toHaveBeenCalled();
    });
  });

  it('refreshes location on refreshLocation call', async () => {
    render(
      <LocationProvider>
        <TestComponent />
      </LocationProvider>
    );

    await waitFor(() => expect(screen.getByTestId('current-location').props.children).toBe('10,20'));

    (getCurrentLocation as jest.Mock).mockResolvedValueOnce({ coords: { latitude: 30, longitude: 40 } });
    fireEvent.press(screen.getByText('Refresh Location'));

    await waitFor(() => {
      expect(screen.getByTestId('current-location').props.children).toBe('30,40');
    });
  });

  it('requests location access on requestLocationAccess call', async () => {
    (requestLocationPermission as jest.Mock).mockResolvedValueOnce({ granted: false, canAskAgain: true });

    render(
      <LocationProvider>
        <TestComponent />
      </LocationProvider>
    );

    fireEvent.press(screen.getByText('Request Access'));

    await waitFor(() => {
      expect(requestLocationPermission).toHaveBeenCalledTimes(2); // Initial + explicit request
      expect(screen.getByTestId('location-permission-granted').props.children).toBe('false');
      expect(screen.getByTestId('location-error').props.children).toBe('Location permission not granted');
    });
  });

  it('handles location fetching error', async () => {
    (getCurrentLocation as jest.Mock).mockRejectedValue(new Error('GPS Error'));

    render(
      <LocationProvider>
        <TestComponent />
      </LocationProvider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('location-error').props.children).toBe('GPS Error');
      expect(screen.getByTestId('current-location').props.children).toBe('N/A');
    });
  });

  it('throws error if useLocation is used outside provider', () => {
    expect(() => render(<useLocation />)).toThrow(
      'useLocation must be used within a LocationProvider'
    );
  });
});

describe('calculateDistanceFromCurrent', () => {
  it('calculates distance correctly', () => {
    const currentLocation = { latitude: 0, longitude: 0, timestamp: Date.now() };
    const targetLatitude = 0.008993216;
    const targetLongitude = 0.008993216;
    const distance = calculateDistanceFromCurrent(currentLocation, targetLatitude, targetLongitude);
    // Expected distance for 1km is approximately 0.008993216 degrees latitude/longitude
    // Using a small epsilon for floating point comparison
    expect(distance).toBeCloseTo(1.414, 2); // Approx. 1.414 km for these coordinates
  });

  it('returns null if currentLocation is null', () => {
    const distance = calculateDistanceFromCurrent(null, 10, 20);
    expect(distance).toBeNull();
  });
});