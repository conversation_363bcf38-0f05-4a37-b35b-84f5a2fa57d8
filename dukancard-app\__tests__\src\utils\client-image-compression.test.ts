import { compressImageClientSide, compressImageUltraAggressiveClient, compressImageModerateClient, compressFileUltraAggressive } from '@/src/utils/client-image-compression';
import * as ImageManipulator from 'expo-image-manipulator';

// Mock external dependencies
jest.mock('expo-image-manipulator');

describe('client-image-compression', () => {
  const mockImageUri = 'file://mock/image.jpg';
  const mockOriginalSize = 1000000; // 1MB

  beforeEach(() => {
    jest.clearAllMocks();
    (ImageManipulator.manipulateAsync as jest.Mock).mockImplementation((uri, actions, options) => {
      // Simulate compression by reducing size and dimensions
      const newWidth = actions[0]?.resize?.width || 100;
      const newHeight = actions[0]?.resize?.height || 100;
      const newSize = mockOriginalSize * (options.compress || 1);
      return Promise.resolve({
        uri: `file://mock/compressed_${newSize}.jpg`,
        width: newWidth,
        height: newHeight,
      });
    });
    // Mock fetch for getFileSize
    global.fetch = jest.fn((uri) =>
      Promise.resolve({
        blob: () => Promise.resolve({ size: parseInt(uri.split('_')[1]) }),
      }) as any
    );
  });

  describe('compressImageClientSide', () => {
    it('should compress image to target size and dimensions', async () => {
      const result = await compressImageClientSide(mockImageUri, mockOriginalSize, {
        targetSizeKB: 50,
        maxDimension: 200,
        quality: 0.8,
      });

      expect(result.finalSizeKB).toBeCloseTo(800, -2); // Approx 800KB, as mock returns originalSize * quality
      expect(result.dimensions.width).toBeDefined();
      expect(result.dimensions.height).toBeDefined();
      expect(ImageManipulator.manipulateAsync).toHaveBeenCalled();
    });

    it('should handle different formats', async () => {
      await compressImageClientSide(mockImageUri, mockOriginalSize, { format: 'png' });
      expect(ImageManipulator.manipulateAsync).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Array),
        expect.objectContaining({ format: ImageManipulator.SaveFormat.PNG })
      );
    });

    it('should throw error on compression failure', async () => {
      (ImageManipulator.manipulateAsync as jest.Mock).mockRejectedValue(new Error('Compression failed'));
      await expect(compressImageClientSide(mockImageUri, mockOriginalSize)).rejects.toThrow('Failed to compress image');
    });
  });

  describe('compressImageUltraAggressiveClient', () => {
    it('should apply aggressive compression based on original size', async () => {
      const result = await compressImageUltraAggressiveClient(mockImageUri, 5 * 1024 * 1024); // 5MB
      expect(result.finalSizeKB).toBeCloseTo(2750, -2); // 5MB * 0.55
      expect(ImageManipulator.manipulateAsync).toHaveBeenCalled();
    });
  });

  describe('compressImageModerateClient', () => {
    it('should apply moderate compression', async () => {
      const result = await compressImageModerateClient(mockImageUri, mockOriginalSize);
      expect(result.finalSizeKB).toBeCloseTo(700, -2); // 1MB * 0.7
      expect(ImageManipulator.manipulateAsync).toHaveBeenCalled();
    });
  });

  describe('compressFileUltraAggressive', () => {
    it('should compress a File object', async () => {
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(mockFile, 'size', { value: mockOriginalSize });

      const result = await compressFileUltraAggressive(mockFile);

      expect(result.blob).toBeInstanceOf(Blob);
      expect(result.uri).toBeUndefined();
      expect(result.finalSizeKB).toBeCloseTo(3500, -2); // 1MB * 0.35 (default aggressive)
    });

    it('should throw error if compression result has no URI', async () => {
      (ImageManipulator.manipulateAsync as jest.Mock).mockResolvedValue({}); // No URI
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(mockFile, 'size', { value: mockOriginalSize });

      await expect(compressFileUltraAggressive(mockFile)).rejects.toThrow('Failed to compress file');
    });
  });
});