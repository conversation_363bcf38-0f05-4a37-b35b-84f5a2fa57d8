import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react-native';
import { DrawerProvider, useDrawer, Drawer, DrawerTrigger } from '../../../../src/components/shared/navigation/DrawerProvider';
import { View, Text } from 'react-native';

// Mock useColorScheme hook
jest.mock('@/src/hooks/useColorScheme', () => ({
  useColorScheme: () => 'light',
}));

describe('DrawerProvider and related components', () => {
  it('useDrawer throws error when not used within DrawerProvider', () => {
    const TestComponent = () => {
      useDrawer();
      return null;
    };
    expect(() => render(<TestComponent />)).toThrow(
      'useDrawer must be used within a DrawerProvider'
    );
  });

  it('DrawerProvider correctly provides context values', () => {
    const TestComponent = () => {
      const { isOpen, openDrawer, closeDrawer, toggleDrawer } = useDrawer();
      return (
        <View>
          <Text testID="is-open-text">{isOpen ? 'Open' : 'Closed'}</Text>
          <button testID="open-button" onPress={openDrawer} />
          <button testID="close-button" onPress={closeDrawer} />
          <button testID="toggle-button" onPress={toggleDrawer} />
        </View>
      );
    };

    render(
      <DrawerProvider>
        <TestComponent />
      </DrawerProvider>
    );

    const isOpenText = screen.getByTestId('is-open-text');
    const openButton = screen.getByTestId('open-button');
    const closeButton = screen.getByTestId('close-button');
    const toggleButton = screen.getByTestId('toggle-button');

    expect(isOpenText.props.children).toBe('Closed');

    fireEvent.press(openButton);
    expect(isOpenText.props.children).toBe('Open');

    fireEvent.press(closeButton);
    expect(isOpenText.props.children).toBe('Closed');

    fireEvent.press(toggleButton);
    expect(isOpenText.props.children).toBe('Open');

    fireEvent.press(toggleButton);
    expect(isOpenText.props.children).toBe('Closed');
  });

  it('Drawer component renders when open and closes on overlay press', () => {
    const { getByTestId, queryByTestId } = render(
      <DrawerProvider>
        <DrawerTrigger>
          <Text>Open Drawer</Text>
        </DrawerTrigger>
        <Drawer>
          <Text testID="drawer-content">Drawer Content</Text>
        </Drawer>
      </DrawerProvider>
    );

    // Initially closed
    expect(queryByTestId('drawer-content')).toBeNull();

    // Open drawer
    fireEvent.press(getByTestId('open-drawer-button')); // Assuming DrawerTrigger has this testID
    expect(getByTestId('drawer-content')).toBeTruthy();

    // Close by pressing overlay
    fireEvent.press(getByTestId('drawer-overlay'));
    expect(queryByTestId('drawer-content')).toBeNull();
  });

  it('DrawerTrigger toggles the drawer', () => {
    const { getByText, queryByTestId } = render(
      <DrawerProvider>
        <DrawerTrigger>
          <Text>Toggle Drawer</Text>
        </DrawerTrigger>
        <Drawer>
          <Text testID="drawer-content">Drawer Content</Text>
        </Drawer>
      </DrawerProvider>
    );

    // Open
    fireEvent.press(getByText('Toggle Drawer'));
    expect(getByTestId('drawer-content')).toBeTruthy();

    // Close
    fireEvent.press(getByText('Toggle Drawer'));
    expect(queryByTestId('drawer-content')).toBeNull();
  });
});
