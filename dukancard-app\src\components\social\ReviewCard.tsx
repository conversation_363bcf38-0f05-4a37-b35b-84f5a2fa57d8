/**
 * ReviewCard Component for React Native
 * Displays individual review with edit and delete functionality
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ReviewBusinessProfile } from '@/backend/supabase/services/posts/socialService';
import { useRouter } from 'expo-router';
import { useToast } from '@/src/components/ui/Toast';
import { useTheme } from '@/src/hooks/useTheme';
import { createReviewCardStyles } from '@/styles/components/social/review-card';

interface ReviewData {
  id: string;
  rating: number;
  review_text: string | null;
  created_at: string;
  updated_at: string;
  business_profile_id: string;
  user_id: string;
  business_profiles: ReviewBusinessProfile | null;
}

interface ReviewCardProps {
  review: ReviewData;
  onDelete: (reviewId: string) => Promise<void>;
  onUpdate: (reviewId: string, rating: number, reviewText: string) => Promise<void>;
}

export const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  onDelete,
  onUpdate,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editedRating, setEditedRating] = useState(review.rating);
  const [editedReviewText, setEditedReviewText] = useState(review.review_text || '');

  const business = review.business_profiles;
  const router = useRouter();
  const toast = useToast();
  const theme = useTheme();
  const styles = createReviewCardStyles(theme);

  if (!business) {
    return null;
  }

  const handleDelete = () => {
    Alert.alert(
      'Delete Review',
      'Are you sure you want to delete this review? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setIsDeleting(true);
            try {
              await onDelete(review.id);
            } catch (error) {
              console.error('Error deleting review:', error);
            } finally {
              setIsDeleting(false);
            }
          },
        },
      ]
    );
  };

  const handleEdit = () => {
    setIsEditing(true);
    setEditedRating(review.rating);
    setEditedReviewText(review.review_text || '');
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedRating(review.rating);
    setEditedReviewText(review.review_text || '');
  };

  const handleSaveEdit = async () => {
    if (editedRating < 1 || editedRating > 5) {
      Alert.alert('Invalid Rating', 'Please select a rating between 1 and 5 stars.');
      return;
    }

    setIsUpdating(true);
    try {
      await onUpdate(review.id, editedRating, editedReviewText);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating review:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleVisitBusiness = () => {
    try {
      if (!business?.business_slug) {
        toast.error("Navigation Error", "Business profile not available");
        return;
      }

      // Navigate to business profile using the slug
      router.push(`/business/${business.business_slug}`);
    } catch (error) {
      console.error("Error navigating to business profile:", error);
      toast.error("Navigation Error", "Failed to open business profile");
    }
  };

  const getBusinessInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderStars = (rating: number, onPress?: (rating: number) => void) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => onPress && onPress(star)}
            disabled={!onPress}
          >
            <Ionicons
              name={star <= rating ? 'star' : 'star-outline'}
              size={20}
              color={star <= rating ? '#FFD700' : '#ccc'}
              style={styles.star}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View style={styles.listItem}>
      {/* Business Header */}
      <TouchableOpacity
        style={styles.businessHeader}
        onPress={handleVisitBusiness}
        activeOpacity={0.7}
      >
        <View style={styles.logoContainer}>
          {business.logo_url ? (
            <Image source={{ uri: business.logo_url }} style={styles.logo} />
          ) : (
            <View style={styles.logoPlaceholder}>
              <Text style={styles.logoText}>
                {getBusinessInitials(business.business_name || 'B')}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.businessInfo}>
          <Text style={styles.businessName} numberOfLines={1}>
            {business.business_name}
          </Text>
          <Text style={styles.reviewDate}>
            Reviewed on {formatDate(review.created_at)}
            {review.updated_at !== review.created_at && ' (edited)'}
          </Text>
        </View>

        {/* Action Buttons - Right Corner */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.editButton}
            onPress={handleEdit}
            disabled={isDeleting || isUpdating}
          >
            <Ionicons name="create-outline" size={18} color="#007AFF" />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.deleteButton, isDeleting && styles.deleteButtonDisabled]}
            onPress={handleDelete}
            disabled={isDeleting || isUpdating}
          >
            {isDeleting ? (
              <ActivityIndicator size="small" color="#ff4444" />
            ) : (
              <Ionicons name="trash-outline" size={18} color="#ff4444" />
            )}
          </TouchableOpacity>
        </View>
      </TouchableOpacity>

      {/* Review Content */}
      <View style={styles.reviewContent}>
        {isEditing ? (
          <>
            {/* Edit Rating */}
            <View style={styles.editSection}>
              <Text style={styles.editLabel}>Rating:</Text>
              {renderStars(editedRating, setEditedRating)}
            </View>

            {/* Edit Review Text */}
            <View style={styles.editSection}>
              <Text style={styles.editLabel}>Review:</Text>
              <TextInput
                style={styles.editTextInput}
                value={editedReviewText}
                onChangeText={setEditedReviewText}
                placeholder="Write your review..."
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            {/* Edit Actions */}
            <View style={styles.editActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={handleCancelEdit}
                disabled={isUpdating}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.saveButton, isUpdating && styles.saveButtonDisabled]}
                onPress={handleSaveEdit}
                disabled={isUpdating}
              >
                {isUpdating ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <Text style={styles.saveButtonText}>Save</Text>
                )}
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <>
            {/* Display Rating */}
            {renderStars(review.rating)}

            {/* Display Review Text */}
            {review.review_text && (
              <Text style={styles.reviewText}>{review.review_text}</Text>
            )}
          </>
        )}
      </View>

      {/* Separator */}
      <View style={styles.separator} />
    </View>
  );
};
