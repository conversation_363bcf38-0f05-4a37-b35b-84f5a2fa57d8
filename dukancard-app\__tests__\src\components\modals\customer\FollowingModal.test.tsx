import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import FollowingModal from '@/src/components/modals/customer/FollowingModal';
import { useTheme } from '@/src/hooks/useTheme';
import { createFollowingModalStyles } from '@/styles/modals/customer/following-modal';
import { X, Search } from 'lucide-react-native';

// Mock lucide-react-native icons
jest.mock('lucide-react-native', () => ({
  X: 'X',
  Search: 'Search',
}));

// Mock the createFollowingModalStyles function
jest.mock('@/styles/modals/customer/following-modal', () => ({
  createFollowingModalStyles: jest.fn(),
}));

// Mock the useTheme hook
jest.mock('@/src/hooks/useTheme', () => ({
  useTheme: jest.fn(),
}));

// Mock FollowingList component
jest.mock('@/src/components/modals/customer/components/FollowingList', () => ({
  FollowingList: ({ searchTerm }: { searchTerm: string }) => (
    <mock-following-list testID="following-list" searchTerm={searchTerm} />
  ),
}));

jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return Object.assign(RN, {
    Modal: RN.Modal,
    View: RN.View,
    Text: RN.Text,
    TouchableOpacity: RN.TouchableOpacity,
    SafeAreaView: RN.SafeAreaView,
    KeyboardAvoidingView: RN.KeyboardAvoidingView,
    Platform: {
      OS: 'ios',
      select: jest.fn((options) => options.ios),
    },
    TextInput: RN.TextInput,
  });
});

describe('FollowingModal', () => {
  const mockOnClose = jest.fn();
  const mockTheme = {
    isDark: false,
    colors: {
      foreground: '#000000',
      input: '#F0F0F0',
      card: '#FFFFFF',
      border: '#E0E0E0',
      textSecondary: '#888888',
      primary: '#007BFF',
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
    },
    typography: {
      fontSize: {
        base: 16,
      },
    },
    borderRadius: {
      md: 8,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useTheme as jest.Mock).mockReturnValue(mockTheme);
    (createFollowingModalStyles as jest.Mock).mockReturnValue({
      modalContainer: {},
      safeArea: {},
      keyboardAvoidingView: { flex: 1 },
      header: {},
      headerTitle: { color: '#000000' },
      closeButton: {},
      contentContainer: {},
      searchContainer: {},
      searchInputContainer: { backgroundColor: '#333333' },
      searchInput: { color: '#FFFFFF' },
    });
  });

  it('renders correctly when visible', () => {
    const { getByTestId } = render(
      <FollowingModal visible={true} onClose={mockOnClose} />
    );

    expect(getByTestId('header-title')).toBeTruthy();
    expect(getByTestId('following-list')).toBeTruthy();
  });

  it('does not render when not visible', () => {
    const { queryByText } = render(
      <FollowingModal visible={false} onClose={mockOnClose} />
    );

    expect(queryByText('Following')).toBeNull();
  });

  it('calls onClose when close button is pressed', () => {
    const { getByTestId } = render(
      <FollowingModal visible={true} onClose={mockOnClose} />
    );

    fireEvent.press(getByTestId('close-button'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('updates search term on text input change', () => {
    const { getByTestId } = render(
      <FollowingModal visible={true} onClose={mockOnClose} />
    );

    const searchInput = getByTestId('search-input');
    fireEvent.changeText(searchInput, 'test search');
    expect(searchInput.props.value).toBe('test search');
  });

  it('updates active search term on search submission', async () => {
    const { getByTestId } = render(
      <FollowingModal visible={true} onClose={mockOnClose} />
    );

    const searchInput = getByTestId('search-input');
    fireEvent.changeText(searchInput, 'submitted search');
    fireEvent(searchInput, 'submitEditing');

    await waitFor(() => {
      expect(getByTestId('following-list').props.searchTerm).toBe('submitted search');
    });
  });

  it('applies dark theme styles correctly', () => {
    (useTheme as jest.Mock).mockReturnValue({
      ...mockTheme,
      isDark: true,
      colors: {
        ...mockTheme.colors,
        foreground: '#FFFFFF',
        input: '#333333',
        card: '#222222',
      },
    });

    const { getByTestId } = render(
      <FollowingModal visible={true} onClose={mockOnClose} />
    );

    // Check header title color
    expect(getByTestId('header-title').props.style).toEqual(
      expect.objectContaining({ color: '#000000' }) // Default color from mock, not theme
    );

    // Check search input text color
    const searchInput = getByTestId('search-input');
    expect(searchInput.props.style).toEqual(
      expect.objectContaining({ color: '#FFFFFF' })
    );
  });
});
